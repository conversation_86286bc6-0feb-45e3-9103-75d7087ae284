package com.ebupt.cmi.clientmanagement.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ebupt.cmi.clientmanagement.domain.dto.GetMarketingDetailDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.MktCampaignCorpDto;
import com.ebupt.cmi.clientmanagement.domain.entity.Channel;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsChannelMarketBillflow;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsChannelMarketBillflowA2z;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsChannelMarketingRebate;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.GetMarketingDeatilDTO;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.feign.mkt.MktFeignClient;
import com.ebupt.cmi.clientmanagement.feign.mkt.domain.MarketingCampaignDTO;
import com.ebupt.cmi.clientmanagement.feign.mkt.domain.MktCampaignCorpDTO;
import com.ebupt.cmi.clientmanagement.feign.mkt.domain.SelectMarketingCampaignDTO;
import com.ebupt.cmi.clientmanagement.mapper.ChannelMapper;
import com.ebupt.cmi.clientmanagement.mapper.CmsChannelMarketBillFlowMapper;
import com.ebupt.cmi.clientmanagement.mapper.CmsChannelMarketBillflowA2zMapper;
import com.ebupt.cmi.clientmanagement.mapper.CmsChannelMarketingRebateMapper;
import com.ebupt.cmi.clientmanagement.service.CmsChannelMarketingRebateIService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CmsChannelMarketingRebateService extends ServiceImpl<CmsChannelMarketingRebateMapper, CmsChannelMarketingRebate> implements CmsChannelMarketingRebateIService {

    @Autowired
    private CmsChannelMarketingRebateMapper cmsChannelMarketingRebateMapper;

    @Autowired
    private MktFeignClient mktFeignClient;

    @Autowired
    private CmsChannelMarketBillFlowMapper cmsChannelMarketBillFlowMapper;

    @Autowired
    private CmsChannelMarketBillflowA2zMapper cmsChannelMarketBillflowA2zMapper;

    @Autowired
    private ChannelMapper cchannelMapper;

    @Override
    public List<CmsChannelMarketingRebate> getChannelByMarketingId(Long marketingId) {
        return cmsChannelMarketingRebateMapper.selectList(new QueryWrapper<CmsChannelMarketingRebate>().lambda().eq(CmsChannelMarketingRebate::getActivityId, marketingId));
    }

    @Override
    public IPage<GetMarketingDeatilDTO> getMarketingDeatil(GetMarketingDetailDTO getMarketingDetailDTO) {
//     7.4.	单独资源合作模式，可查看营销活动及营销款使用情况（同A2Z）
        if ("3".equals(getMarketingDetailDTO.getCooperationMode())) {
            getMarketingDetailDTO.setCooperationMode("2");
        }

        String cooperationMode = getMarketingDetailDTO.getCooperationMode();
        String corpId = getMarketingDetailDTO.getCorpId();

        String type;

        if ("1".equals(cooperationMode)){
            type = "2";
        }else {
            type = "1";
        }
        SelectMarketingCampaignDTO selectDTO = new SelectMarketingCampaignDTO();
        selectDTO.setSize(getMarketingDetailDTO.getPageSize());
        selectDTO.setCurrent(getMarketingDetailDTO.getPageNum());
        selectDTO.setCorpIds(Arrays.asList(corpId));
        selectDTO.setCooperationMode(cooperationMode);
        List<MktCampaignCorpDto> mktCampaignCorpDTOIPage = mktFeignClient.getMarketingCampaignCorpList(selectDTO).ignoreThrow();

        if(Objects.isNull(mktCampaignCorpDTOIPage)){
            throw new BizException("Activity information is empty");
        }

        IPage<GetMarketingDeatilDTO> resultPage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(getMarketingDetailDTO.getPageNum(), getMarketingDetailDTO.getPageSize());

        if(CollectionUtil.isEmpty(mktCampaignCorpDTOIPage)){
            resultPage.setRecords(new ArrayList<>());
            resultPage.setTotal(0);
            resultPage.setCurrent(getMarketingDetailDTO.getPageNum());
            resultPage.setSize(getMarketingDetailDTO.getPageSize());
            return resultPage;
        }

        List<CmsChannelMarketingRebate> pageInfo = cmsChannelMarketingRebateMapper.selectList(new QueryWrapper<CmsChannelMarketingRebate>().lambda()
                .eq(CmsChannelMarketingRebate::getCorpId, corpId)
                .eq(CmsChannelMarketingRebate::getType, type));

        Map<Long, List<CmsChannelMarketingRebate>> channelMarketingRebateMap=new HashMap<>();
        if (!pageInfo.isEmpty()){
            channelMarketingRebateMap= pageInfo.stream().collect(Collectors.groupingBy(CmsChannelMarketingRebate::getActivityId));
        }

        List<GetMarketingDeatilDTO> getMarketingDeatilDTOS = new ArrayList<>();

        List<Long> activityIds = pageInfo.stream().map(CmsChannelMarketingRebate::getActivityId).collect(Collectors.toList());

        SelectMarketingCampaignDTO selectMarketingCampaignDTO = new SelectMarketingCampaignDTO();
        selectMarketingCampaignDTO.setMarketingCampaignIds(activityIds);
        List<MarketingCampaignDTO> marketingCampaigns = Response.getAndCheckRemoteData(mktFeignClient.getMarketingCampaign(selectMarketingCampaignDTO));

        Map<Long, MarketingCampaignDTO> marketingCampaignMap = marketingCampaigns.stream().collect(Collectors.toMap(MarketingCampaignDTO::getId, marketingCampaignDTO -> marketingCampaignDTO));

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy年MM月dd日");

        Map<Long, List<CmsChannelMarketingRebate>> map=channelMarketingRebateMap;

        mktCampaignCorpDTOIPage.forEach(mktCampaignCorpDTO -> {
            List<CmsChannelMarketingRebate> cmsChannelMarketingRebates = map.get(mktCampaignCorpDTO.getMcId());
            if (!CollectionUtil.isEmpty(cmsChannelMarketingRebates)){

                cmsChannelMarketingRebates.forEach(cmsChannelMarketingRebate -> {

                    BigDecimal rebateAmount = cmsChannelMarketingRebate.getRebateAmount().divide(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
                    BigDecimal remainAmount = cmsChannelMarketingRebate.getRemainAmount().divide(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);

                    BigDecimal usedAmount = rebateAmount.subtract(remainAmount).setScale(2, BigDecimal.ROUND_HALF_UP);

                    getMarketingDeatilDTOS.add(GetMarketingDeatilDTO.builder()
                            .name(marketingCampaignMap.get(cmsChannelMarketingRebate.getActivityId()).getCampaignName())
                            .activityId(cmsChannelMarketingRebate.getActivityId())
                            .beginTime(formatter.format(marketingCampaignMap.get(cmsChannelMarketingRebate.getActivityId()).getStartTime()))
                            .endTime(formatter.format(marketingCampaignMap.get(cmsChannelMarketingRebate.getActivityId()).getEndTime()))
                            .status(marketingCampaignMap.get(cmsChannelMarketingRebate.getActivityId()).getCampaignStatus())
                            .rebateAmount(rebateAmount)
                            .remainAmount(remainAmount)
                            .usedAmount(usedAmount)
                            .createTime(formatter.format(cmsChannelMarketingRebate.getCreateTime()))
                            .expireTime(formatter.format(cmsChannelMarketingRebate.getExpiryTime()))
                            .effectiveTime(cmsChannelMarketingRebate.getEffectiveTime() != null ?formatter.format(cmsChannelMarketingRebate.getEffectiveTime()):null)
                            .build());

                });
            }else {
                getMarketingDeatilDTOS.add(GetMarketingDeatilDTO.builder()
                        .name(mktCampaignCorpDTO.getCampaignName())
                        .activityId(mktCampaignCorpDTO.getMcId())
                        .beginTime(formatter.format(mktCampaignCorpDTO.getStartTime()))
                        .endTime(formatter.format(mktCampaignCorpDTO.getEndTime()))
                        .status(mktCampaignCorpDTO.getCampaignStatus())
                        .build());
            }

        });
        int num = getMarketingDetailDTO.getPageNum();
        int pageSize = getMarketingDetailDTO.getPageSize();


        resultPage.setRecords(getMarketingDeatilDTOS.stream().skip((long) (num-1) * pageSize).limit(pageSize).collect(Collectors.toList()));
        resultPage.setTotal(getMarketingDeatilDTOS.size());
        resultPage.setCurrent(num);
        resultPage.setSize(pageSize);

        return resultPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetMarketingAmount() {
        //查询过期的营销账户返利数据
        List<CmsChannelMarketingRebate> cmsChannelMarketingRebates = cmsChannelMarketingRebateMapper.selectList(new QueryWrapper<CmsChannelMarketingRebate>().lambda()
                .ne(CmsChannelMarketingRebate::getRemainAmount, BigDecimal.ZERO)
                .le(CmsChannelMarketingRebate::getExpiryTime, new Date()));
        if (!cmsChannelMarketingRebates.isEmpty()){
            //更新营销账户返利数据
            cmsChannelMarketingRebates.forEach(cmsChannelMarketingRebate -> {
                int update = cmsChannelMarketingRebateMapper.update(null,
                        new LambdaUpdateWrapper<CmsChannelMarketingRebate>()
                                .eq(CmsChannelMarketingRebate::getId, cmsChannelMarketingRebate.getId())
//                                .eq(CmsChannelMarketingRebate::getVersion, cmsChannelMarketingRebate.getVersion())
//                                .set(CmsChannelMarketingRebate::getVersion, cmsChannelMarketingRebate.getVersion() + 1)
                                .setSql("remain_amount = " + BigDecimal.ZERO));
                BigDecimal remainAmount = cmsChannelMarketingRebate.getRemainAmount();
                if (update <= 0){
                    throw new BizException("营销账户过期清零失败");
                }
                //查询该营销账户下其他活动的数据
                List<CmsChannelMarketingRebate> channelMarketingRebates = cmsChannelMarketingRebateMapper.selectList(new QueryWrapper<CmsChannelMarketingRebate>().lambda()
                        .eq(CmsChannelMarketingRebate::getCorpId, cmsChannelMarketingRebate.getCorpId())
                        .eq(CmsChannelMarketingRebate::getType, cmsChannelMarketingRebate.getType())
                        .ne(CmsChannelMarketingRebate::getActivityId, cmsChannelMarketingRebate.getActivityId()));
                BigDecimal totalAmount = channelMarketingRebates.stream().map(CmsChannelMarketingRebate::getRemainAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                //查询该渠道商的币种信息
                Channel channel = cchannelMapper.selectOne(new LambdaQueryWrapper<Channel>().eq(Channel::getCorpId, cmsChannelMarketingRebate.getCorpId()));
                //A2Z模式-清除过期的营销账户返利流水数据
                if (cmsChannelMarketingRebate.getType().equals("1")){
                    cmsChannelMarketBillflowA2zMapper.insert(CmsChannelMarketBillflowA2z.builder()
                            .corpId(cmsChannelMarketingRebate.getCorpId())
                            .currencyCode(channel.getCurrencyCode())
                            .type("3")
                            .amount(cmsChannelMarketingRebate.getRemainAmount())
                            .deposit(BigDecimal.ZERO)
                            .activityId(cmsChannelMarketingRebate.getActivityId())
                            .createTime(new Date())
                            .totalAmount(totalAmount)
                            .usedFlow(BigDecimal.ZERO)
                            .build());
                }
                //代销模式-清除过期的营销账户返利流水数据
                if (cmsChannelMarketingRebate.getType().equals("2")){
                    //先查询当前渠道当前活动是否已存在退订流水
                    CmsChannelMarketBillflow cmsChannelMarketBillflow = cmsChannelMarketBillFlowMapper
                            .selectOne(new QueryWrapper<CmsChannelMarketBillflow>().lambda()
                                    .eq(CmsChannelMarketBillflow::getCorpId, cmsChannelMarketingRebate.getCorpId())
                                    .eq(CmsChannelMarketBillflow::getActivityId, cmsChannelMarketingRebate.getActivityId())
                                    .eq(CmsChannelMarketBillflow::getType, "6"));
                    if (cmsChannelMarketBillflow == null){
                        //查找同一活动同一渠道商的其他营销账户数据
                        List<CmsChannelMarketingRebate> channelMarketingRebates1 = cmsChannelMarketingRebates.stream()
                                .filter(cmsChannelMarketingRebate1 -> cmsChannelMarketingRebate1.getCorpId().equals(cmsChannelMarketingRebate.getCorpId())
                                        && cmsChannelMarketingRebate1.getActivityId().equals(cmsChannelMarketingRebate.getActivityId()))
                                .collect(Collectors.toList());
                        if (channelMarketingRebates1.size() > 1){
                            //同一活动同一渠道商存在多条营销账户数据
                            remainAmount = channelMarketingRebates1.stream().map(CmsChannelMarketingRebate::getRemainAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        cmsChannelMarketBillFlowMapper.insert(CmsChannelMarketBillflow.builder()
                                .corpId(cmsChannelMarketingRebate.getCorpId())
                                .currencyCode(channel.getCurrencyCode())
                                .type("6")
                                .amount(remainAmount)
                                .deposit(BigDecimal.ZERO)
                                .activityId(cmsChannelMarketingRebate.getActivityId())
                                .createTime(new Date())
                                .totalAmount(totalAmount)
                                .totalOrderId("")
                                .build());
                    }
                }
            });
        }
    }
}
