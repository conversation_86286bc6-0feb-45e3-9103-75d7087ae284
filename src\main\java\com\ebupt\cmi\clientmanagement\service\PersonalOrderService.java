package com.ebupt.cmi.clientmanagement.service;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebupt.cmi.clientmanagement.annotion.SdcMultipleQueues;
import com.ebupt.cmi.clientmanagement.config.NoticeConfigProperties;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.enums.QueueEnum;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.vo.ReachingTreatmentVO;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.vo.SingleDayDelayVO;
import com.ebupt.cmi.clientmanagement.consumer.uitils.LuWarpper;
import com.ebupt.cmi.clientmanagement.consumer.uitils.SendMessageWrapper;
import com.ebupt.cmi.clientmanagement.consumer.vo.MockLuVO;
import com.ebupt.cmi.clientmanagement.domain.dto.HimsiStatusAndLocationDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.*;
import com.ebupt.cmi.clientmanagement.domain.entity.redis.RedissonLock;
import com.ebupt.cmi.clientmanagement.domain.enums.*;
import com.ebupt.cmi.clientmanagement.domain.enums.lu.CardTypeEnum;
import com.ebupt.cmi.clientmanagement.domain.enums.lu.OrderTypeEnum;
import com.ebupt.cmi.clientmanagement.domain.enums.lu.PackageStatusEnum;
import com.ebupt.cmi.clientmanagement.domain.enums.lu.UpccSignIdEnum;
import com.ebupt.cmi.clientmanagement.domain.req.AuditOrderReq;
import com.ebupt.cmi.clientmanagement.domain.req.ChannelUnsubscribeReq;
import com.ebupt.cmi.clientmanagement.domain.req.SearchOrderDetailReq;
import com.ebupt.cmi.clientmanagement.domain.response.ApiResponseEnum;
import com.ebupt.cmi.clientmanagement.domain.response.PageResult;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.response.ResponseResult;
import com.ebupt.cmi.clientmanagement.domain.vo.*;
import com.ebupt.cmi.clientmanagement.domain.vo.channel.ChannelOrderDetailVO;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.feign.back.BackFeignClient;
import com.ebupt.cmi.clientmanagement.feign.control.ControlFeignClient;
import com.ebupt.cmi.clientmanagement.feign.external.req.PriceInfo;
import com.ebupt.cmi.clientmanagement.feign.external.req.UnsubscribeNotifyReq;
import com.ebupt.cmi.clientmanagement.feign.mkt.MktFeignClient;
import com.ebupt.cmi.clientmanagement.feign.mkt.domain.MarketingCampaignDTO;
import com.ebupt.cmi.clientmanagement.feign.pms.PmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.HcardInfo;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.PackageVO;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.UpdateOpenStatusReq;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.VcardInfo;
import com.ebupt.cmi.clientmanagement.feign.pms.domainV2.CardVO;
import com.ebupt.cmi.clientmanagement.mapper.*;
import com.ebupt.cmi.clientmanagement.service.ccrcommon.CCRCommonService;
import com.ebupt.cmi.clientmanagement.service.channel.ChannelDistributorsService;
import com.ebupt.cmi.clientmanagement.service.impl.FlowPoolServiceImpl;
import com.ebupt.cmi.clientmanagement.service.recorddetail.context.SingleCyclePackageDeductContext;
import com.ebupt.cmi.clientmanagement.service.recorddetail.context.SingleDayPackageDeductContext;
import com.ebupt.cmi.clientmanagement.utils.*;
import groovy.lang.Lazy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import javax.swing.*;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static org.assertj.core.util.DateUtil.now;

/**
 * 个人订单相关逻辑
 *
 * <AUTHOR>
 * @date 2021-6-15 15:58:13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PersonalOrderService {

    private final ChannelMapper channelMapper;

    private final CmsBigOrderMapper cmsBigOrderMapper;
    private final ChannelCardMapper channelCardMapper;
    private final ChannelSurfMapper channelSurfMapper;
    private final ChannelOrderMapper channelOrderMapper;
    private final ChannelOrderDetailMapper channelOrderDetailMapper;
    private final ChannelDistributorDetailMapper distributorDetailMapper;
    private final ChannelPackageCardMapper channelPackageCardMapper;
    private final EopAccessDetailMapper eopAccessDetailMapper;

    private final FlowOperationUtils flowOperationUtils;

    private final PmsFeignClient pmsFeignClient;

    private final SmsService smsService;
    private final PackageService packageService;
    private final PackageOverdueService packageOverdueService;
    private final UnsubscribeNotifyService unsubscribeNotifyService;
    private final NoticeConfigProperties noticeConfigProperties;

    @Resource
    private Executor luExecutor;

    private final RedissonLock redissonLock;
    private final RedisTemplate<String, Object> redisTemplate;

    private final RedisUtil<Long> redisUtil;

    private final ChannelSurfInfoMapper channelSurfInfoMapper;

    private final PackageEndService packageEndService;

    private final CCRCommonService ccrCommonService;

    private final FlowPoolServiceImpl flowPoolServiceImpl;

    private final ControlFeignClient controlFeignClient;

    private final CmsTopchannelMapper cmsTopchannelMapper;

    private final CmsChannelRelationMapper cmsChannelRelationMapper;

    private final PackageDirectionRelationMapper packageDirectionRelationMapper;


    @Lazy
    @Resource
    private LuWarpper luWarpper;
    /**
     * 统一丢队方法
     */
    private final SendMessageWrapper sendMessageWrapper;

    private final ChannelService channelService;

    private final ChannelBindMapper channelBindMapper;

    private final ChannelDistributorsService channelDistributorsService;

    private final CmsPackageDelayRecordInfoMapper cmsPackageDelayRecordInfoMapper;

    private final CmsPackageCardUpccRelationMapper cmsPackageCardUpccRelationMapper;

    private final BackFeignClient backFeignClient;

    private final CmsChannelMarketBillFlowMapper cmsChannelMarketBillFlowMapper;

    private final CmsChannelMarketingRebateMapper cmsChannelMarketingRebateMapper;

    @Resource
    private CmsPackageDayUsedMapper cmsPackageDayUsedMapper;

    @Resource
    private CmsPackageDayRemainMapper cmsPackageDayRemainMapper;

    @Resource
    private CmsPackageCycleRemainMapper cmsPackageCycleRemainMapper;

    @Value("${delayTime.twentyFourHours}")
    private Long twentyFourHours;

    @Value("${delayTime.singleDayDelay:-1}")
    private Long singleDayDelay;

    /**
     * 单日流量剩余key
     */
    private final static String packageDayRemain = "package_add_day_";

    /**
     * 周期内套餐剩余流量key
     */

    private final static String packageRemain = "package_add_remain_";

    @Autowired
    @Qualifier("taskExecutor")
    Executor executor;
    @Autowired
    private MktFeignClient mktFeignClient;

    /**
     * 根据订单id分页查询子订单
     *
     * @param searchOrderDetailReq
     * @return
     */
    public PageResult<ChannelOrderDetailVO> getOrderDetailPage(SearchOrderDetailReq searchOrderDetailReq) {
        final LambdaQueryWrapper<ChannelOrderDetail> queryWrapper = Wrappers.lambdaQuery(ChannelOrderDetail.class)
                .select(ChannelOrderDetail::getId, ChannelOrderDetail::getIccid, ChannelOrderDetail::getAmount,
                        ChannelOrderDetail::getCurrencyCode, ChannelOrderDetail::getOrderStatus,
                        ChannelOrderDetail::getNameEn, ChannelOrderDetail::getAddress,
                        ChannelOrderDetail::getLogisticCompany, ChannelOrderDetail::getPostCode,
                        ChannelOrderDetail::getCardForm, ChannelOrderDetail::getLogistic, ChannelOrderDetail::getOrderType)
                .eq(ChannelOrderDetail::getOrderId, Long.valueOf(searchOrderDetailReq.getOrderId()))
                .eq(StringUtils.isNotBlank(searchOrderDetailReq.getIccid()), ChannelOrderDetail::getIccid, searchOrderDetailReq.getIccid())
                .eq(StringUtils.isNotBlank(searchOrderDetailReq.getStatus()), ChannelOrderDetail::getOrderStatus, searchOrderDetailReq.getStatus())
                .isNotNull(searchOrderDetailReq.isOnlyIccid(), ChannelOrderDetail::getIccid)
                .ne(searchOrderDetailReq.isOnlyIccid(), ChannelOrderDetail::getIccid, "");
        Integer pageNumber = searchOrderDetailReq.getPageNumber();
        Integer pageSize = searchOrderDetailReq.getPageSize();
        Page<ChannelOrderDetail> page = new Page<>(pageNumber, pageSize);
        final Page<ChannelOrderDetail> detailPage = channelOrderDetailMapper.selectPage(page, queryWrapper);
        final List<ChannelOrderDetailVO> detailVOList = detailPage.getRecords()
                .stream()
                .map(ChannelOrderDetailVO::convert)
                .collect(Collectors.toList());
        //脱敏
        Boolean hasPrivilege = backFeignClient.getPrivilege(searchOrderDetailReq.getUsername(), "1").get();
        if (Boolean.FALSE.equals(hasPrivilege)) {
            detailVOList.forEach(vo -> {
                if (!org.springframework.util.StringUtils.isEmpty(vo.getAddress())) {
                    vo.setAddress(DesensitizationUtils.hide(vo.getAddress(), 3));
                }
            });
        }

        return PageResult.ofPage(detailVOList, pageNumber, pageSize, detailPage.getTotal());
    }

    /**
     * 子订单退订
     *
     * @param id 子订单id
     */
    @Transactional(rollbackFor = Exception.class)
    public void unsubscribeOrderDetail(Long id) {
        final ChannelOrderDetail orderDetail = channelOrderDetailMapper.selectById(id);
        if (orderDetail == null) {
            throw new BizException("订单不存在");
        }
        Long orderId = orderDetail.getOrderId();
        ChannelOrder order = channelOrderMapper.selectById(orderId);
        if (order == null) {
            log.debug("总订单不存在 orderId: {}", orderId);
            throw new BizException("退订失败");
        }
        try {
            if (!redissonLock.tryLock(orderId.toString())) {
                throw new BizException("订单正在处理中，请稍后重试");
            }
            if (cmsBigOrderMapper.selectOne(Wrappers.lambdaQuery(CmsBigOrder.class)
                    .eq(CmsBigOrder::getOrderUniqueId, order.getOrderUniqueId())) != null) {
                if (!ChannelOrder.OrderStatusEnum.NOT_DELIVERY.getValue().equals(order.getOrderStatus())
                        && !ChannelOrder.OrderStatusEnum.FINISHED.getValue().equals(order.getOrderStatus())
                        && !ChannelOrder.OrderStatusEnum.PARTIAL_DELIVERED.getValue().equals(order.getOrderStatus())
                        && !ChannelOrder.OrderStatusEnum.UNSUBSCRIBE_NOT_AUDIT.getValue().equals(order.getOrderStatus())
                        && !ChannelOrder.OrderStatusEnum.COMBINATION.getValue().equals(order.getOrderStatus())
                        && !ChannelOrder.OrderStatusEnum.PARTIAL_UNSUBSCRIBE.getValue().equals(order.getOrderStatus())) {
                    throw new BizException("大单此状态不能进行退订");
                }
            }
            final String orderType = orderDetail.getOrderType();
            log.debug("当前订单orderType: {}", orderType);
            final List<String> orderTypes = Arrays.asList(OrderTypeEnum.CARD_PACKAGE.getOrderType(),
                    OrderTypeEnum.PACKAGE.getOrderType());
            if (!orderTypes.contains(orderType)) {
                throw new BizException("当前订单类型不允许退订");
            }

            String orderStatus = orderDetail.getOrderStatus();
            final String packageUniqueId = orderDetail.getPackageUniqueId();
            final String orderUserId = order.getOrderUserId();
            final Date now = new Date();
            if (OrderTypeEnum.PACKAGE.getOrderType().equals(orderType)) {
                log.debug("当前订单类型为购买套餐");
                if (!ChannelOrder.OrderStatusEnum.FINISHED.getValue().equals(orderStatus)) {
                    log.debug("当前订单状态不为已完成，不允许退订 orderStatus: {}", orderStatus);
                    throw new BizException("当前订单状态不允许退订");
                }
                ChannelDistributorDetail distributorDetail = getChannelDistributorDetail(orderUserId,
                        orderDetail.getCurrencyCode());
                final ChannelPackageCard packageCard = channelPackageCardMapper.selectOne(Wrappers.lambdaQuery(ChannelPackageCard.class)
                        .select(ChannelPackageCard::getPackageStatus, ChannelPackageCard::getEffectiveDay)
                        .eq(ChannelPackageCard::getPackageUniqueId, packageUniqueId));
                String packageStatus = packageCard.getPackageStatus();
                if (PackageStatusEnum.UNACTIVATED.getStatus().equals(packageStatus)
                        && now.before(packageCard.getEffectiveDay())) {
                    unsubscribeInactivedOrderDetail(orderDetail, order, packageUniqueId, distributorDetail);
                    return;
                }
                unsubscribeOrderDetailUnAudit(orderId, id);
                return;
            }
            log.debug("当前订单类型为购买卡+套餐");
            List<String> orderStatusList = Arrays.asList(ChannelOrder.OrderStatusEnum.NOT_DELIVERY.getValue(),
                    ChannelOrder.OrderStatusEnum.FINISHED.getValue());
            if (!orderStatusList.contains(orderStatus)) {
                throw new BizException("当前订单状态不允许退订");
            }
            ChannelDistributorDetail distributorDetail = getChannelDistributorDetail(orderUserId,
                    orderDetail.getCurrencyCode());
            if (ChannelOrder.OrderStatusEnum.NOT_DELIVERY.getValue().equals(orderStatus)) {
                log.debug("当前订单状态为待发货");
                refundOrderDetail(order, orderDetail, distributorDetail);
                unsubscribeOrderDetail(order, orderDetail, PackageStatusEnum.NOT_DELIVERED);
            } else {
                log.debug("当前订单状态为已完成");
                ChannelPackageCard packageCard = channelPackageCardMapper.selectOne(Wrappers.lambdaQuery(ChannelPackageCard.class)
                        .select(ChannelPackageCard::getPackageStatus, ChannelPackageCard::getEffectiveDay)
                        .eq(ChannelPackageCard::getPackageUniqueId, packageUniqueId));
                String packageStatus = packageCard.getPackageStatus();
                if (PackageStatusEnum.UNACTIVATED.getStatus().equals(packageStatus)
                        && now.before(packageCard.getEffectiveDay())) {
                    unsubscribeInactivedOrderDetail(orderDetail, order, packageUniqueId, distributorDetail);
                    return;
                }
                unsubscribeOrderDetailUnAudit(orderId, id);
            }
        } finally {
            if (redissonLock.isHeldByCurrentThread(orderId.toString())) {
                redissonLock.unlock(orderId.toString());
            }
        }
    }

    /**
     * 子订单待激活套餐退订流程
     *
     * @param orderDetail
     * @param order
     * @param packageUniqueId
     * @param distributorDetail
     */
    private void unsubscribeInactivedOrderDetail(ChannelOrderDetail orderDetail, ChannelOrder order,
                                                 String packageUniqueId, ChannelDistributorDetail distributorDetail) {
        log.debug("子订单待激活套餐退订流程");
        final boolean lock = redissonLock.tryLock(packageUniqueId);
        if (!lock) {
            log.warn("获取分布式锁失败 packageUniqueId: {}", packageUniqueId);
            throw new BizException("退订失败");
        }
        try {
            refundOrderDetail(order, orderDetail, distributorDetail);
            channelPackageCardMapper.delete(Wrappers.lambdaQuery(ChannelPackageCard.class)
                    .eq(ChannelPackageCard::getPackageUniqueId, packageUniqueId));
            packageDirectionRelationMapper.delete(Wrappers.lambdaQuery(PackageDirectionRelation.class)
                    .eq(PackageDirectionRelation::getPackageUniqueId, packageUniqueId));
            unsubscribeOrderDetail(order, orderDetail, PackageStatusEnum.UNACTIVATED);
        } finally {
            if (redissonLock.isHeldByCurrentThread(packageUniqueId)) {
                redissonLock.unlock(packageUniqueId);
            }
        }
    }

    /**
     * 封一层解决事务问题
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void unsubscribeOrder(Long id) {
        try {
            //订单锁锁并发，套餐锁锁业务。
            boolean lock = redissonLock.tryLock(id.toString());
            if (!lock) {
                throw new BizException("订单正在处理中，请稍后重试");
            }
            unsubscribeOrder1(id, NotifyTypeEnum.NON_ASYNC_NOTIFY.getType());
        } finally {
            if (redissonLock.isHeldByCurrentThread(id.toString())) {
                redissonLock.unlock(id.toString());
            }
        }
    }


    /**
     * 总订单退订
     *
     * @param id 订单id
     */
    @Transactional(rollbackFor = Exception.class)
    public void unsubscribeOrder1(Long id, String asyncNotifyType) {
        final ChannelOrder order = channelOrderMapper.selectById(id);
        if (order == null) {
            throw new BizException("订单不存在");
        }
        final String orderType = order.getOrderType();
        log.debug("当前订单orderType: {}", orderType);
        final List<String> orderTypes = Arrays.asList(OrderTypeEnum.CARD_PACKAGE.getOrderType(),
                OrderTypeEnum.PACKAGE.getOrderType(), OrderTypeEnum.REFUEL_PACKAGE.getOrderType());
        if (!orderTypes.contains(orderType)) {
            throw new BizException("当前订单类型不允许退订");
        }
        final String orderStatus = order.getOrderStatus();
        if (OrderTypeEnum.PACKAGE.getOrderType().equals(orderType)) {
            // 购买套餐，只有已完成的支持退订
            log.debug("当前订单类型为购买套餐 id: {}", id);
            if (!ChannelOrder.OrderStatusEnum.FINISHED.getValue().equals(orderStatus)) {
                log.debug("orderStatus: {}", orderStatus);
                throw new BizException("当前订单状态不允许退订");
            }
            final Integer unfinishedCount = channelOrderDetailMapper.selectCount(Wrappers.lambdaQuery(ChannelOrderDetail.class)
                    .eq(ChannelOrderDetail::getOrderId, id)
                    .ne(ChannelOrderDetail::getOrderStatus, ChannelOrder.OrderStatusEnum.FINISHED.getValue()));
            if (unfinishedCount > 0) {
                throw new BizException("不允许全部退订，请进入详情页操作");
            }
            final List<String> packageUniqueIds = getPackageUniqueIdsByOrderId(id);
            final List<String> packageStatusList = channelPackageCardMapper.selectList(Wrappers.lambdaQuery(ChannelPackageCard.class)
                            .select(ChannelPackageCard::getPackageStatus)
                            .in(ChannelPackageCard::getPackageUniqueId, packageUniqueIds)
                            .groupBy(ChannelPackageCard::getPackageStatus))
                    .stream()
                    .map(ChannelPackageCard::getPackageStatus)
                    .collect(Collectors.toList());
            final int size = packageStatusList.size();
            if (size > 1) {
                log.debug("套餐状态不统一，不允许全部退订");
                throw new BizException("不允许全部退订");
            }
            if (size == 1) {
                String packageStatus = packageStatusList.iterator().next();
                final String orderUserId = order.getOrderUserId();
                ChannelDistributorDetail distributorDetail = getChannelDistributorDetail(orderUserId, order.getCurrencyCode());
                if (PackageStatusEnum.UNACTIVATED.getStatus().equals(packageStatus)) {
                    // 待激活
                    log.debug("套餐状态为待激活");
                    final Integer count = channelPackageCardMapper.selectCount(Wrappers.lambdaQuery(ChannelPackageCard.class)
                            .in(ChannelPackageCard::getPackageUniqueId, packageUniqueIds)
                            .eq(ChannelPackageCard::getPackageStatus, packageStatus)
                            .lt(ChannelPackageCard::getEffectiveDay, new Date()));
                    final int num = packageUniqueIds.size();
                    log.debug("套餐个数: {}", num);
                    if (count > 0 && count < num) {
                        throw new BizException("不允许全部退订，请进入详情页操作");
                    }
                    ChannelDistributorDetail subChannelDistributorDetail = null;
                    if (StringUtils.isNotBlank(order.getSubCorpId())) {
                        subChannelDistributorDetail = distributorDetailMapper.selectChannelDetail(order.getSubCorpId());
                    }
                    if (count == 0) {
                        inactivatedPackageUnsubAll(order, packageUniqueIds, distributorDetail, false, asyncNotifyType, subChannelDistributorDetail);
                        return;
                    }
                }
                // 已激活/已使用/已过期/激活中
                unsubscribeOrderUnAudit(id, asyncNotifyType);
            }
            return;
        }
        if (OrderTypeEnum.CARD_PACKAGE.getOrderType().equals(orderType)) {
            // 购买卡+套餐，只有待发货、已完成的支持退订
            log.debug("当前订单类型为购买卡+套餐，订单id: {} status: {}", id, orderStatus);
            final String orderUserId = order.getOrderUserId();
            if (ChannelOrder.OrderStatusEnum.NOT_DELIVERY.getValue().equals(orderStatus)) {
                log.debug("[卡+套餐][待发货]查询子订单状态");
                final Integer count = channelOrderDetailMapper.selectCount(Wrappers.lambdaQuery(ChannelOrderDetail.class)
                        .eq(ChannelOrderDetail::getOrderId, id)
                        .ne(ChannelOrderDetail::getOrderStatus, ChannelOrder.OrderStatusEnum.NOT_DELIVERY.getValue()));
                if (count > 0) {
                    throw new BizException("存在非待发货状态的订单，请进入详情页进行退订");
                }
                ChannelDistributorDetail distributorDetail = getChannelDistributorDetail(orderUserId, order.getCurrencyCode());
                refundOrder(order, distributorDetail, null);
                unsubscribeOrder(order, PackageStatusEnum.NOT_DELIVERED, false, asyncNotifyType);
                return;
            }
            if (ChannelOrder.OrderStatusEnum.FINISHED.getValue().equals(orderStatus)) {
                log.debug("[卡+套餐][已完成]查询子订单状态");
                Integer count = channelOrderDetailMapper.selectCount(Wrappers.lambdaQuery(ChannelOrderDetail.class)
                        .eq(ChannelOrderDetail::getOrderId, id)
                        .ne(ChannelOrderDetail::getOrderStatus, ChannelOrder.OrderStatusEnum.FINISHED.getValue()));
                if (count > 0) {
                    throw new BizException("存在非已完成的订单，请进入详情页进行退订");
                }
                final List<String> packageUniqueIds = getPackageUniqueIdsByOrderId(id);
                final List<String> packageStatusList = channelPackageCardMapper.selectList(Wrappers.lambdaQuery
                                        (ChannelPackageCard.class)
                                .select(ChannelPackageCard::getPackageStatus)
                                .in(ChannelPackageCard::getPackageUniqueId, packageUniqueIds)
                                .groupBy(ChannelPackageCard::getPackageStatus)).stream()
                        .map(ChannelPackageCard::getPackageStatus)
                        .collect(Collectors.toList());
                final int size = packageStatusList.size();
                if (size > 1) {
                    log.debug("套餐状态不统一，不允许全部退订");
                    throw new BizException("不允许全部退订，请进入详情页操作");
                }
                if (size == 1) {
                    String packageStatus = packageStatusList.iterator().next();
                    ChannelDistributorDetail distributorDetail = getChannelDistributorDetail(orderUserId, order.getCurrencyCode());
                    if (PackageStatusEnum.UNACTIVATED.getStatus().equals(packageStatus)) {
                        // 待激活
                        log.debug("套餐状态均为待激活");
                        count = channelPackageCardMapper.selectCount(Wrappers.lambdaQuery(ChannelPackageCard.class)
                                .in(ChannelPackageCard::getPackageUniqueId, packageUniqueIds)
                                .eq(ChannelPackageCard::getPackageStatus, packageStatus)
                                .lt(ChannelPackageCard::getEffectiveDay, new Date()));
                        final int num = packageUniqueIds.size();
                        log.debug("套餐个数: {}", num);
                        if (count > 0 && count < num) {
                            throw new BizException("不允许全部退订，请进入详情页操作");
                        }
                        if (count == 0) {
                            inactivatedPackageUnsubAll(order, packageUniqueIds, distributorDetail, false, asyncNotifyType, null);
                            return;
                        }
                    }
                    // 已激活/已使用/已过期/激活中
                    unsubscribeOrderUnAudit(id, asyncNotifyType);
                }
                return;
            }
        }
        if (OrderTypeEnum.REFUEL_PACKAGE.getOrderType().equals(orderType)) {
            log.debug("当前订单类型为购买加油包，订单id: {} status: {}", id, orderStatus);
            //直接走总单退订审批流程
            unsubscribeOrderUnAudit(id, asyncNotifyType);
            return;
        }
        throw new BizException("当前订单状态不允许退订");
    }

    /**
     * 待激活套餐全部退订流程
     *
     * @param order
     * @param packageUniqueIds
     * @param distributorDetail
     */
    public void inactivatedPackageUnsubAll(ChannelOrder order, List<String> packageUniqueIds,
                                           ChannelDistributorDetail distributorDetail, boolean isApi, String asyncNotifyType,
                                           ChannelDistributorDetail subChannelDistributorDetail) {
        log.debug("待激活套餐全部退订流程");
        if (!redissonLock.tryLockMulti(packageUniqueIds)) {
            log.warn("分布式锁获取失败 packageUniqueIds: {}", packageUniqueIds);
            throw new BizException("退订失败", ApiResponseEnum.ACQUIRE_LOCK_FAILED.getCode());
        }
        try {
            refundOrder(order, distributorDetail, subChannelDistributorDetail);
            unsubscribeOrder(order, PackageStatusEnum.UNACTIVATED, isApi, asyncNotifyType);
        } finally {
            redissonLock.unlockMulti(packageUniqueIds);
        }
    }

    /**
     * 审核总订单
     *
     * @param auditOrderReq
     */
    @Transactional(rollbackFor = Exception.class)
    public void auditOrder(AuditOrderReq auditOrderReq) {
        Long orderId = Long.valueOf(auditOrderReq.getId());
        String status = checkStatus(auditOrderReq.getStatus());
        final ChannelOrder order = channelOrderMapper.selectById(orderId);
        if (!ChannelOrder.OrderStatusEnum.UNSUBSCRIBE_NOT_AUDIT.getValue().equals(order.getOrderStatus())) {
            throw new BizException("当前订单状态不为激活退订待审批");
        }
        final Integer count = channelOrderDetailMapper.selectCount(Wrappers.lambdaQuery(ChannelOrderDetail.class)
                .eq(ChannelOrderDetail::getOrderId, orderId)
                .ne(ChannelOrderDetail::getOrderStatus, ChannelOrder.OrderStatusEnum.UNSUBSCRIBE_NOT_AUDIT.getValue()));
        if (count > 0) {
            log.debug("存在非激活退订待审批状态的子订单，不允许退订审批操作 orderId: {}", orderId);
            throw new BizException("不能使用激活退订审批操作，请进详单操作");
        }
        if (AuditStatusEnum.NOT_PASS.matches(status)) {
            auditNotPassOrder(orderId);
        } else {
            try {
                if (!redissonLock.tryLock(auditOrderReq.getId())) {
                    throw new BizException("订单正在处理中，请稍后重试");
                }
                auditPassOrder(order);
            } finally {
                if (redissonLock.isHeldByCurrentThread(auditOrderReq.getId())) {
                    redissonLock.unlock(auditOrderReq.getId());
                }
            }
        }
    }

    /**
     * 审核子订单
     *
     * @param auditOrderReq
     */
    @Transactional(rollbackFor = Exception.class)
    public void auditOrderDetail(AuditOrderReq auditOrderReq) {
        Long orderDetailId = Long.valueOf(auditOrderReq.getId());
        String status = checkStatus(auditOrderReq.getStatus());
        final ChannelOrderDetail orderDetail = channelOrderDetailMapper.selectById(orderDetailId);
        if (!ChannelOrder.OrderStatusEnum.UNSUBSCRIBE_NOT_AUDIT.getValue().equals(orderDetail.getOrderStatus())) {
            throw new BizException("当前订单状态不为激活退订待审批");
        }
        if (AuditStatusEnum.NOT_PASS.matches(status)) {
            auditNotPassOrderDetail(orderDetailId);
        } else {
            String orderId = orderDetail.getOrderId().toString();
            try {
                if (!redissonLock.tryLock(orderId)) {
                    throw new BizException("订单正在处理中，请稍后重试");
                }
                auditPassOrderDetail(orderDetail);
            } finally {
                if (redissonLock.isHeldByCurrentThread(orderId)) {
                    redissonLock.unlock(orderId);
                }
            }
        }
    }

    /**
     * 子订单审核不通过
     *
     * @param id 子订单id
     */
    private void auditNotPassOrderDetail(Long id) {
        log.debug("子订单审核不通过，更新子订单状态为已完成 id: {}", id);
        ChannelOrderDetail orderDetailToUpdate = new ChannelOrderDetail();
        orderDetailToUpdate.setId(id);
        orderDetailToUpdate.setOrderStatus(ChannelOrder.OrderStatusEnum.FINISHED.getValue());
        channelOrderDetailMapper.updateById(orderDetailToUpdate);

        ChannelOrder orderToUpdate = new ChannelOrder();
        final ChannelOrderDetail orderDetail = channelOrderDetailMapper.selectById(id);
        final Long orderId = orderDetail.getOrderId();
        orderToUpdate.setId(orderId);
        log.debug("子订单退订审核不通过，更新总订单状态，查询是否有其他非已完成状态的子单");
        List<ChannelOrderDetail> otherOrderDetails = channelOrderDetailMapper.selectList(Wrappers.lambdaQuery
                        (ChannelOrderDetail.class)
                .select(ChannelOrderDetail::getOrderStatus)
                .eq(ChannelOrderDetail::getOrderId, orderId)
                .ne(ChannelOrderDetail::getPackageUniqueId, orderDetail.getPackageUniqueId())
                .ne(ChannelOrderDetail::getOrderStatus, ChannelOrder.OrderStatusEnum.FINISHED.getValue()));
        ChannelOrder.OrderStatusEnum orderStatusEnum;
        if (CollectionUtils.isEmpty(otherOrderDetails)) {
            orderStatusEnum = ChannelOrder.OrderStatusEnum.FINISHED;
        } else {
            Set<String> otherOrderStatusSet = otherOrderDetails.stream().map(ChannelOrderDetail::getOrderStatus)
                    .collect(Collectors.toSet());
            if (otherOrderStatusSet.contains(ChannelOrder.OrderStatusEnum.UNSUBSCRIBE_NOT_AUDIT.getValue())) {
                orderStatusEnum = ChannelOrder.OrderStatusEnum.UNSUBSCRIBE_NOT_AUDIT;
            } else if (otherOrderStatusSet.contains(ChannelOrder.OrderStatusEnum.UNSUBSCRIBE.getValue())
                    && otherOrderStatusSet.contains(ChannelOrder.OrderStatusEnum.NOT_DELIVERY.getValue())) {
                orderStatusEnum = ChannelOrder.OrderStatusEnum.COMBINATION;
            } else if (otherOrderStatusSet.stream().allMatch(ChannelOrder.OrderStatusEnum.NOT_DELIVERY.getValue()::equals)) {
                orderStatusEnum = ChannelOrder.OrderStatusEnum.PARTIAL_DELIVERED;
            } else {
                orderStatusEnum = ChannelOrder.OrderStatusEnum.PARTIAL_UNSUBSCRIBE;
            }
        }
        orderToUpdate.setOrderStatus(orderStatusEnum.getValue());
        channelOrderMapper.updateById(orderToUpdate);
    }

    /**
     * 总订单审核不通过
     *
     * @param orderId 总订单id
     */
    private void auditNotPassOrder(Long orderId) {
        log.debug("总订单审核不通过，更新总订单、子订单状态为已完成");
        ChannelOrderDetail orderDetail = new ChannelOrderDetail();
        orderDetail.setOrderStatus(ChannelOrder.OrderStatusEnum.FINISHED.getValue());
        channelOrderDetailMapper.update(orderDetail, Wrappers.lambdaQuery(ChannelOrderDetail.class)
                .eq(ChannelOrderDetail::getOrderId, orderId));
        ChannelOrder order = new ChannelOrder();
        order.setId(orderId);
        order.setOrderStatus(ChannelOrder.OrderStatusEnum.FINISHED.getValue());
        channelOrderMapper.updateById(order);
    }

    private String checkStatus(String status) {
        if (AuditStatusEnum.PASSED.matches(status) || AuditStatusEnum.NOT_PASS.matches(status)) {
            return status;
        }
        throw new BizException("请求参数status错误");
    }

    /**
     * 总订单审核通过
     *
     * @param order
     */
    private void auditPassOrder(ChannelOrder order) {
        log.debug("总订单审核通过流程");
        ChannelDistributorDetail distributorDetail = getChannelDistributorDetail(order.getOrderUserId(), order.getCurrencyCode());
        final List<String> packageUniqueIds = getPackageUniqueIdsByOrderId(order.getId());
        List<ChannelPackageCard> packageCards = channelPackageCardMapper.selectList(Wrappers.lambdaQuery(ChannelPackageCard.class)
                .in(ChannelPackageCard::getPackageUniqueId, packageUniqueIds)
                .orderByAsc(ChannelPackageCard::getExpireTime));
        Date now = new Date();
        ChannelDistributorDetail subDistributorDetail = null;
        if (StringUtils.isNotBlank(order.getSubCorpId())) {
            log.debug("子渠道商订单，查询子渠道商详情");
            subDistributorDetail = distributorDetailMapper.selectChannelDetail(order.getSubCorpId());
        }
        refundOrder(order, distributorDetail, subDistributorDetail);
        if (!OrderTypeEnum.REFUEL_PACKAGE.getOrderType().equals(order.getOrderType())) {
            log.debug("审核通过,订单类型不为加油包");
            packageCards.forEach(packageCard -> {
                ChannelOrderDetail orderDetail = channelOrderDetailMapper.selectOne(Wrappers.lambdaQuery(ChannelOrderDetail.class)
                        .eq(ChannelOrderDetail::getPackageUniqueId, packageCard.getPackageUniqueId()));
                String packageStatus = packageCard.getPackageStatus();
                final boolean unactiveOverdue = PackageStatusEnum.UNACTIVATED.getStatus().equals(packageStatus)
                        && now.after(packageCard.getEffectiveDay());
                if (unactiveOverdue || PackageStatusEnum.OVERDUE.getStatus().equals(packageStatus)) {
                    unsubscribeInactiveAndOverdue(order, orderDetail);
                } else {
                    unsubscribeActivatedOrderDetail(order, orderDetail, packageCard);
                }
            });
        } else {
            log.debug("审核通过, 订单类型为加油包");
            List<Date> dateList = packageCards.stream().map(ChannelPackageCard::getExpireTime).collect(Collectors.toList());
            refundRefuelPackage(order, packageCards);
            //加油包数量
            Integer refuelNumber = order.getCount();
            Date nowTime = new Date();
            //获取此订单任意一个加油包
            ChannelPackageCard refuelPackage = packageCards.get(0);
            String belongPackageId = refuelPackage.getBelongPackageId();
            //找到加油包绑定的套餐
            ChannelPackageCard channelPackageCard = channelPackageCardMapper.selectOne(Wrappers.<ChannelPackageCard>lambdaQuery()
                    .eq(ChannelPackageCard::getPackageUniqueId, belongPackageId));

            String flowLimitType = channelPackageCard.getFlowLimitType();
            BigDecimal flowLimitSum = refuelPackage.getFlowLimitSum();
            long flowCount;
            List<BigDecimal> presentTrafficValue;

            if (ChannelPackageCard.FlowLimitTypeEnum.DAY_LIMIT.getValue().equals(flowLimitType)) {
                if (refuelNumber == 1 && dateList.size() == 1) {
                    if (nowTime.after(dateList.get(0))) {
                        log.debug("加油包数量为1，且已过时，跳过流量扣除流程");
                        return;
                    }
                    log.debug("加油包数量为1，且没有过时");
                }
                //确定套餐当前所在周期及流量扣除返回值
                flowCount = flowLimitSum.longValue();
                presentTrafficValue = getDayPresentTrafficValue(nowTime, channelPackageCard, flowCount, refuelNumber);

            } else {
                flowCount = refuelNumber * Long.parseLong(flowLimitSum.toString());
                presentTrafficValue = getCyclePresentTrafficValue(flowCount, channelPackageCard);
            }

            if (presentTrafficValue.get(0) == null || presentTrafficValue.get(1) == null) {
                log.warn("[流量扣除]套餐周期流量剩余key或者 加油包当前周期key 不存在，是否达量流程结束");
                return;
            }
            //扣除流量返回值
            boolean isControlSpeed = StringUtils.isNotBlank(channelPackageCard.getSpeedControlId());

            if (isControlSpeed) {
                log.debug("套餐处于控速，流程结束");
                return;
            }

            long trafficValue = presentTrafficValue.get(0).longValue();
            //剩余流量
            long packageRemainFlow = presentTrafficValue.get(1).longValue();
            if (trafficValue <= 0) {
                if (packageRemainFlow > 0) {
                    log.debug("[达量处理][套餐剩余流量 > 0] 模拟LU或触发rar");
                    Boolean isNewCard = ccrCommonService.DetermineNewAndOldCards(channelPackageCard.getImsi());
                    if (isNewCard) {
                        log.info("==================检测到是H新卡，判断是否触发rar====================");
                        flowPoolServiceImpl.getHCardOrVCard(channelPackageCard.getIccid());
                    } else {
                        HimsiStatusAndLocationDTO userLocation = null;
                        try {
                            userLocation = packageEndService.getUserLocation(HimsiStatusAndLocationVO.builder()
                                    .iccid(channelPackageCard.getIccid())
                                    .build());
                        } catch (Exception e) {
                            log.error("位置查询失败, 跳过达量处理", e);
                            return;
                        }
                        if (userLocation == null || org.springframework.util.StringUtils.isEmpty(userLocation.getMobileCountryCode())) {
                            log.error("位置查询失败, 跳过达量处理");
                            return;
                        }
                        log.info("==================检测到是H旧卡，模拟LU上报流程====================");
                        luWarpper.mockLu(MockLuVO.builder()
                                .mcc(userLocation.getMobileCountryCode())
                                .himsi(channelPackageCard.getImsi())
                                .iccid(channelPackageCard.getIccid())
                                .msisdn(channelPackageCard.getMsisdn())
                                .activeType("1")
                                .cardType("1")
                                .cardForm("1")
                                .imsi(channelPackageCard.getImsi())
                                .build());
                    }

                } else {
                    log.debug("[达量处理][套餐剩余流量 < 0]入队列");
                    packageProcessingOfVolume(channelPackageCard.getImsi(), channelPackageCard.getFlowLimitType(), channelPackageCard.getPeriodUnit(), channelPackageCard);
                }
            } else {
                log.debug("[单日限量][达量处理]不满足达量处理流程流量条件或非普通套餐类型");
            }
        }
    }

    /**
     * 待激活已过期退订流程
     *
     * @param order
     * @param orderDetail
     */
    private void unsubscribeInactiveAndOverdue(ChannelOrder order, ChannelOrderDetail orderDetail) {
        log.debug("待激活已过期退订流程");
        channelPackageCardMapper.delete(Wrappers.lambdaQuery(ChannelPackageCard.class)
                .eq(ChannelPackageCard::getPackageUniqueId, orderDetail.getPackageUniqueId()));
        unsubscribeOrderDetail(order, orderDetail, PackageStatusEnum.OVERDUE);
    }

    /**
     * 子订单审核通过
     *
     * @param orderDetail
     */
    private void auditPassOrderDetail(ChannelOrderDetail orderDetail) {
        log.debug("子订单审核通过流程");
        Long orderId = orderDetail.getOrderId();
        ChannelOrder order = channelOrderMapper.selectById(orderId);
        if (order == null) {
            log.debug("总订单不存在 orderId: {}", orderId);
            throw new BizException("审核失败");
        }
        ChannelDistributorDetail distributorDetail = getChannelDistributorDetail(order.getOrderUserId(),
                orderDetail.getCurrencyCode());
        refundOrderDetail(order, orderDetail, distributorDetail);
        final String packageUniqueId = orderDetail.getPackageUniqueId();
        final ChannelPackageCard packageCard = channelPackageCardMapper.selectOne(Wrappers.lambdaQuery(ChannelPackageCard.class)
                .eq(ChannelPackageCard::getPackageUniqueId, packageUniqueId));
        final String packageStatus = packageCard.getPackageStatus();
        final boolean unactiveOverdue = PackageStatusEnum.UNACTIVATED.getStatus().equals(packageStatus)
                && new Date().after(packageCard.getEffectiveDay());
        if (unactiveOverdue || PackageStatusEnum.OVERDUE.getStatus().equals(packageStatus)) {
            unsubscribeOrderDetail(order, orderDetail, PackageStatusEnum.OVERDUE);
        } else {
            unsubscribeActivatedOrderDetail(order, orderDetail, packageCard);
        }
    }

    /**
     * 子订单已激活退订流程
     *
     * @param order
     * @param orderDetail
     * @param packageCard
     */
    private void unsubscribeActivatedOrderDetail(ChannelOrder order, ChannelOrderDetail orderDetail, ChannelPackageCard packageCard) {
        String packageUniqueId = packageCard.getPackageUniqueId();
        log.debug("进入已激活退订流程 packageUniqueId: {}", packageUniqueId);
        final boolean lock = redissonLock.tryLock(packageUniqueId);
        if (!lock) {
            log.warn("获取分布式锁失败 packageUniqueId: {}", packageUniqueId);
            throw new BizException("退订失败");
        }
        try {
            unsubscribeActivatedOrderDetail0(order, orderDetail, packageCard);
        } finally {
            if (redissonLock.isHeldByCurrentThread(packageUniqueId)) {
                redissonLock.unlock(packageUniqueId);
            }
        }
    }

    private void unsubscribeActivatedOrderDetail0(ChannelOrder order, ChannelOrderDetail orderDetail, ChannelPackageCard packageCard) {
        final String packageUniqueId = packageCard.getPackageUniqueId();
        Date now = new Date();
        final String packageStatus = packageCard.getPackageStatus();
        final boolean activated = PackageStatusEnum.ACTIVATED.getStatus().equals(packageStatus);
        final boolean activating = PackageStatusEnum.ACTIVATING.getStatus().equals(packageStatus);
        if (!PackageStatusEnum.USED.getStatus().equals(packageStatus)) {
            List<ChannelSurf> channelSurfs = channelSurfMapper.selectList(Wrappers.lambdaQuery(ChannelSurf.class)
                    .select(ChannelSurf::getImsi, ChannelSurf::getMadeImsi, ChannelSurf::getHimsi,
                            ChannelSurf::getInternetType, ChannelSurf::getMcc)
                    .eq(ChannelSurf::getPackageUniqueId, packageUniqueId));
            String corpId = packageCard.getCorpId();
            String imsi = packageCard.getImsi();
            final List<ChannelSurf> vCardSurfList = channelSurfs.stream()
                    .filter(channelSurf -> ChannelSurf.InternetTypeEnum.V.getValue().equals(channelSurf.getInternetType()))
                    .collect(Collectors.toList());
            log.debug("处理V卡上网记录");
            for (ChannelSurf channelSurf : vCardSurfList) {
                final String vImsi = channelSurf.getImsi();
                final String mcc = channelSurf.getMcc();
                log.debug("删除redis中的vimsi记录 imsi: {} mcc: {}", vImsi, mcc);
                String key = String.format(BizConstants.LU_V2_KEY_PATTERN, channelSurf.getMadeImsi(), mcc);
                redisTemplate.delete(key);
                boolean delHssSubscriberResult = packageOverdueService.delHssSubscriber(vImsi);
                if (!delHssSubscriberResult) {
                    log.warn("V卡HSS销户失败 imsi: {}", vImsi);
                    throw new BizException("退订失败");
                }
                log.debug("判断V卡卡池UPCC动态签约标志位 imsi: {}", vImsi);
                final Boolean isSignUpcc = pmsFeignClient.getIsSignUpcc(vImsi).get();
                if (isSignUpcc) {
                    log.debug("V卡所在卡池为UPCC动态签约，进行UPCC销户 imsi: {}", vImsi);
                    boolean delUpccSubscriberResult = packageOverdueService.delUpccSubscriber(vImsi, CardTypeEnum.V_CARD);
                    if (!delUpccSubscriberResult) {
                        log.warn("V卡UPCC销户失败 imsi: {}", vImsi);
                        throw new BizException("退订失败");
                    }
                } else {
                    log.debug("V卡所在卡池非UPCC动态签约，不进行UPCC销户 imsi: {}", vImsi);
                    pmsFeignClient.updateVimsiOpenStatus(UpdateOpenStatusReq.builder().imsi(vImsi).freeze(true).build());
                }
            }
            log.debug("处理H卡 imsi: {}", imsi);
            Integer otherAvailablePackagesCount = channelPackageCardMapper.selectCount(Wrappers.lambdaQuery(ChannelPackageCard.class)
                    .eq(ChannelPackageCard::getImsi, imsi)
                    .ne(ChannelPackageCard::getPackageUniqueId, packageUniqueId)
                    .and(wrapper ->
                            wrapper.and(wrapper2 -> wrapper2.eq(ChannelPackageCard::getPackageStatus, PackageStatusEnum.ACTIVATING.getStatus())
                                            .gt(ChannelPackageCard::getEffectiveDay, now))
                                    .or(wrapper3 -> wrapper3.eq(ChannelPackageCard::getPackageStatus, PackageStatusEnum.ACTIVATED.getStatus())
                                            .gt(ChannelPackageCard::getExpireTime, now)))
                    .eq(ChannelPackageCard::getCorpId, corpId));
            if (otherAvailablePackagesCount == 0) {
                log.debug("没有其它激活中/已激活的套餐，进入H卡签约0k模板流程");
                boolean delSubscriberResult = packageOverdueService.delUpccSubscriber(imsi, CardTypeEnum.H_CARD);
                if (!delSubscriberResult) {
                    log.warn("H卡UPCC签约0k模板 imsi: {}", imsi);
                    throw new BizException("退订失败");
                }
            }
            // 更新套餐状态为已使用
            ChannelPackageCard updatePackageCard = new ChannelPackageCard();
            updatePackageCard.setPackageStatus(PackageStatusEnum.USED.getStatus());
            updatePackageCard.setExpireTime(now);
            channelPackageCardMapper.update(updatePackageCard, Wrappers.lambdaQuery(ChannelPackageCard.class)
                    .eq(ChannelPackageCard::getPackageUniqueId, packageUniqueId));
            log.debug("下发套餐到期通知短信");
            final HcardInfo card = getCardByImsi(imsi);
            final String packageName = packageService.getPackageName(packageCard, card.getSendLang());
            smsService.sendActivatedOverdueSms(card, packageUniqueId, packageName, now);
            if (PackageStatusEnum.UNACTIVATED.getStatus().equals(packageStatus)
                    || PackageStatusEnum.OVERDUE.getStatus().equals(packageStatus)) {
                channelPackageCardMapper.delete(Wrappers.lambdaQuery(ChannelPackageCard.class)
                        .eq(ChannelPackageCard::getPackageUniqueId, packageUniqueId));
            } else {
                if (activating || activated) {
                    log.info("激活中/已激活套餐退订，更新上网结束时间");
                    packageOverdueService.updateSurfEndTime(packageUniqueId, new Date(), false);
                    packageOverdueService.deleteRedisKey(packageCard, true);
                    cmsPackageDelayRecordInfoMapper.delete(Wrappers.lambdaQuery(CmsPackageDelayRecordInfo.class)
                            .eq(CmsPackageDelayRecordInfo::getOldPackageUniqueId, packageUniqueId));
                    channelCardMapper.update(null, Wrappers.lambdaUpdate(ChannelCard.class)
                            .set(ChannelCard::getLastVimsi, null)
                            .eq(ChannelCard::getImsi, imsi)
                            .eq(ChannelCard::getPackageUniqueId, packageUniqueId));
                }
            }
        }
        // a)	package_status = 2 and expire_time > 当前时间 则为已激活。
        // b)	package_status = 2 and expire_time < 当前时间 则为已使用。
        // c)	package_status = 6 and effective_day > 当前时间 则为已激活。
        // d)	package_status = 6 and effective_day < 当前时间 则为已使用。
        // e)	package_status = 3 则为已使用。
        PackageStatusEnum packageStatusEnum;
        if (activated) {
            final Date expireTime = packageCard.getExpireTime();
            if (expireTime.after(now)) {
                packageStatusEnum = PackageStatusEnum.ACTIVATED;
            } else {
                packageStatusEnum = PackageStatusEnum.USED;
            }
        } else if (activating) {
            final Date effectiveDay = packageCard.getEffectiveDay();
            if (effectiveDay.after(now)) {
                packageStatusEnum = PackageStatusEnum.ACTIVATED;
            } else {
                packageStatusEnum = PackageStatusEnum.USED;
            }
        } else if (PackageStatusEnum.USED.getStatus().equals(packageStatus)) {
            packageStatusEnum = PackageStatusEnum.USED;
        } else {
            log.warn("异常流程，套餐状态不是已激活/已使用/激活中");
            throw new BizException("退订失败");
        }
        //退订子单
        unsubscribeOrderDetail(order, orderDetail, packageStatusEnum);
        //退订加油包
        unsubscribeRefuelOrder(orderDetail, packageCard);
    }

    /**
     * 退订套餐绑定的加油包
     *
     * @param orderDetail
     * @param channelPackageCard
     */
    private void unsubscribeRefuelOrder(ChannelOrderDetail orderDetail, ChannelPackageCard channelPackageCard) {
        log.debug("套餐退订，流程最后退订套餐绑定的加油包");
        String packageUniqueId = orderDetail.getPackageUniqueId();
        //绑定的加油包
        List<ChannelPackageCard> packageCards = channelPackageCardMapper.selectList(Wrappers.<ChannelPackageCard>lambdaQuery()
                .eq(ChannelPackageCard::getBelongPackageId, packageUniqueId));
        if (packageCards == null || packageCards.size() == 0) {
            log.debug("套餐退订,没有绑定的加油包，流程结束😄");
            return;
        }
        List<String> orderUniqueIdList = packageCards.stream().map(ChannelPackageCard::getOrderUniqueId).distinct().collect(Collectors.toList());
        log.debug("套餐退订,有绑定的加油包┭┮﹏┭┮，此套餐关联的加油包对应的总单数为： {}", orderUniqueIdList.size());
        List<ChannelOrder> channelOrders = channelOrderMapper.selectList(Wrappers.<ChannelOrder>lambdaQuery()
                .in(ChannelOrder::getOrderUniqueId, orderUniqueIdList)
                .ne(ChannelOrder::getOrderStatus, ChannelOrder.OrderStatusEnum.UNSUBSCRIBE.getValue()));
        int i = 0;
        for (ChannelOrder order : channelOrders) {
            i++;
            log.debug("循环退订套餐关联加油包，轮数：{}", i);
            ChannelDistributorDetail subChannelDistributorDetail = null;
            if (StringUtils.isNotBlank(order.getSubCorpId())) {
                subChannelDistributorDetail = distributorDetailMapper.selectChannelDetail(order.getSubCorpId());
            }
            ChannelDistributorDetail channelDistributorDetail = getChannelDistributorDetail(order.getOrderUserId(), order.getCurrencyCode());
            try {
                refundOrder(order, channelDistributorDetail, subChannelDistributorDetail);
            } catch (Exception e) {
                log.error("e: ", e);
                log.debug("此加油包订单退款失败，跳过此订单。");
                continue;
            }
            List<ChannelPackageCard> packageCards1 = channelPackageCardMapper.selectList(Wrappers.<ChannelPackageCard>lambdaQuery()
                    .eq(ChannelPackageCard::getOrderUniqueId, order.getOrderUniqueId()));
            refundRefuelPackage(order, packageCards1);
            log.debug("循环退订套餐关联加油包，轮数：{} 完成", i);
        }
    }


    private HcardInfo getCardByImsi(String imsi) {
        return Response.getAndCheckRemoteData(pmsFeignClient.getCardByImsi(imsi));
    }

    private List<String> getPackageUniqueIdsByOrderId(Long id) {
        return channelOrderDetailMapper.selectList(Wrappers.lambdaQuery(ChannelOrderDetail.class)
                        .select(ChannelOrderDetail::getPackageUniqueId)
                        .eq(ChannelOrderDetail::getOrderId, id))
                .stream()
                .map(ChannelOrderDetail::getPackageUniqueId)
                .collect(Collectors.toList());
    }

    private ChannelDistributorDetail getChannelDistributorDetail(String orderUserId, String currencyCode) {
        final boolean isChannel = channelMapper.selectCount(Wrappers.lambdaQuery(Channel.class)
                .eq(Channel::getType, Channel.ChannelTypeEnum.CHANNEL.getValue())
                .eq(Channel::getCorpId, orderUserId)) > 0;
        ChannelDistributorDetail distributorDetail = null;
        if (isChannel) {
            log.debug("渠道商用户，判断押金币种与订单是否一致");
            distributorDetail = distributorDetailMapper.selectChannelDetail(orderUserId);
            final String distributorDetailCurrencyCode = distributorDetail.getCurrencyCode();
            log.debug("订单币种: {}, 渠道商押金币种: {}", currencyCode, distributorDetailCurrencyCode);
            if (!currencyCode.equals(distributorDetailCurrencyCode)) {
                throw new BizException("币种不一致，不允许退订");
            }
        }
        return distributorDetail;
    }

    /**
     * 子订单退订写表流程
     *
     * @param order             订单id
     * @param orderDetail       子订单
     * @param packageStatusEnum 退订时的套餐状态
     */
    private void unsubscribeOrderDetail(ChannelOrder order, ChannelOrderDetail orderDetail,
                                        PackageStatusEnum packageStatusEnum) {
        Long orderDetailId = orderDetail.getId();
        log.debug("进入子订单退订流程，订单id: {} 子订单id: {}", order, orderDetailId);
        log.debug("更新子订单状态为已退订 orderId: {}, orderDetailId: {}", order, orderDetailId);
        ChannelOrderDetail orderDetailToUpdate = new ChannelOrderDetail();
        orderDetailToUpdate.setId(orderDetailId);
        orderDetailToUpdate.setUnsubscribeTime(order.getUnsubscribeTime() == null ? new Date() : order.getUnsubscribeTime());
        orderDetailToUpdate.setUnsubscribeChannel(UnsubscribeChannelEnum.CUSTOMER_WEBSITE.getK());
        orderDetailToUpdate.setOrderStatus(ChannelOrder.OrderStatusEnum.UNSUBSCRIBE.getValue());
        orderDetailToUpdate.setPackageStatus(packageStatusEnum.getStatus());
        final boolean isCardAndPackage = OrderTypeEnum.CARD_PACKAGE.getOrderType().equals(orderDetail.getOrderType());
        if (isCardAndPackage) {
            orderDetailToUpdate.setIccid(StringPool.EMPTY);
        }
        channelOrderDetailMapper.updateById(orderDetailToUpdate);

        ChannelOrder orderToUpdate = new ChannelOrder();
        if (isCardAndPackage) {
            String orderIccid = order.getIccid();
            if (StringUtils.isNotBlank(orderIccid)) {
                List<String> iccids = Arrays.stream(orderIccid.split(StringPool.BACK_SLASH + StringPool.PIPE))
                        .collect(Collectors.toList());
                orderToUpdate.setIccid(iccids.stream()
                        .filter(item -> !item.equals(orderDetail.getIccid()))
                        .collect(Collectors.joining(StringPool.PIPE)));
            }
        }
        final Long orderId = order.getId();
        orderToUpdate.setId(orderId);
        log.debug("子订单退订，更新总订单状态，查询是否有其他非已退订状态的子单");
        List<ChannelOrderDetail> otherOrderDetails = channelOrderDetailMapper.selectList(Wrappers.lambdaQuery
                        (ChannelOrderDetail.class)
                .select(ChannelOrderDetail::getOrderStatus)
                .eq(ChannelOrderDetail::getOrderId, orderId)
                .ne(ChannelOrderDetail::getPackageUniqueId, orderDetail.getPackageUniqueId())
                .ne(ChannelOrderDetail::getOrderStatus, ChannelOrder.OrderStatusEnum.UNSUBSCRIBE.getValue()));
        ChannelOrder.OrderStatusEnum orderStatusEnum;
        if (CollectionUtils.isEmpty(otherOrderDetails)) {
            orderStatusEnum = ChannelOrder.OrderStatusEnum.UNSUBSCRIBE;
        } else {
            Set<String> otherOrderStatusSet = otherOrderDetails.stream().map(ChannelOrderDetail::getOrderStatus)
                    .collect(Collectors.toSet());
            if (otherOrderStatusSet.contains(ChannelOrder.OrderStatusEnum.UNSUBSCRIBE_NOT_AUDIT.getValue())) {
                orderStatusEnum = ChannelOrder.OrderStatusEnum.UNSUBSCRIBE_NOT_AUDIT;
            } else if (otherOrderStatusSet.contains(ChannelOrder.OrderStatusEnum.NOT_DELIVERY.getValue())) {
                orderStatusEnum = ChannelOrder.OrderStatusEnum.COMBINATION;
            } else {
                orderStatusEnum = ChannelOrder.OrderStatusEnum.PARTIAL_UNSUBSCRIBE;
            }
        }
        orderToUpdate.setOrderStatus(orderStatusEnum.getValue());
        channelOrderMapper.updateById(orderToUpdate);
    }

    /**
     * 总订单退订写表流程
     *
     * @param order             订单id
     * @param packageStatusEnum 退订时的套餐状态
     */
    public void unsubscribeOrder(ChannelOrder order, PackageStatusEnum packageStatusEnum, Boolean isApi, String asyncNotifyType) {
        final Long orderId = order.getId();
        log.debug("进入总订单退订流程，订单id: {}", orderId);
        List<String> packageUniqueIds = getPackageUniqueIdsByOrderId(orderId);
        if (packageStatusEnum == PackageStatusEnum.UNACTIVATED) {
            channelPackageCardMapper.delete(Wrappers.lambdaQuery(ChannelPackageCard.class)
                    .in(ChannelPackageCard::getPackageUniqueId, packageUniqueIds));

            packageDirectionRelationMapper.delete(Wrappers.lambdaQuery(PackageDirectionRelation.class)
                    .in(PackageDirectionRelation::getPackageUniqueId, packageUniqueIds));

        }
        log.debug("更新子订单及总订单状态为已退订 orderId: {}", orderId);
        ChannelOrderDetail orderDetailToUpdate = new ChannelOrderDetail();
        orderDetailToUpdate.setUnsubscribeTime(order.getUnsubscribeTime() == null ? new Date() : order.getUnsubscribeTime());
        orderDetailToUpdate.setUnsubscribeChannel(isApi ? UnsubscribeChannelEnum.API.getK() : UnsubscribeChannelEnum.CUSTOMER_WEBSITE.getK());
        orderDetailToUpdate.setOrderStatus(ChannelOrder.OrderStatusEnum.UNSUBSCRIBE.getValue());
        orderDetailToUpdate.setPackageStatus(packageStatusEnum.getStatus());

        ChannelOrder orderToUpdate = new ChannelOrder();
        if (OrderTypeEnum.CARD_PACKAGE.getOrderType().equals(order.getOrderType())) {
            orderToUpdate.setIccid(StringPool.EMPTY);
            orderDetailToUpdate.setIccid(StringPool.EMPTY);
        }
        channelOrderDetailMapper.update(orderDetailToUpdate, Wrappers.lambdaQuery(ChannelOrderDetail.class)
                .eq(ChannelOrderDetail::getOrderId, orderId));

        orderToUpdate.setId(orderId);
        orderToUpdate.setOrderStatus(ChannelOrder.OrderStatusEnum.UNSUBSCRIBE.getValue());
        orderToUpdate.setAsyncNotifyType(asyncNotifyType);
        channelOrderMapper.updateById(orderToUpdate);
    }

    /**
     * 子单退订待审批流程
     *
     * @param orderDetailId
     */
    private void unsubscribeOrderDetailUnAudit(Long orderId, Long orderDetailId) {
        log.debug("进入子订单退订待审批流程，更新总单及子单为退订待审核 orderDetailId: {}", orderDetailId);
        ChannelOrderDetail orderDetailToUpdate = new ChannelOrderDetail();
        orderDetailToUpdate.setId(orderDetailId);
        orderDetailToUpdate.setOrderStatus(ChannelOrder.OrderStatusEnum.UNSUBSCRIBE_NOT_AUDIT.getValue());
        channelOrderDetailMapper.updateById(orderDetailToUpdate);
        ChannelOrder orderToUpdate = new ChannelOrder();
        orderToUpdate.setId(orderId);
        orderToUpdate.setOrderStatus(ChannelOrder.OrderStatusEnum.UNSUBSCRIBE_NOT_AUDIT.getValue());
        channelOrderMapper.updateById(orderToUpdate);
    }

    /**
     * 总单退订待审批流程
     *
     * @param orderId
     */
    private void unsubscribeOrderUnAudit(Long orderId, String asyncNotifyType) {
        log.debug("进入退订待审批流程，更新子订单及总订单状态为退订待审核 orderId: {}", orderId);
        ChannelOrderDetail orderDetailToUpdate = new ChannelOrderDetail();
        orderDetailToUpdate.setOrderStatus(ChannelOrder.OrderStatusEnum.UNSUBSCRIBE_NOT_AUDIT.getValue());
        channelOrderDetailMapper.update(orderDetailToUpdate, Wrappers.lambdaQuery(ChannelOrderDetail.class)
                .eq(ChannelOrderDetail::getOrderId, orderId));
        ChannelOrder orderToUpdate = new ChannelOrder();
        orderToUpdate.setId(orderId);
        orderToUpdate.setOrderStatus(ChannelOrder.OrderStatusEnum.UNSUBSCRIBE_NOT_AUDIT.getValue());
        orderToUpdate.setAsyncNotifyType(asyncNotifyType);
        channelOrderMapper.updateById(orderToUpdate);
    }

    /**
     * 总订单退款流程
     *
     * @param order             订单
     * @param distributorDetail 渠道商详情
     */
    public void refundOrder(ChannelOrder order, ChannelDistributorDetail distributorDetail, ChannelDistributorDetail subChannelDistributorDetail) {
        Long id = order.getId();
        log.debug("进入退款流程，订单id: {}", id);
        String orderUserId = order.getOrderUserId();
        BigDecimal amount = order.getAmount();
        log.info("订单金额： {}, {}", amount, order.getSubAmount());
        Date now = new Date();
        order.setUnsubscribeTime(now);
        if (distributorDetail != null) {
            if (amount.compareTo(BigDecimal.ZERO) != 0) {
                log.debug("===============查询子单详情===============");
                List<ChannelOrderDetail> refoudOrderDetail =channelOrderDetailMapper
                        .selectList(Wrappers.lambdaQuery(ChannelOrderDetail.class).eq(ChannelOrderDetail::getOrderId, id));
                List<Long> orderIdList = refoudOrderDetail.stream().map(ChannelOrderDetail::getId).collect(Collectors.toList());
                //获取营销活动流水,匹配活动id
                if (!orderIdList.isEmpty()){
                    log.debug("===============查询是否存在营销额流水===============");
                    LambdaQueryWrapper<CmsChannelMarketBillflow> queryWrapper = Wrappers.lambdaQuery(CmsChannelMarketBillflow.class)
                            .in(CmsChannelMarketBillflow::getOrderId, orderIdList);
                    List<CmsChannelMarketBillflow> billflow = cmsChannelMarketBillFlowMapper.selectList(queryWrapper);
                    if (!billflow.isEmpty()) {
                        log.debug("===============存在营销金额流水,开始营销额退款===============");
                        //退订营销活动金额-退订营销活动流水
                        changeMarketingAmount(order,distributorDetail.getCorpId(), billflow);
                        //计算营销额总额
                        BigDecimal totalAmount = billflow.stream().map(CmsChannelMarketBillflow::getAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
                        amount = amount.add(totalAmount);
                    }
                }
                if (amount.compareTo(BigDecimal.ZERO) != 0){
                    log.debug("渠道商，退还押金 orderId: {}, corpId: {}", id, orderUserId);
                    channelDistributorsService.changeSubDeposit(distributorDetail.getId(), amount, 1);
                    channelService.channelBillRecord(BillRecordVo.builder()
                            .corpId(distributorDetail.getCorpId())
                            //加油包退订 还是 套餐退订
                            .billType("7".equals(order.getOrderType()) ? "8" : "7")
                            .orderId(order.getOrderUniqueId())
                            .orderSubOrUnsubDate(now)
                            .amount(amount)
                            .cooperationMode("1")
                            .build());
                }
            }
            if (subChannelDistributorDetail != null && BigDecimal.ZERO.compareTo(order.getSubAmount()) != 0) {
                log.debug("子渠道商，退还押金 orderId: {}, corpId: {}", id, subChannelDistributorDetail.getCorpId());
                channelDistributorsService.changeSubDeposit(subChannelDistributorDetail.getId(), order.getSubAmount(), 1);
                channelService.channelBillRecord(BillRecordVo.builder()
                        .corpId(subChannelDistributorDetail.getCorpId())
                        //加油包退订 还是 套餐退订
                        .orderId(order.getOrderUniqueId())
                        .orderSubOrUnsubDate(now)
                        .billType("7".equals(order.getOrderType()) ? "8" : "7")
                        .amount(order.getSubAmount())
                        .cooperationMode("1")
                        .build());
            }
        } else {
            sendOrderUnsubscribeNotice(order);
        }
        log.debug("退款流程结束，订单id: {}", id);
    }

    private void changeMarketingAmount(ChannelOrder order,String cropId,List<CmsChannelMarketBillflow> billflow) {
        //过期时间初始化
        Date outTime =new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            outTime = sdf.parse("2099-12-31 23:59:59");
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

        //根据营销额流水开始退款
        Date finalOutTime = outTime;
        billflow.forEach(x->{
            BigDecimal billFlowAmount = x.getAmount().negate();
            CmsChannelMarketingRebate cmsChannelMarketingRebate;
            if (x.getRebateId() == null){
                cmsChannelMarketingRebate = cmsChannelMarketingRebateMapper.selectOne(Wrappers.lambdaQuery(CmsChannelMarketingRebate.class)
                        .eq(CmsChannelMarketingRebate::getCorpId, cropId)
                        //v6.10.2,新增rebateId用来校验流水和营销账户绑定
                        .eq(CmsChannelMarketingRebate::getActivityId, x.getActivityId()));
            }else {
                //查询营销账户信息
                cmsChannelMarketingRebate = cmsChannelMarketingRebateMapper.selectOne(Wrappers.lambdaQuery(CmsChannelMarketingRebate.class)
                        .eq(CmsChannelMarketingRebate::getCorpId, cropId)
                        //v6.10.2,新增rebateId用来校验流水和营销账户绑定
                        .eq(CmsChannelMarketingRebate::getId, x.getRebateId())
                        .eq(CmsChannelMarketingRebate::getActivityId, x.getActivityId()));
            }


            Long version = cmsChannelMarketingRebate.getVersion();

            //营销款是否过期 true过期 false未过期
            boolean isTimeOut = cmsChannelMarketingRebate.getExpiryTime().getTime() < System.currentTimeMillis();

            //查询渠道商所有活动未过期营销额总额
            List<CmsChannelMarketingRebate> rebate = cmsChannelMarketingRebateMapper.selectList(Wrappers.lambdaQuery(CmsChannelMarketingRebate.class)
                    .eq(CmsChannelMarketingRebate::getCorpId, cropId)
                    .ne(CmsChannelMarketingRebate::getRemainAmount, BigDecimal.ZERO)
                    .ge(CmsChannelMarketingRebate::getExpiryTime, new Date()));

            BigDecimal remainAmount =rebate.stream().map(CmsChannelMarketingRebate::getRemainAmount).reduce(BigDecimal.ZERO,BigDecimal::add);

            Date targetDate = new Date();
            //营销款过期,先执行清零操作,再执行退款操作
            if (isTimeOut){
                    targetDate = finalOutTime;
                //开始清零-若营销金额为0,则不写入清零流水
                if (cmsChannelMarketingRebate.getRemainAmount().compareTo(BigDecimal.ZERO) != 0){
                    //先查询当前渠道当前活动是否已存在退订流水
                    CmsChannelMarketBillflow cmsChannelMarketBillflow = cmsChannelMarketBillFlowMapper
                            .selectOne(new QueryWrapper<CmsChannelMarketBillflow>().lambda()
                                    .eq(CmsChannelMarketBillflow::getCorpId, cmsChannelMarketingRebate.getCorpId())
                                    .eq(CmsChannelMarketBillflow::getActivityId, cmsChannelMarketingRebate.getActivityId())
                                    .eq(CmsChannelMarketBillflow::getType, "6"));
                    if (cmsChannelMarketBillflow == null) {
                        //查找同一活动同一渠道商的其他营销账户数据
                        List<CmsChannelMarketingRebate> channelMarketingRebates1 = cmsChannelMarketingRebateMapper.selectList(Wrappers.lambdaQuery(CmsChannelMarketingRebate.class)
                                .eq(CmsChannelMarketingRebate::getCorpId, cropId)
                                .eq(CmsChannelMarketingRebate::getActivityId, cmsChannelMarketingRebate.getActivityId()));
                        BigDecimal remainAmount1 = channelMarketingRebates1.stream().map(CmsChannelMarketingRebate::getRemainAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

                        cmsChannelMarketBillFlowMapper.insert(CmsChannelMarketBillflow.builder()
                                .corpId(cmsChannelMarketingRebate.getCorpId())
                                .corpId(cropId)
                                .type("6")
                                .currencyCode(x.getCurrencyCode())
                                .amount(remainAmount1)
                                .deposit(BigDecimal.ZERO)
                                .activityId(cmsChannelMarketingRebate.getActivityId())
                                .createTime(new Date())
                                .totalAmount(remainAmount)
                                .totalOrderId("")
                                .build());
                    }
                }
                //营销账户退订,直接设置为流水金额
                int update =cmsChannelMarketingRebateMapper.update(null,Wrappers.lambdaUpdate(CmsChannelMarketingRebate.class)
                        .eq(CmsChannelMarketingRebate::getId,cmsChannelMarketingRebate.getId())
//                        .eq(CmsChannelMarketingRebate::getVersion,version)
//                        .set(CmsChannelMarketingRebate::getVersion,version + 1)
                        .set(CmsChannelMarketingRebate::getExpiryTime,targetDate)
                        .set(CmsChannelMarketingRebate::getRemainAmount,billFlowAmount));
                if (update <= 0){
                    throw new BizException("营销账户更新失败,请重试");
                }
                //退订营销额流水
                cmsChannelMarketBillFlowMapper.insert(CmsChannelMarketBillflow.builder()
                        .corpId(cropId)
                        .orderId(x.getOrderId())
                        .type("7".equals(order.getOrderType()) ? "5" : "4")
                        .currencyCode(x.getCurrencyCode())
                        .amount(billFlowAmount)
                        .deposit(billFlowAmount)
                        .activityId(x.getActivityId())
                        .createTime(new Date())
                        .totalAmount(remainAmount.add(billFlowAmount))
                        .totalOrderId(x.getTotalOrderId())
                        .build());
            }else {
                //查询当前活动该营销账户信息,计算总额
                List<CmsChannelMarketingRebate> rebateList = cmsChannelMarketingRebateMapper.selectList
                        (Wrappers.lambdaQuery(CmsChannelMarketingRebate.class)
                                .eq(CmsChannelMarketingRebate::getActivityId,x.getActivityId())
                                .eq(CmsChannelMarketingRebate::getCorpId,cropId)
                                .ge(CmsChannelMarketingRebate::getExpiryTime, new Date()));
                BigDecimal remainAmount1 = rebateList.stream().map(CmsChannelMarketingRebate::getRemainAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
                //未过期,正常更新营销账户信息
                LambdaUpdateWrapper<CmsChannelMarketingRebate> updateWrapper = Wrappers.lambdaUpdate(CmsChannelMarketingRebate.class)
                        .eq(CmsChannelMarketingRebate::getId,cmsChannelMarketingRebate.getId())
//                        .eq(CmsChannelMarketingRebate::getVersion, version)
//                        .set(CmsChannelMarketingRebate::getVersion, version + 1)
                        .setSql("remain_amount = remain_amount"+"+"+billFlowAmount);
                int update = cmsChannelMarketingRebateMapper.update(cmsChannelMarketingRebate, updateWrapper);
                if (update <= 0){
                    throw new BizException("更新营销账户信息失败");
                }
                //退订营销额流水
                cmsChannelMarketBillFlowMapper.insert(CmsChannelMarketBillflow.builder()
                        .corpId(cropId)
                        .orderId(x.getOrderId())
                        .type("7".equals(order.getOrderType()) ? "5" : "4")
                        .currencyCode(x.getCurrencyCode())
                        .amount(billFlowAmount)
                        .deposit(remainAmount1.add(billFlowAmount))
                        .activityId(x.getActivityId())
                        .createTime(new Date())
                        .totalAmount(remainAmount.add(billFlowAmount))
                        .totalOrderId(x.getTotalOrderId())
                        .build());
            }
        });
    }

    /**
     * 子订单退款流程
     *
     * @param orderDetail       子订单
     * @param distributorDetail 渠道商详情
     */
    private void refundOrderDetail(ChannelOrder order, ChannelOrderDetail orderDetail, ChannelDistributorDetail
            distributorDetail) {
        Long id = orderDetail.getId();
        log.debug("进入退款流程，子订单id: {}", id);
        String orderUserId = order.getOrderUserId();
        BigDecimal amount = orderDetail.getAmount();
        Date now = new Date();
        order.setUnsubscribeTime(now);
        if (distributorDetail != null) {
            //获取营销活动流水,匹配活动id
            LambdaQueryWrapper<CmsChannelMarketBillflow> queryWrapper = Wrappers.lambdaQuery(CmsChannelMarketBillflow.class)
                    .eq(CmsChannelMarketBillflow::getOrderId, orderDetail.getId());
            List<CmsChannelMarketBillflow> billflowList = cmsChannelMarketBillFlowMapper.selectList(queryWrapper);
            if (amount.compareTo(BigDecimal.ZERO) != 0) {
                log.debug("存在营销金额流水,渠道商退还营销额 orderId: {}, corpId: {}", id, orderUserId);
                if (billflowList != null) {
                    //退订营销活动金额-退订营销活动流水
                    changeMarketingAmount(order,distributorDetail.getCorpId(), billflowList);
                    //计算营销额总额
                    BigDecimal totalAmount = billflowList.stream().map(CmsChannelMarketBillflow::getAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
                    amount = amount.add(totalAmount);
                }
                if (amount.compareTo(BigDecimal.ZERO) != 0){
                    log.debug("渠道商，退还押金 orderId: {}, corpId: {}", id, orderUserId);
                    channelDistributorsService.changeSubDeposit(distributorDetail.getId(), amount, 1);
                    channelService.channelBillRecord(BillRecordVo.builder()
                            .corpId(distributorDetail.getCorpId())
                            //加油包退订 还是 套餐退订
                            .billType("7".equals(order.getOrderType()) ? "8" : "7")
                            .orderSubOrUnsubDate(now)
                            .orderId(order.getOrderUniqueId())
                            .amount(amount)
                            .build());
                }
            }
            if (StringUtils.isNotBlank(orderDetail.getSubCorpId())
                    && BigDecimal.ZERO.compareTo(orderDetail.getSubAmount()) != 0) {
                ChannelDistributorDetail subChannelDistributorDetail = distributorDetailMapper.
                        selectChannelDetail(orderDetail.getSubCorpId());
                log.debug("子渠道商，退还押金 orderId: {}, corpId: {}", id, subChannelDistributorDetail.getCorpId());
                channelDistributorsService.changeSubDeposit(subChannelDistributorDetail.getId(), order.getSubAmount(), 1);
                channelService.channelBillRecord(BillRecordVo.builder()
                        .corpId(subChannelDistributorDetail.getCorpId())
                        //加油包退订 还是 套餐退订
                        .billType("7".equals(order.getOrderType()) ? "8" : "7")
                        .orderSubOrUnsubDate(now)
                        .orderId(order.getOrderUniqueId())
                        .amount(orderDetail.getSubAmount())
                        .build());
            }
        } else {
            sendOrderDetailUnsubscribeNotice(order, Collections.singletonList(orderDetail));
        }
        log.debug("退款流程结束，子订单id: {}", id);
    }

    private void sendOrderUnsubscribeNotice(ChannelOrder order) {
        String corpId = noticeConfigProperties.getCorpId();
        log.debug("个人用户/其它用户，进行退订通知 corpId: {}", corpId);
        final EopAccessDetail eopAccessDetail = eopAccessDetailMapper.selectOne
                (Wrappers.lambdaQuery(EopAccessDetail.class)
                        .eq(EopAccessDetail::getCorpId, corpId));
        if (eopAccessDetail == null) {
            log.warn("未查询到能力接入信息数据 corpId: {}", corpId);
            return;
        }
        try {
            unsubscribeNotifyService.notify(UnsubscribeNotifyReq.builder()
                    .unsubscribeType(UnsubscribeNotifyReq.UnsubscirbeTypeEnum.ALL.getValue())
                    .notifyUrl(eopAccessDetail.getNotifyUrl())
                    .orderID(order.getOrderUniqueId())
                    .num(order.getCount().toString())
                    .priceObj(PriceInfo.builder()
                            .price(order.getAmount().toString())
                            .currencyCode(order.getCurrencyCode()).build())
                    .appKey(eopAccessDetail.getAppKey())
                    .appSecret(eopAccessDetail.getAppSecret())
                    .build());
        } catch (Exception e) {
            log.warn("e", e);
            throw new BizException(ApiResponseEnum.ORDER_NOTIFY_ERROR);
        }

    }

    private void sendOrderDetailUnsubscribeNotice(ChannelOrder order, List<ChannelOrderDetail> orderDetails) {
        String corpId = noticeConfigProperties.getCorpId();
        log.debug("个人用户/其它用户，进行退订通知 corpId: {}", corpId);
        final EopAccessDetail eopAccessDetail = eopAccessDetailMapper.selectOne
                (Wrappers.lambdaQuery(EopAccessDetail.class)
                        .eq(EopAccessDetail::getCorpId, corpId));
        if (eopAccessDetail == null) {
            log.warn("未查询到能力接入信息数据 corpId: {}", corpId);
            return;
        }
        try {
            final UnsubscribeNotifyReq notifyReq = UnsubscribeNotifyReq.builder()
                    .unsubscribeType(UnsubscribeNotifyReq.UnsubscirbeTypeEnum.PARTIAL.getValue())
                    .notifyUrl(eopAccessDetail.getNotifyUrl())
                    .iccid(orderDetails.stream().map(ChannelOrderDetail::getIccid).collect(Collectors.toList()))
                    .orderID(order.getOrderUniqueId())
                    .packageID(orderDetails.get(0).getPackageId())
                    .priceObj(PriceInfo.builder()
                            .price(orderDetails.get(0).getAmount().multiply(BigDecimal.valueOf(orderDetails.size())).toString())
                            .currencyCode(orderDetails.get(0).getCurrencyCode())
                            .build())
                    .appKey(eopAccessDetail.getAppKey())
                    .appSecret(eopAccessDetail.getAppSecret())
                    .build();
            unsubscribeNotifyService.notify(notifyReq);
        } catch (Exception e) {
            log.warn("e", e);
            throw new BizException(ApiResponseEnum.ORDER_NOTIFY_ERROR);
        }
    }

    /**
     * 加油包退订写表流程
     *
     * @param order
     */
    public void refundRefuelPackage(ChannelOrder order, List<ChannelPackageCard> channelPackageCardList) {
        log.debug("加油包退订，写表流程, 更新订单所属加油包状态");
        //当前时间>activeTime ,则 已使用 当前时间< activeTime , 则 删除。
        ChannelOrderDetail orderDetailToUpdate = new ChannelOrderDetail();
        orderDetailToUpdate.setUnsubscribeTime(order.getUnsubscribeTime() == null ? new Date() : order.getUnsubscribeTime());
        orderDetailToUpdate.setUnsubscribeChannel(UnsubscribeChannelEnum.CUSTOMER_WEBSITE.getK());
        orderDetailToUpdate.setOrderStatus(ChannelOrder.OrderStatusEnum.UNSUBSCRIBE.getValue());
        for (ChannelPackageCard channelPackageCard : channelPackageCardList) {
            if (channelPackageCard.getActiveTime().after(new Date())) {
                log.debug("当前时间 < activeTime, 删除卡与套餐表此条记录， 更改子单表 未激活");
                channelPackageCardMapper.deleteById(channelPackageCard.getId());
                orderDetailToUpdate.setPackageStatus(PackageStatusEnum.UNACTIVATED.getStatus());
            } else {
                log.debug("当前时间 > activeTime , 更改子单表为 已激活 或者 已使用");
                if (channelPackageCard.getExpireTime().after(new Date()) &&
                        PackageStatusEnum.ACTIVATED.getStatus().equals(channelPackageCard.getPackageStatus())) {
                    log.debug("当前时间 < expireTime 且目前状态已激活, 子单表已激活");
                    orderDetailToUpdate.setPackageStatus(PackageStatusEnum.ACTIVATED.getStatus());
                } else {
                    log.debug("当前时间 > expireTime 或者目前状态已使用, 子单表已使用");
                    orderDetailToUpdate.setPackageStatus(PackageStatusEnum.USED.getStatus());
                }
                channelPackageCard.setPackageStatus(ChannelPackageCard.PackageStatusEnum.USED.getValue());
                channelPackageCard.setExpireTime(new Date());
                channelPackageCardMapper.updateById(channelPackageCard);
            }
            channelOrderDetailMapper.update(orderDetailToUpdate, Wrappers.<ChannelOrderDetail>lambdaUpdate()
                    .eq(ChannelOrderDetail::getPackageUniqueId, channelPackageCard.getPackageUniqueId()));
        }
        Long orderId = order.getId();
        log.debug("更新总订单状态为已退订 orderId: {}", orderId);
        ChannelOrder orderToUpdate = new ChannelOrder();
        orderToUpdate.setId(orderId);
        orderToUpdate.setOrderStatus(ChannelOrder.OrderStatusEnum.UNSUBSCRIBE.getValue());
        channelOrderMapper.updateById(orderToUpdate);
    }

    /**
     * lua脚本阻塞redis.*
     *
     * @return
     */
    public String refuelLua(int refuelNumber) {
        //加油包数量为1时，只扣当前周期流量
        if (refuelNumber == 1) {
            return "local res = {}\n" +
                    "local left  = KEYS[1] .. KEYS[2] \n" +
                    "local value  = ARGV[1]\n" +
                    "\n" +
                    "if( 1 == redis.call('EXISTS',left) )\n" +
                    "then\n" +
                    "res[1] = redis.call('DECRBY',left,value)\n" +
                    "if (res[1] <= 0) " +
                    "then " +
                    "redis.call('DEL', left) " +
                    "end " +
                    "end\n" +
                    "\n" +
                    "return res";
        }
        return "local res = {}\n" +
                "res[1] = redis.call('DECRBY', KEYS[1]..KEYS[2], ARGV[1]) " +
                "if(res[1] <= 0) " +
                "then " +
                "redis.call('DEL', KEYS[1]..KEYS[2]) " +
                "end " +
                "for i = KEYS[2]+1, ARGV[2] do " +
                "local x " +
                "x = redis.call('DECRBY', KEYS[1]..i, ARGV[1]) " +
                "if(x <= 0) " +
                "then " +
                "redis.call('DEL', KEYS[1]..i) " +
                "end " +
                "end " +
                "return res";
    }

    public String refuelCycleLua() {
        return "local res = {}\n" +
                "local left  = KEYS[1]\n" +
                "local value  = ARGV[1]\n" +
                "\n" +
                "if( 1 == redis.call('EXISTS',left) )\n" +
                "then\n" +
                "res[1] = redis.call('DECRBY',left,value)\n" +
                "if(res[1] <= 0) " +
                "then " +
                "redis.call('DEL', left) " +
                "end " +
                "\n" +
                "end\n" +
                "\n" +
                "return res";
    }

    /**
     * 获取单日限量流量返回值
     *
     * @param now
     * @param packageCard
     * @param flowCount
     * @param refuelNumber
     * @return
     */
    public List<BigDecimal> getDayPresentTrafficValue(Date now, ChannelPackageCard packageCard, Long flowCount, int refuelNumber) {
        log.debug("此加油包绑定得套餐周期类型为单日限量,确定目前正处于哪一个周期");
//        String periodUnitType;
//        //确定套餐周期类型
//        if (PackageVO.PeriodUnitEnum.HOURS.getValue().equals(packageCard.getPeriodUnit())) {
//            periodUnitType = PackageVO.PeriodUnitEnum.HOURS.getValue();
//        } else if (PackageVO.PeriodUnitEnum.HOURS.getValue().equals(packageCard.getPeriodUnit())){
//            periodUnitType = PackageVO.PeriodUnitEnum.DAY.getValue();
//        }
        //计算index的值
        long keyIndex = Utils.getIndex(packageCard.getPeriodUnit(), packageCard.getActiveTime(),now);
//        if (Objects.nonNull(packageCard.getActiveTime()) && PackageVO.PeriodUnitEnum.DAY.getValue().equals(periodUnitType)) {
//            log.debug("获取套餐单日流量剩余key，单周期类型为自然日");
//            String activeTimeStr = DateUtil.format(packageCard.getActiveTime(), "yyyyMMdd");
//            keyIndex = now.compareTo(packageCard.getActiveTime()) >= 0 ?
//                    new BigDecimal(DateUtil.between(now, DateUtil.parse(activeTimeStr), DateUnit.MS))
//                            .divide(new BigDecimal("86400000"), RoundingMode.FLOOR)
//                            .setScale(0, RoundingMode.DOWN).longValue() : 0;
//        } else {
//            log.debug("获取套餐单日流量剩余key，单周期类型为24小时");
//            keyIndex = now.compareTo(packageCard.getActiveTime()) >= 0 ?
//                    new BigDecimal(DateUtil.between(now, packageCard.getActiveTime(), DateUnit.MS))
//                            .divide(new BigDecimal("86400000"), RoundingMode.FLOOR)
//                            .setScale(0, RoundingMode.DOWN).longValue() : 0;
//        }

        SingleDayPackageDeductContext deductContext = SingleDayPackageDeductContext.builder()
                .flowCount(flowCount)
                .index(keyIndex)
                .isDecrby(true)
                .packageUniqueId(packageCard.getPackageUniqueId())
                .recordUsedFlow(false)
                .build();

        BigDecimal addPackageFlow = flowOperationUtils.singleDayPackageAndAddFlowDeduct(deductContext, CmsPackageDayRemain.FlowTypeEnum.ADD_REMAIN.getValue());
        List<BigDecimal> res = new ArrayList<>();
        BigDecimal dayRemainFlow = flowOperationUtils.getDayRemainFlow(packageCard.getPackageUniqueId(), CmsPackageDayRemain.FlowTypeEnum.PACKAGE_REMAIN.getValue(), keyIndex);
        res.add(addPackageFlow);
        res.add(dayRemainFlow);
        for (int i = 1; i < refuelNumber; i++) {
            keyIndex++;
            if (keyIndex < refuelNumber){
                deductContext.setIndex(keyIndex);
                flowOperationUtils.singleDayPackageAndAddFlowDeduct(deductContext, CmsPackageDayRemain.FlowTypeEnum.ADD_REMAIN.getValue());
            }
//            boolean b = flowOperationUtils.addPackageDayDeduct(packageCard.getPackageUniqueId(), keyIndex, -1 * flowCount, CmsPackageDayRemain.FlowTypeEnum.ADD_REMAIN.getValue());
//            if (!addPackageFlow1.equals(BigDecimal.ZERO)) {
//                break;
//            }
        }

        return res;
    }

    /**
     * 获取周期限量流量扣除返回值
     *
     * @param flowCount
     * @param packageCard
     * @return
     */
    private List<BigDecimal> getCyclePresentTrafficValue(Long flowCount, ChannelPackageCard packageCard) {
        log.debug("此加油包绑定得套餐周期类型为周期内限量,进行总流量扣除");
        String cyclePackageKey = packageRemain.concat(packageCard.getPackageUniqueId());
        log.debug("套餐redisKey: {}", cyclePackageKey);
        List<BigDecimal> res = new ArrayList<>();
        SingleCyclePackageDeductContext deductContext = SingleCyclePackageDeductContext.builder()
                .flowCount(flowCount)
                .isDecrby(true)
                .recordUsedFlow(false)
                .packageUniqueId(packageCard.getPackageUniqueId())
                .build();
        BigDecimal addPackageFlow = flowOperationUtils.singleCyclePackageAndAddFlowDeduct(deductContext, CmsPackageCycleRemain.FlowTypeEnum.ADD_CYCLE_REMAIN.getValue());
        res.add(addPackageFlow);
        BigDecimal packageFlow = flowOperationUtils.getCycleRemainFlow(packageCard.getPackageUniqueId(), CmsPackageCycleRemain.FlowTypeEnum.PACKAGE_CYCLE_REMAIN.getValue());
        res.add(packageFlow);
        return res;
    }


    /**
     * 达量处理扔队列
     *
     * @param himsi
     * @param limitType
     * @param periodUnitType
     * @param packageCard
     */
    private void packageProcessingOfVolume(String himsi, String limitType, String periodUnitType, ChannelPackageCard packageCard) {
        String volumeStatus = packageCard.getControlLogic();
        List<ChannelSurfInfo> channelSurfInfos = channelSurfInfoMapper.selectInfoListAndImsiDistinctByHImsi(himsi, packageCard.getPackageUniqueId());
        log.debug("[达量处理]获取上网信息列表，将进行循环入队，执行个数为：{}", channelSurfInfos.size());
        //排序
        channelSurfInfos.sort(Comparator.comparing(obj -> ((ChannelSurfInfo) obj).getInternetType()).reversed());
        for (ChannelSurfInfo info : channelSurfInfos) {
            //H卡时取签约模板需要区分H卡上网还是V卡上网
            String msisdn = null;
            String iccid = null;
            String imsiToH = info.getHimsi();
            if (ImsiType.HIMSI.getK().equals(info.getInternetType())) {
                log.debug("[达量处理][信息查询][H卡]查询H卡信息，IMSI: {}", info.getImsi());
                Response<CardVO> vResp4Hcard = pmsFeignClient.getOneCard(info.getImsi());
                if (!ResponseResult.SUCCESS.getCode().equals(vResp4Hcard.getCode()) || ObjectUtil.isNull(vResp4Hcard.getData())) {
                    log.error("[达量处理][信息查询][H卡]查询H卡信息失败，该卡不进行入队，IMSI：{}", info.getImsi());
                    continue;
                }
                CardVO cardVO = vResp4Hcard.getData();
                msisdn = cardVO.getMsisdn();
                iccid = cardVO.getIccid();
                imsiToH = cardVO.getImsi();
            }
            if (ImsiType.VIMSI.getK().equals(info.getInternetType())) {
                log.debug("[达量处理][信息查询][V卡]查询V卡信息，IMSI: {}", info.getImsi());
                Response<VcardInfo> vResp4Vcard = pmsFeignClient.getVcardAccountInfo(info.getImsi());
                if (!ResponseResult.SUCCESS.getCode().equals(vResp4Vcard.getCode()) || ObjectUtil.isNull(vResp4Vcard.getData())) {
                    log.error("[达量处理][信息查询][V卡]查询V卡信息失败，该卡不进行入队，IMSI：{}", info.getImsi());
                    continue;
                }
                VcardInfo vcardInfo = vResp4Vcard.getData();
                msisdn = vcardInfo.getMsisdn();
                iccid = vcardInfo.getIccid();
            }
            //普通套餐类型下-周期内-达量限速/普通套餐类型下-单日限量
            if (PackageVO.flowLimitTypeEnum.DAY.getValue().equals(limitType) ||
                    (PackageVO.flowLimitTypeEnum.CYCLE.getValue().equals(limitType) &&
                            ChannelPackageCard.ControlLogicEnum.SPEED_LIMIT.getValue().equals(volumeStatus))) {
                volumeStatus = ChannelPackageCard.ControlLogicEnum.SPEED_LIMIT.getValue();
                log.debug("[达量处理][达量限速/释放]入rabibitmq达量处理队列：达量限速");
            }
            //终端线上类型/普通套餐类型下-周期内-达量释放
            if (PackageType.TERMINAL_ONLINE_CAEDPOOL.getType().equals(packageCard.getPackageType()) ||
                    (PackageVO.flowLimitTypeEnum.CYCLE.getValue().equals(limitType) &&
                            ChannelPackageCard.ControlLogicEnum.REACH_RELEASE.getValue().equals(volumeStatus))) {
                //达量释放
                volumeStatus = ChannelPackageCard.ControlLogicEnum.REACH_RELEASE.getValue();
                log.debug("[达量处理][达量限速/释放]入rabibitmq达量处理队列：达量释放");
            }
            String reachingJson = JSONObject.toJSONString(
                    ReachingTreatmentVO.builder()
                            .imsi(info.getImsi())
                            .msisdn(msisdn)
                            .iccid(iccid)
                            .himsi(imsiToH)
                            .cardType(info.getInternetType())
                            .packageUniqueId(info.getPackageUniqueId())
                            .upccLimitsSignId(packageCard.getSlowSpeedSignBizId())
                            .logic(volumeStatus)
                            .appGroupId(Collections.singletonList("123456"))
                            .build()
            );
            log.debug("[达量处理][达量限速/释放]入rabibitmq达量处理队列，mqJson: {}", reachingJson);
            sendMessageWrapper.throwMessageToQueue(
                    reachingJson, QueueEnum.ReachingTreatment);
            //单日恢复延时消息队列
            boolean isNeedRecover = PackageVO.flowLimitTypeEnum.DAY.getValue().equals(limitType) &&
                    !redisUtil.hasKey(String.format(BizConstants.PACKAGE_RECOVER_KEY, packageCard.getPackageUniqueId()));
            if (isNeedRecover) {
                //24小时 毫秒
                long effectiveTimeMS = Utils.resumeDelayQueueEffectiveTimeCount(periodUnitType,packageCard.getActiveTime());
//                Date nowTime = new Date();
//                Calendar cal = Calendar.getInstance();
//                if (PackageVO.PeriodUnitEnum.DAY.getValue().equals(periodUnitType) ||
//                        PackageVO.PeriodUnitEnum.MONTH.getValue().equals(periodUnitType) ||
//                        PackageVO.PeriodUnitEnum.YEAR.getValue().equals(periodUnitType)) {
//                    log.debug("[达量处理][单日恢复延时][自然日]入rabbitmq单日恢复延时消息队列，时间差计算，当前执行时间：{} ms", nowTime);
//                    //设置起时间
//                    cal.setTime(nowTime);
//                    //增加一天
//                    cal.add(Calendar.DATE, 1);
//                    //自然日 毫秒
//                    effectiveTimeMS = singleDayDelay == -1 ?
//                            DateUtil.between(nowTime, DateUtil.beginOfDay(cal.getTime()), DateUnit.MS) : singleDayDelay;
//                } else {
//                    log.debug("[达量处理][单日恢复延时][24H]入rabbitmq单日恢复延时消息队列，时间差计算，当前执行时间：{} ms", nowTime);
//                    //设置起时间
//                    cal.setTime(packageCard.getActiveTime());
//                    int x = (int) DateUtil.between(nowTime, packageCard.getActiveTime(), DateUnit.DAY);
//                    cal.add(Calendar.DATE, x);
//                    cal.add(Calendar.MILLISECOND, Math.toIntExact(twentyFourHours));
//                    log.debug("配置的延时时间为：{}", twentyFourHours);
//                    effectiveTimeMS = DateUtil.between(nowTime, cal.getTime(), DateUnit.MS);
//                }
                //单日恢复延时消息队列
                CmsPackageCardUpccRelation cmsPackageCardUpccRelation = cmsPackageCardUpccRelationMapper.selectOne(Wrappers.lambdaQuery(CmsPackageCardUpccRelation.class)
                        .eq(CmsPackageCardUpccRelation::getPackageUniqueId, packageCard.getPackageUniqueId())
                        .orderByAsc(CmsPackageCardUpccRelation::getConsumption));
                String singleDelayJson = JSONObject.toJSONString(
                        SingleDayDelayVO.builder()
                                .imsi(info.getImsi())
                                .msisdn(msisdn)
                                .iccid(iccid)
                                .himsi(imsiToH)
                                .cardType(info.getInternetType())
                                .packageUniqueId(info.getPackageUniqueId())
                                .signUpccId(cmsPackageCardUpccRelation.getUpccSignId())
                                .build()
                );
                log.debug("[达量处理][单日恢复延时]入rabibitmq单日恢复延时消息队列，延迟时间：{} ms，mqJson: {}",
                        effectiveTimeMS, singleDelayJson);
                //有效期为当前自然日/24小时（根据套餐周期类型）结束时间
                sendMessageWrapper.throwMessageToQueue(
                        singleDelayJson, QueueEnum.ResumeDelayQueue, effectiveTimeMS);
            }
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public Response<Void> unsubscribeOrderForApi(ChannelUnsubscribeReq channelUnsubscribeReq) {
        log.debug("渠道商退订收到请求：{}", channelUnsubscribeReq);
        String id = channelUnsubscribeReq.getOrderId();
        String corpId = channelUnsubscribeReq.getCorpId();
        Channel channel = channelMapper.selectOne(Wrappers.lambdaQuery(Channel.class)
                .eq(Channel::getCorpId, corpId));
        List<String> corpIdList;
        boolean isSubChannel = false;
        if (channel == null) {
            log.debug("未找到一级渠道商，查询是否是零级渠道商");
            CmsTopchannel cmsTopchannel = cmsTopchannelMapper.selectOne(Wrappers.lambdaQuery(CmsTopchannel.class)
                    .eq(CmsTopchannel::getCorpId, corpId));
            Optional.ofNullable(cmsTopchannel).orElseThrow(() -> new BizException("数据异常,未找到渠道商"));
            List<CmsChannelRelation> cmsChannelRelations = cmsChannelRelationMapper.selectList(Wrappers.lambdaQuery(CmsChannelRelation.class)
                    .eq(CmsChannelRelation::getParentCorpId, corpId));
            corpIdList = cmsChannelRelations.stream().map(CmsChannelRelation::getCorpId).collect(Collectors.toList());
        } else {
            log.debug("确认一级渠道商还是子渠道商");
            if (channel.getParentCorpId() != null && Channel.ChannelTypeEnum.SUBCHANNEL.getValue().equals(channel.getType())) {
                log.debug("子渠道商退订");
                isSubChannel = true;
                corpIdList = Collections.singletonList(channel.getParentCorpId());
            } else {
                log.debug("一级渠道商退订");
                corpIdList = new ArrayList<>();
                corpIdList.add(corpId);
                List<Channel> channels = channelMapper.selectList(Wrappers.lambdaQuery(Channel.class)
                        .eq(Channel::getParentCorpId, corpId));
                if (!CollectionUtils.isEmpty(channels)) {
                    corpIdList.addAll(channels.stream().map(Channel::getCorpId).collect(Collectors.toList()));
                }
            }
        }
        //外部API，只能退订套餐。可以抽出来，但是我觉得抽出来还得测试原来的接口，太麻烦了，后来人要改的话希望别忘记这个接口。
        //注意外部API交互的是订单唯一ID
        ChannelOrder order;
        if (org.springframework.util.StringUtils.hasText(id)) {
            order = channelOrderMapper.selectOne(Wrappers.lambdaQuery(ChannelOrder.class)
                    .eq(ChannelOrder::getOrderUniqueId, id));
            if (order != null) {
                log.debug("查到普通订单，校验此渠道商是否能操作此订单");
                if (isSubChannel) {
                    if (!corpId.equals(order.getSubCorpId())) {
                        return Response.error(ApiResponseEnum.ORDER_NOT_FOUND);
                    }
                } else {
                    if (!corpIdList.contains(order.getOrderUserId())) {
                        return Response.error(ApiResponseEnum.ORDER_NOT_FOUND);
                    }
                }
            } else {
                log.info("普通订单查询失败，查询绑定订单");
                ChannelBind channelBind = channelBindMapper.selectOne(Wrappers.lambdaQuery(ChannelBind.class)
                        .eq(ChannelBind::getOrderUniqueId, id));
                if (channelBind == null) {
                    return Response.error(ApiResponseEnum.ORDER_NOT_FOUND);
                }
                log.info("查到绑定订单，校验此渠道商是否能操作此订单");
                if (isSubChannel) {
                    if (!corpId.equals(channelBind.getCorpId())) {
                        return Response.error(ApiResponseEnum.ORDER_NOT_FOUND);
                    }
                } else {
                    if (!corpIdList.contains(channelBind.getCorpId())) {
                        return Response.error(ApiResponseEnum.ORDER_NOT_FOUND);
                    }
                }

                channelBindMapper.delete(Wrappers.lambdaQuery(ChannelBind.class)
                        .eq(ChannelBind::getOrderUniqueId, id));
                return Response.okForApi("0000000");
            }
        } else {
            List<ChannelOrder> channelOrders = channelOrderMapper.selectList(Wrappers.lambdaQuery(ChannelOrder.class)
                    .eq(ChannelOrder::getThirdOrderId, channelUnsubscribeReq.getThirdOrderId()));
            if (CollectionUtils.isEmpty(channelOrders)) {
                log.info("普通订单查询失败，查询绑定订单");
                List<ChannelBind> channelBinds = channelBindMapper.selectList(Wrappers.lambdaQuery(ChannelBind.class)
                        .eq(ChannelBind::getThirdOrderId, channelUnsubscribeReq.getThirdOrderId()));
                if (CollectionUtils.isEmpty(channelBinds)) {
                    return Response.error(ApiResponseEnum.ORDER_NOT_FOUND);
                }
                if (channelBinds.size() > 1) {
                    return Response.error(ApiResponseEnum.ORDER_NOT_ALLOWED_UNS);
                }
                if (isSubChannel) {
                    if (!corpId.equals(channelBinds.get(0).getCorpId())) {
                        return Response.error(ApiResponseEnum.ORDER_NOT_FOUND);
                    }
                } else {
                    if (!corpIdList.contains(channelBinds.get(0).getCorpId())) {
                        return Response.error(ApiResponseEnum.ORDER_NOT_FOUND);
                    }
                }
                channelBindMapper.delete(Wrappers.lambdaQuery(ChannelBind.class)
                        .eq(ChannelBind::getThirdOrderId, channelUnsubscribeReq.getThirdOrderId())
                        .in(ChannelBind::getCorpId, corpIdList));
                return Response.okForApi("0000000");
            } else {
                if (channelOrders.size() > 1) {
                    return Response.error(ApiResponseEnum.ORDER_NOT_ALLOWED_UNS);
                }
                order = channelOrders.get(0);
                if (isSubChannel) {
                    if (!corpId.equals(order.getSubCorpId())) {
                        return Response.error(ApiResponseEnum.ORDER_NOT_FOUND);
                    }
                } else {
                    if (!corpIdList.contains(order.getOrderUserId())) {
                        return Response.error(ApiResponseEnum.ORDER_NOT_FOUND);
                    }
                }
            }
        }
        try {
            //走到这确定一定有订单了,加分布锁。
            if (!redissonLock.tryLock(order.getId().toString(), 30)) {
                return Response.error(ApiResponseEnum.ACQUIRE_LOCK_FAILED);
            }
            ChannelDistributorDetail channelDistributorDetail = distributorDetailMapper.selectChannelDetail(order.getOrderUserId());
            if (channelDistributorDetail == null) {
                return Response.error(ApiResponseEnum.EBSCODE_ERROR);
            }
            String unsubscribeRule = channelDistributorDetail.getUnsubscribeRule();
            Date orderDate = order.getOrderDate();
            if ("1".equals(unsubscribeRule)) {
                //时间类懒得转化了,主要没时间，可以先转化为Calendar
                int dateMonth = orderDate.getMonth();
                int newMonth = new Date().getMonth();
                if (dateMonth != newMonth) {
                    return Response.error(ApiResponseEnum.MONTH_NOT_MATCH);
                }
            }
            if (!ChannelOrder.orderTypeEnum.PACKAGE.getValue().equals(order.getOrderType())) {
                return Response.error(ApiResponseEnum.ORDER_TYPE_ERROR);
            }
            String orderStatus = order.getOrderStatus();
            if (!ChannelOrder.OrderStatusEnum.FINISHED.getValue().equals(orderStatus)) {
                log.debug("orderStatus: {}", orderStatus);
                return Response.error(ApiResponseEnum.ORDER_STATUS_ERROR);
            }
            Integer unfinishedCount = channelOrderDetailMapper.selectCount(Wrappers.lambdaQuery(ChannelOrderDetail.class)
                    .eq(ChannelOrderDetail::getOrderId, order.getId())
                    .ne(ChannelOrderDetail::getOrderStatus, ChannelOrder.OrderStatusEnum.FINISHED.getValue()));
            if (unfinishedCount > 0) {
                return Response.error(ApiResponseEnum.SUBLIST_STATUS_NOT_UNIFICATION);
            }
            List<String> packageUniqueIds = getPackageUniqueIdsByOrderId(order.getId());
            List<String> packageStatusList = channelPackageCardMapper.selectList(Wrappers.lambdaQuery(ChannelPackageCard.class)
                            .select(ChannelPackageCard::getPackageStatus)
                            .in(ChannelPackageCard::getPackageUniqueId, packageUniqueIds)
                            .groupBy(ChannelPackageCard::getPackageStatus))
                    .stream()
                    .map(ChannelPackageCard::getPackageStatus)
                    .collect(Collectors.toList());
            int size = packageStatusList.size();
            if (size > 1) {
                log.debug("套餐状态不统一，不允许全部退订");
                return Response.error(ApiResponseEnum.PACKAGE_STATUS_NOT_UNIFICATION);
            }
            if (size == 1) {
                String packageStatus = packageStatusList.iterator().next();
                final String orderUserId = order.getOrderUserId();
                ChannelDistributorDetail distributorDetail;
                try {
                    distributorDetail = getChannelDistributorDetail(orderUserId, order.getCurrencyCode());
                } catch (BizException e) {
                    return Response.error(ApiResponseEnum.CURRENCY_NOT_FIT);
                }
                if (PackageStatusEnum.UNACTIVATED.getStatus().equals(packageStatus)) {
                    final Integer count = channelPackageCardMapper.selectCount(Wrappers.lambdaQuery(ChannelPackageCard.class)
                            .in(ChannelPackageCard::getPackageUniqueId, packageUniqueIds)
                            .eq(ChannelPackageCard::getPackageStatus, packageStatus)
                            .lt(ChannelPackageCard::getEffectiveDay, new Date()));
                    final int num = packageUniqueIds.size();
                    log.debug("套餐个数: {}", num);
                    if (count > 0 && count < num) {
                        return Response.error(ApiResponseEnum.PACKAHE_NUM_ERROR);
                    }
                    if (count == 0) {
                        try {
                            ChannelDistributorDetail subChannelDistributorDetail = null;
                            if (order.getSubCorpId() != null) {
                                subChannelDistributorDetail = distributorDetailMapper.selectChannelDetail(order.getSubCorpId());
                            }
                            inactivatedPackageUnsubAll(order, packageUniqueIds, distributorDetail, true, NotifyTypeEnum.NON_ASYNC_NOTIFY.getType(), subChannelDistributorDetail);
                        } catch (BizException e) {
                            ApiResponseEnum.matchCode(e.getCode());
                        }
                    }
                } else {
                    return Response.error(ApiResponseEnum.PACKAGE_NOT_WAITACTIVED);
                }
            }
            return Response.okForApi("0000000");
        } finally {
            if (redissonLock.isHeldByCurrentThread(order.getId().toString())) {
                redissonLock.unlock(order.getId().toString());
            }
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void unsubscribeForSmallOrder(H5UnsubscribeReq h5UnsubscribeReq, ChannelOrder channelOrder, List<ChannelOrderDetail> chosenOrderDetails) {
        List<String> packageUniqueIds = chosenOrderDetails.stream().map(ChannelOrderDetail::getPackageUniqueId).collect(Collectors.toList());
        log.debug("需要退订得套餐唯一id集合：{}, 加分布式锁", packageUniqueIds);
        if (!redissonLock.tryLockMulti(packageUniqueIds)) {
            log.warn("分布式锁获取失败 packageUniqueIds: {}", packageUniqueIds);
            throw new BizException(ApiResponseEnum.ACQUIRE_LOCK_FAILED);
        }
        try {
            final boolean isCardAndPackage = updateChannelOrderDetail(channelOrder, chosenOrderDetails, true);
            ChannelOrder orderToUpdate = new ChannelOrder();
            orderToUpdate.setAsyncNotifyType(NotifyTypeEnum.NON_ASYNC_NOTIFY.getType());
            if (isCardAndPackage) {
                String orderIccid = channelOrder.getIccid();
                if (StringUtils.isNotBlank(orderIccid)) {
                    List<String> iccids = Arrays.stream(orderIccid.split(StringPool.BACK_SLASH + StringPool.PIPE))
                            .collect(Collectors.toList());
                    iccids.removeAll(chosenOrderDetails.stream().map(ChannelOrderDetail::getIccid).collect(Collectors.toList()));
                    log.info("iccids:{}", iccids);
                    String iccid = String.join(StringPool.PIPE, iccids);
                    orderToUpdate.setIccid(iccid);
                }
            }
            final Long orderId = channelOrder.getId();
            orderToUpdate.setId(orderId);
            updateChannelOrder(packageUniqueIds, orderToUpdate);
            sendOrderDetailUnsubscribeNotice(channelOrder, chosenOrderDetails);
        } catch (Exception e) {
            luExecutor.execute(() -> {
                SysSystemAlerts sysSystemAlerts = new SysSystemAlerts();
                sysSystemAlerts.setOperationTime(new Date());
                sysSystemAlerts.setSeverityLevel("3");
                sysSystemAlerts.setModuleName("H5个人订单退订");
                sysSystemAlerts.setOperationContent(h5UnsubscribeReq.toString());
                sysSystemAlerts.setExceptionReason(getSimplifiedExceptionMessage(e));
                backFeignClient.insertLog(sysSystemAlerts);
            });
        } finally {
            redissonLock.unlockMulti(packageUniqueIds);
        }
    }

    private void updateChannelOrder(List<String> packageUniqueIds, ChannelOrder orderToUpdate) {
        final Long orderId = orderToUpdate.getId();
        log.debug("子订单退订，更新总订单状态，查询是否有其他非已退订状态的子单");
        List<ChannelOrderDetail> otherOrderDetails = channelOrderDetailMapper.selectList(Wrappers.lambdaQuery
                        (ChannelOrderDetail.class)
                .select(ChannelOrderDetail::getOrderStatus)
                .eq(ChannelOrderDetail::getOrderId, orderId)
                .notIn(ChannelOrderDetail::getPackageUniqueId, packageUniqueIds)
                .ne(ChannelOrderDetail::getOrderStatus, ChannelOrder.OrderStatusEnum.UNSUBSCRIBE.getValue()));
        ChannelOrder.OrderStatusEnum orderStatusEnum;
        if (CollectionUtils.isEmpty(otherOrderDetails)) {
            orderStatusEnum = ChannelOrder.OrderStatusEnum.UNSUBSCRIBE;
        } else {
            Set<String> otherOrderStatusSet = otherOrderDetails.stream().map(ChannelOrderDetail::getOrderStatus)
                    .collect(Collectors.toSet());
            if (otherOrderStatusSet.contains(ChannelOrder.OrderStatusEnum.UNSUBSCRIBE_NOT_AUDIT.getValue())) {
                orderStatusEnum = ChannelOrder.OrderStatusEnum.UNSUBSCRIBE_NOT_AUDIT;
            } else if (otherOrderStatusSet.contains(ChannelOrder.OrderStatusEnum.NOT_DELIVERY.getValue())) {
                orderStatusEnum = ChannelOrder.OrderStatusEnum.COMBINATION;
            } else {
                orderStatusEnum = ChannelOrder.OrderStatusEnum.PARTIAL_UNSUBSCRIBE;
            }
        }
        orderToUpdate.setOrderStatus(orderStatusEnum.getValue());
        channelOrderMapper.updateById(orderToUpdate);
    }

    private boolean updateChannelOrderDetail(ChannelOrder channelOrder, List<ChannelOrderDetail> chosenOrderDetails, boolean isApi) {
        List<String> notDeliverIds = chosenOrderDetails.stream().filter(channelOrderDetail -> "1".equals(channelOrderDetail.getOrderStatus()))
                .map(ChannelOrderDetail::getPackageUniqueId)
                .collect(Collectors.toList());
        List<String> deliverIds = chosenOrderDetails.stream().filter(channelOrderDetail -> "2".equals(channelOrderDetail.getOrderStatus()))
                .map(ChannelOrderDetail::getPackageUniqueId)
                .collect(Collectors.toList());
        ChannelOrderDetail orderDetailToUpdate = new ChannelOrderDetail();
        orderDetailToUpdate.setUnsubscribeTime(new Date());
        orderDetailToUpdate.setUnsubscribeChannel(isApi ? UnsubscribeChannelEnum.API.getK() : UnsubscribeChannelEnum.CUSTOMER_WEBSITE.getK());
        orderDetailToUpdate.setOrderStatus(ChannelOrder.OrderStatusEnum.UNSUBSCRIBE.getValue());
        final boolean isCardAndPackage = OrderTypeEnum.CARD_PACKAGE.getOrderType().equals(channelOrder.getOrderType());
        if (isCardAndPackage) {
            orderDetailToUpdate.setIccid(StringPool.EMPTY);
        }
        if (CollectionUtils.isNotEmpty(deliverIds)) {
            log.debug("退订以完成得子单");
            channelPackageCardMapper.delete(Wrappers.lambdaQuery(ChannelPackageCard.class)
                    .in(ChannelPackageCard::getPackageUniqueId, deliverIds));
            packageDirectionRelationMapper.delete(Wrappers.lambdaQuery(PackageDirectionRelation.class)
                    .in(PackageDirectionRelation::getPackageUniqueId, deliverIds));
            orderDetailToUpdate.setPackageStatus("1");
            channelOrderDetailMapper.update(orderDetailToUpdate, Wrappers.lambdaQuery(ChannelOrderDetail.class)
                    .in(ChannelOrderDetail::getPackageUniqueId, deliverIds));
        }
        if (CollectionUtils.isNotEmpty(notDeliverIds)) {
            log.debug("退订待发货得子单");
            orderDetailToUpdate.setPackageStatus("7");
            channelOrderDetailMapper.update(orderDetailToUpdate, Wrappers.lambdaQuery(ChannelOrderDetail.class)
                    .in(ChannelOrderDetail::getPackageUniqueId, notDeliverIds));
        }
        return isCardAndPackage;
    }

    public List<ChannelOrderDetail> getChosenOrderDetails(ChannelOrder channelOrder,String startStatus) {
        List<ChannelOrderDetail> channelOrderDetails = channelOrderDetailMapper.selectList(Wrappers.lambdaQuery(ChannelOrderDetail.class)
                .eq(ChannelOrderDetail::getOrderId, channelOrder.getId())
                .eq(ChannelOrderDetail::getOrderStatus, ChannelOrder.OrderStatusEnum.NOT_DELIVERY.getValue()));
        List<ChannelOrderDetail> chosenOrderDetails = new ArrayList<>(channelOrderDetails);
        List<ChannelOrderDetail> finishOrderDetails = channelOrderDetailMapper.selectList(Wrappers.lambdaQuery(ChannelOrderDetail.class)
                .eq(ChannelOrderDetail::getOrderId, channelOrder.getId())
                .eq(ChannelOrderDetail::getOrderStatus, ChannelOrder.OrderStatusEnum.FINISHED.getValue()));
        if (!CollectionUtils.isEmpty(finishOrderDetails)) {
            List<String> list = finishOrderDetails.stream().map(ChannelOrderDetail::getPackageUniqueId).collect(Collectors.toList());
            List<ChannelPackageCard> channelPackageCards = channelPackageCardMapper.selectList(Wrappers.lambdaQuery(ChannelPackageCard.class)
                    .select(ChannelPackageCard::getPackageUniqueId)
                    .ne(ChannelPackageCard::getPackageStatus, PackageStatusEnum.UNACTIVATED.getStatus())
                    .in(ChannelPackageCard::getPackageUniqueId, list));
            List<String> packageUniqueIds = channelPackageCards.stream().map(ChannelPackageCard::getPackageUniqueId).collect(Collectors.toList());
            finishOrderDetails = finishOrderDetails.stream().filter(channelOrderDetail -> !packageUniqueIds.contains(channelOrderDetail.getPackageUniqueId())).collect(Collectors.toList());
        }
        chosenOrderDetails.addAll(finishOrderDetails);
        if (CollectionUtils.isEmpty(chosenOrderDetails)) {
            //抛出异常,状态更改为原始状态
            updateChannelOrderStatus(channelOrder.getOrderUniqueId(), startStatus);
            throw new BizException("子单待激活套餐数量为0，退订失败");
        }
        return chosenOrderDetails;
    }

    public List<ChannelOrderDetail> getChannelOrderDetails(Integer num, ChannelOrder channelOrder) {
        List<ChannelOrderDetail> channelOrderDetails = channelOrderDetailMapper.selectList(Wrappers.lambdaQuery(ChannelOrderDetail.class)
                .eq(ChannelOrderDetail::getOrderId, channelOrder.getId())
                .eq(ChannelOrderDetail::getOrderStatus, ChannelOrder.OrderStatusEnum.NOT_DELIVERY.getValue())
                .orderByDesc(ChannelOrderDetail::getId));
        List<ChannelOrderDetail> chosenOrderDetails = new ArrayList<>(channelOrderDetails);
        if (num != null && chosenOrderDetails.size() >= num) {
            chosenOrderDetails = chosenOrderDetails.subList(0, num);
            log.debug("待发货的子单数量已满足");
            return chosenOrderDetails;
        }
        List<ChannelOrderDetail> finishOrderDetails = channelOrderDetailMapper.selectList(Wrappers.lambdaQuery(ChannelOrderDetail.class)
                .eq(ChannelOrderDetail::getOrderId, channelOrder.getId())
                .eq(ChannelOrderDetail::getOrderStatus, ChannelOrder.OrderStatusEnum.FINISHED.getValue())
                .orderByDesc(ChannelOrderDetail::getId));
        if (!CollectionUtils.isEmpty(finishOrderDetails)) {
            List<String> list = finishOrderDetails.stream().map(ChannelOrderDetail::getPackageUniqueId).collect(Collectors.toList());
            List<ChannelPackageCard> channelPackageCards = channelPackageCardMapper.selectList(Wrappers.lambdaQuery(ChannelPackageCard.class)
                    .select(ChannelPackageCard::getPackageUniqueId)
                    .ne(ChannelPackageCard::getPackageStatus, PackageStatusEnum.UNACTIVATED.getStatus())
                    .in(ChannelPackageCard::getPackageUniqueId, list));
            List<String> packageUnqiueIds = channelPackageCards.stream().map(ChannelPackageCard::getPackageUniqueId).collect(Collectors.toList());
            finishOrderDetails = finishOrderDetails.stream().filter(channelOrderDetail -> !packageUnqiueIds.contains(channelOrderDetail.getPackageUniqueId())).collect(Collectors.toList());
        }
        chosenOrderDetails.addAll(finishOrderDetails);
        if (num != null && chosenOrderDetails.size() >= num) {
            channelOrderDetails = chosenOrderDetails.subList(0, num);
            log.debug("待发货、已完成的子单数量已满足");
            return channelOrderDetails;
        }
        if (CollectionUtils.isEmpty(chosenOrderDetails)) {
            throw new BizException(ApiResponseEnum.ORDER_NUMBER_ERROR);
        }
        if (num != null && chosenOrderDetails.size() < num) {
            throw new BizException(ApiResponseEnum.ORDER_NUMBER_ERROR);
        }
        return chosenOrderDetails;
    }

    // 方法将异常的堆栈信息转换为字符串
    public static String getSimplifiedExceptionMessage(Throwable throwable) {
        try {
            StringBuilder sb = new StringBuilder();
            // 添加异常类型和消息
            sb.append(throwable.getClass().getName()).append(": ").append(throwable.getMessage()).append("\n");

            // 获取堆栈跟踪元素
            StackTraceElement[] stackTraceElements = throwable.getStackTrace();
            // 只保留前两行堆栈信息（可以根据需要调整）
            for (int i = 0; i < Math.min(2, stackTraceElements.length); i++) {
                sb.append("\tat ").append(stackTraceElements[i].toString()).append("\n");
            }
            return sb.toString();
        } catch (Exception e) {
            log.warn("记录错误日志失败, =====", e);
            return null;
        }
    }


    @Async("hCardScanExecutor")
    public void unsubscribeForBigOrder(H5UnsubscribeReq h5UnsubscribeReq, ChannelOrder channelOrder) {
        //先查询原始状态
        ChannelOrder channelOrderOrg = channelOrderMapper.selectOne(Wrappers.lambdaQuery(ChannelOrder.class).eq(ChannelOrder::getOrderUniqueId, channelOrder.getOrderUniqueId()));
        String startStatus =channelOrderOrg.getOrderStatus();
        updateChannelOrderStatus(channelOrder.getOrderUniqueId(), ChannelOrder.OrderStatusEnum.UNSUBSCRIBE_PROCESS.getValue());
        List<ChannelOrderDetail> chosenOrderDetails = getChosenOrderDetails(channelOrder,startStatus);
        try {
            List<List<ChannelOrderDetail>> partition = ListUtils.partition(chosenOrderDetails, 100);
            ArrayList<ChannelOrderDetail> failList = new ArrayList<>();
            for (List<ChannelOrderDetail> orderDetails : partition) {
                List<String> packageUniqueIds = orderDetails.stream().filter(channelOrderDetail -> "2".equals(channelOrderDetail.getOrderStatus()))
                        .map(ChannelOrderDetail::getPackageUniqueId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(packageUniqueIds)) {
                    // 本着负责任的态度，防止选中的子单被激活
                    if (!redissonLock.tryLockMulti(packageUniqueIds)) {
                        log.warn("有套餐正在激活，此批子单先不处理");
                        failList.addAll(orderDetails);
                        continue;
                    }
                    List<ChannelPackageCard> channelPackageCards = channelPackageCardMapper.selectList(Wrappers.lambdaQuery(ChannelPackageCard.class)
                            .select(ChannelPackageCard::getPackageUniqueId)
                            .ne(ChannelPackageCard::getPackageStatus, PackageStatusEnum.UNACTIVATED.getStatus())
                            .in(ChannelPackageCard::getPackageUniqueId, packageUniqueIds));
                    List<String> packageUnqiueIds = channelPackageCards.stream().map(ChannelPackageCard::getPackageUniqueId).collect(Collectors.toList());
                    failList.addAll(orderDetails.stream().filter(channelOrderDetail -> packageUnqiueIds.contains(channelOrderDetail.getPackageUniqueId())).collect(Collectors.toList()));
                    orderDetails = orderDetails.stream().filter(channelOrderDetail -> !packageUnqiueIds.contains(channelOrderDetail.getPackageUniqueId())).collect(Collectors.toList());
                }
                updateChannelOrderDetail(channelOrder, orderDetails, !h5UnsubscribeReq.isInterWeb());
                if (CollectionUtils.isNotEmpty(packageUniqueIds)) {
                    redissonLock.unlockMulti(packageUniqueIds);
                }
            }
            chosenOrderDetails.removeAll(failList);
            log.debug("处理成功数量，{}， 失败数量：{}", chosenOrderDetails.size(), failList.size());
            ChannelOrder orderToUpdate = new ChannelOrder();
            orderToUpdate.setAsyncNotifyType(NotifyTypeEnum.NON_ASYNC_NOTIFY.getType());
            final Long orderId = channelOrder.getId();
            orderToUpdate.setId(orderId);
            updateChannelOrder(chosenOrderDetails.stream().map(ChannelOrderDetail::getPackageUniqueId).collect(Collectors.toList()), orderToUpdate);
            if (chosenOrderDetails.size() < channelOrder.getCount()) {
                sendOrderDetailUnsubscribeNotice(channelOrder, chosenOrderDetails);
            } else {
                sendOrderUnsubscribeNotice(channelOrder);
            }
        } catch (Exception e) {
            log.warn("退订失败->", e);
            SysSystemAlerts sysSystemAlerts = new SysSystemAlerts();
            sysSystemAlerts.setOperationTime(new Date());
            sysSystemAlerts.setSeverityLevel("3");
            sysSystemAlerts.setModuleName("个人订单大单退订");
            sysSystemAlerts.setOperationContent(h5UnsubscribeReq.toString());
            sysSystemAlerts.setExceptionReason(getSimplifiedExceptionMessage(e));
            //捕获异常,状态更改为原始状态
//            updateChannelOrderStatus(channelOrder.getOrderUniqueId(), startStatus);
            backFeignClient.insertLog(sysSystemAlerts);
        }
    }

    public void updateChannelOrderStatus(String orderUniqueId,String orderStatus) {
        channelOrderMapper.update(null, Wrappers.lambdaUpdate(ChannelOrder.class)
                .set(ChannelOrder::getOrderStatus, orderStatus)
                .eq(ChannelOrder::getOrderUniqueId, orderUniqueId));
    }
}
