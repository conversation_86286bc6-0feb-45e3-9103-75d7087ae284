# getChannelByEmail 获取销售绑定的渠道商接口方案分析报告

## 1. 接口概述

### 1.1 基本信息
- **接口路径**: `/channel/getChannelByEmail`
- **HTTP方法**: GET
- **功能描述**: 根据用户名获取销售绑定的渠道商列表
- **业务场景**: 销售人员权限管理、渠道商数据权限控制

### 1.2 接口定义
```java
@GetMapping("/getChannelByEmail")
@ApiOperation("获取销售绑定的渠道商")
public Response<List<Channel>> getChannelByEmail(@RequestParam String userName) {
    return Response.ok(channelService.getChannelByEmail(userName));
}
```

## 2. 完整调用链路分析

### 2.1 系统架构图
```mermaid
graph TD
    A[前端/客户端] --> B[ChannelController]
    B --> C[ChannelService]
    C --> D[BackFeignClient]
    D --> E[后台管理服务]
    E --> F[User表查询]
    E --> G[RegionUserRelation表查询]
    C --> H[ChannelMapper]
    H --> I[cms_channel表]
    H --> J[cms_channel_distributors_detail表]
```

### 2.2 调用时序图
```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as ChannelController
    participant Service as ChannelService
    participant Feign as BackFeignClient
    participant BackService as 后台管理服务
    participant Mapper as ChannelMapper
    participant DB as 数据库
    
    Client->>Controller: GET /channel/getChannelByEmail?userName=xxx
    Controller->>Service: getChannelByEmail(userName)
    Service->>Feign: getUserEmail(userName)
    Feign->>BackService: 调用后台管理服务
    BackService->>DB: 查询User表和RegionUserRelation表
    DB-->>BackService: 返回邮箱列表
    BackService-->>Feign: Response<List<String>>
    Feign-->>Service: List<String> emails
    Service->>Mapper: getChannelByEmail(emails)
    Mapper->>DB: 联表查询渠道商信息
    DB-->>Mapper: 渠道商列表
    Mapper-->>Service: List<Channel>
    Service-->>Controller: List<Channel>
    Controller-->>Client: Response<List<Channel>>
```

## 3. 核心业务逻辑分析

### 3.1 Service层实现
```java
@Override
public List<Channel> getChannelByEmail(String userName) {
    // 1. 通过Feign调用获取用户邮箱列表
    List<String> eMails = Response.getAndCheckRemoteData(backFeignClient.getUserEmail(userName));
    
    // 2. 根据邮箱列表查询绑定的渠道商
    List<Channel> channels = channelMapper.getChannelByEmail(eMails);
    
    return channels;
}
```

### 3.2 getUserEmail 核心逻辑分析

#### 3.2.1 业务流程图
```mermaid
flowchart TD
    A[输入userName] --> B[查询User表]
    B --> C{用户corpId为空且functionStatus不在2,3中?}
    C -->|是| D[查询所有销售人员邮箱]
    D --> E[返回所有销售邮箱列表]
    C -->|否| F{用户functionStatus在2,3中?}
    F -->|是| G[返回用户自己的邮箱]
    F -->|否| H[查询用户区域关系]
    H --> I[查询同区域所有用户]
    I --> J[返回同区域用户邮箱列表]
```

#### 3.2.2 详细逻辑分析
```java
@Override 
public List<String> getUserEmail(String userName) {
    // 1. 根据用户名查询用户信息
    User user = userMapper.selectOne(Wrappers.lambdaQuery(User.class)
            .eq(User::getUsername, userName));

    // 2. 特殊情况：用户无corpId且不是销售人员(functionStatus不为2,3)
    if (StrUtil.isBlank(user.getCorpId()) && !"23".contains(user.getFunctionStatus())) {
        // 返回所有销售人员(functionStatus为2,3)的邮箱
        List<User> saleUsers = userMapper.selectList(Wrappers.lambdaQuery(User.class)
                .select(User::getEmail)
                .in(User::getFunctionStatus, Arrays.asList("2","3")));
        
        return saleUsers.stream().map(User::getEmail).collect(Collectors.toList());
    }

    // 3. 如果用户是销售人员(functionStatus为2或3)
    if (functionStatus.contains(user.getFunctionStatus())) {
        // 返回用户自己的邮箱
        return Arrays.asList(user.getEmail());
    } else {
        // 4. 非销售人员：查询同区域的所有用户邮箱
        // 4.1 查询用户的区域关系
        RegionUserRelation regionUserRelation = userRelationMapper.selectOne(
            Wrappers.lambdaQuery(RegionUserRelation.class)
                .eq(RegionUserRelation::getUserId, user.getId()));

        // 4.2 查询同区域的所有用户关系
        List<RegionUserRelation> regionUserRelations = userRelationMapper.selectList(
            Wrappers.lambdaQuery(RegionUserRelation.class)
                .eq(RegionUserRelation::getRegionId, regionUserRelation.getRegionId()));

        // 4.3 获取用户ID列表
        List<Long> userIds = regionUserRelations.stream()
            .map(RegionUserRelation::getUserId)
            .collect(Collectors.toList());

        // 4.4 查询用户信息并获取邮箱
        List<User> users = userMapper.selectList(Wrappers.lambdaQuery(User.class)
                .in(User::getId, userIds));

        return users.stream().map(User::getEmail).collect(Collectors.toList());
    }
}
```

### 3.3 数据库查询分析

#### 3.3.1 ChannelMapper.getChannelByEmail 实现
```sql
SELECT
    cc.corp_name,
    cc.corp_id
FROM
    cms_channel cc
INNER JOIN
    cms_channel_distributors_detail ccdd
ON
    cc.corp_id = ccdd.corp_id
WHERE ccdd.sales_mail IN
<foreach collection="eMails" item="item" open="(" close=")" separator=",">
    #{item}
</foreach>
```

**查询逻辑**:
- 联表查询 `cms_channel` 和 `cms_channel_distributors_detail`
- 通过 `sales_mail` 字段匹配邮箱列表
- 返回渠道商的 `corp_id` 和 `corp_name`

## 4. 用户权限分类分析

### 4.1 用户类型分类

| 用户类型 | 判断条件 | 返回邮箱范围 | 业务含义 |
|----------|----------|--------------|----------|
| **管理员用户** | corpId为空 且 functionStatus不为2,3 | 所有销售人员邮箱 | 超级管理员，可查看所有销售绑定的渠道商 |
| **销售人员** | functionStatus为2或3 | 用户自己的邮箱 | 只能查看自己绑定的渠道商 |
| **区域管理员** | 其他情况 | 同区域所有用户邮箱 | 可查看同区域所有用户绑定的渠道商 |

### 4.2 functionStatus 状态码含义
- **"2"**: 销售人员
- **"3"**: 销售主管
- **其他**: 非销售角色（如区域管理员、系统管理员等）

## 5. 数据表结构分析

### 5.1 核心数据表

#### 5.1.1 cms_channel (渠道商表)
```sql
CREATE TABLE cms_channel (
    corp_id VARCHAR(32) PRIMARY KEY COMMENT '渠道商ID',
    corp_name VARCHAR(100) COMMENT '渠道商名称',
    company_name VARCHAR(200) COMMENT '公司名称',
    status VARCHAR(1) COMMENT '状态:1-正常,2-暂停,3-删除',
    type VARCHAR(1) COMMENT '类型',
    check_status VARCHAR(1) COMMENT '审核状态',
    -- 其他字段...
);
```

#### 5.1.2 cms_channel_distributors_detail (渠道商详情表)
```sql
CREATE TABLE cms_channel_distributors_detail (
    id BIGINT PRIMARY KEY,
    corp_id VARCHAR(32) COMMENT '渠道商ID',
    sales_mail VARCHAR(100) COMMENT '销售邮箱',
    -- 其他字段...
);
```

#### 5.1.3 sys_user (用户表)
```sql
CREATE TABLE sys_user (
    id BIGINT PRIMARY KEY,
    username VARCHAR(50) COMMENT '用户名',
    email VARCHAR(100) COMMENT '邮箱',
    corp_id VARCHAR(32) COMMENT '所属公司ID',
    function_status VARCHAR(2) COMMENT '功能状态:2-销售,3-销售主管',
    -- 其他字段...
);
```

#### 5.1.4 region_user_relation (区域用户关系表)
```sql
CREATE TABLE region_user_relation (
    id BIGINT PRIMARY KEY,
    user_id BIGINT COMMENT '用户ID',
    region_id BIGINT COMMENT '区域ID',
    -- 其他字段...
);
```

## 6. 业务场景分析

### 6.1 典型使用场景

#### 6.1.1 销售人员查询场景
```
用户: sales001 (functionStatus=2)
查询结果: 只返回********************绑定的渠道商
业务意义: 销售人员只能查看自己负责的渠道商
```

#### 6.1.2 区域管理员查询场景
```
用户: manager001 (functionStatus=1, regionId=100)
查询结果: 返回区域100内所有用户邮箱绑定的渠道商
业务意义: 区域管理员可以查看本区域所有渠道商
```

#### 6.1.3 超级管理员查询场景
```
用户: admin (corpId=null, functionStatus=1)
查询结果: 返回所有销售人员邮箱绑定的渠道商
业务意义: 超级管理员可以查看所有渠道商
```

### 6.2 权限控制机制
1. **数据权限**: 通过邮箱匹配控制可见的渠道商范围
2. **角色权限**: 不同角色用户看到不同范围的数据
3. **区域权限**: 区域管理员只能看到本区域的数据

## 7. 潜在问题分析

### 7.1 代码问题
1. **变量未定义**: `functionStatus` 变量在getUserEmail方法中未定义
2. **空指针风险**: 未对user对象进行null检查
3. **异常处理**: 缺少对数据库查询异常的处理

### 7.2 性能问题
1. **N+1查询**: 可能存在多次数据库查询
2. **大数据量**: 管理员用户可能返回大量数据
3. **缓存缺失**: 未使用缓存机制

### 7.3 安全问题
1. **权限绕过**: 可能存在权限控制漏洞
2. **数据泄露**: 管理员可以看到所有销售数据

## 8. 优化建议

### 8.1 代码修复
```java
@Override 
public List<String> getUserEmail(String userName) {
    // 修复：添加functionStatus定义
    List<String> functionStatus = Arrays.asList("2", "3");
    
    User user = userMapper.selectOne(Wrappers.lambdaQuery(User.class)
            .eq(User::getUsername, userName));
    
    // 修复：添加null检查
    if (user == null) {
        throw new BizException("用户不存在");
    }
    
    // 其余逻辑保持不变...
}
```

### 8.2 性能优化
```java
@Override
@Cacheable(value = "channelByEmail", key = "#userName", unless = "#result.isEmpty()")
public List<Channel> getChannelByEmail(String userName) {
    List<String> eMails = Response.getAndCheckRemoteData(backFeignClient.getUserEmail(userName));
    
    if (eMails.isEmpty()) {
        return Collections.emptyList();
    }
    
    return channelMapper.getChannelByEmail(eMails);
}
```

### 8.3 安全增强
```java
@GetMapping("/getChannelByEmail")
@ApiOperation("获取销售绑定的渠道商")
@PreAuthorize("hasRole('SALES') or hasRole('MANAGER') or hasRole('ADMIN')")
public Response<List<Channel>> getChannelByEmail(@RequestParam String userName) {
    // 添加用户身份验证
    String currentUser = SecurityContextHolder.getContext().getAuthentication().getName();
    if (!hasPermission(currentUser, userName)) {
        throw new BizException("无权限查询该用户数据");
    }
    
    return Response.ok(channelService.getChannelByEmail(userName));
}
```

## 9. 测试建议

### 9.1 单元测试
```java
@Test
public void testGetChannelByEmail_SalesUser() {
    // 测试销售人员查询
    when(backFeignClient.getUserEmail("sales001"))
        .thenReturn(Response.ok(Arrays.asList("<EMAIL>")));
    
    List<Channel> result = channelService.getChannelByEmail("sales001");
    
    assertNotNull(result);
    verify(channelMapper).getChannelByEmail(Arrays.asList("<EMAIL>"));
}
```

### 9.2 集成测试
- 测试不同角色用户的权限控制
- 测试大数据量场景的性能
- 测试异常场景的处理

---

**文档版本**: v1.0  
**生成时间**: 2025-01-14  
**分析范围**: getChannelByEmail接口完整业务逻辑  
**技术栈**: Spring Boot + MyBatis-Plus + Feign + 权限控制
