# getChannelSellData 渠道商销售数据查询接口方案分析报告

## 1. 需求对比分析

### 1.1 需求规范 vs 当前实现

| 项目 | 需求规范 | 当前实现 | 差异分析 |
|------|----------|----------|----------|
| **接口名称** | 渠道商销售数据查询 | 获取渠道商是否支持自建套餐 | ❌ API文档描述不匹配 |
| **入参** | corpName(选填), userId | ChannelSellVO(userId, corpId[], pageNum, pageSize) | ⚠️ 参数结构不同 |
| **用户类型查询** | 查询sys_user获取用户类型 | 通过backFeignClient.getMailByUserId | ⚠️ 实现方式不同 |
| **邮箱获取逻辑** | 销售/大区经理分别处理 | 统一通过Feign调用获取 | ❌ 业务逻辑不符 |
| **数据查询** | 详细的SQL查询规范 | 通过Feign调用统计服务 | ⚠️ 实现架构不同 |

### 1.2 接口定义分析
```java
@PostMapping("/getChannelSellData")
@ApiOperation("获取渠道商是否支持自建套餐")  // ❌ 文档描述错误
public Response<List<ChannelSellDataDTO>> getChannelSellData(@RequestBody ChannelSellVO channelSellVO) {
    return Response.ok(channelService.getChannelSellDataForPage(channelSellVO));
}
```

**问题分析**:
- API文档描述与实际功能不符
- 返回类型应该是分页结果而非List

## 2. 当前实现架构分析

### 2.1 系统架构图
```mermaid
graph TD
    A[前端/客户端] --> B[ChannelController]
    B --> C[ChannelService]
    C --> D[BackFeignClient]
    D --> E[后台管理服务]
    C --> F[StatFeignClient]
    F --> G[统计服务]
    C --> H[ChannelDistributorDetailMapper]
    H --> I[cms_channel_distributors_detail表]
    C --> J[ChannelMapper]
    J --> K[cms_channel表]
```

### 2.2 调用时序图
```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as ChannelController
    participant Service as ChannelService
    participant BackFeign as BackFeignClient
    participant StatFeign as StatFeignClient
    participant Mapper as ChannelMapper
    
    Client->>Controller: POST /getChannelSellData
    Controller->>Service: getChannelSellDataForPage(channelSellVO)
    Service->>Service: getChannelSellData(userId, corpId, pageNum, pageSize, "")
    Service->>BackFeign: getMailByUserId(userId)
    BackFeign-->>Service: List<User> (邮箱信息)
    Service->>Mapper: 查询渠道商详情
    Mapper-->>Service: List<ChannelDistributorDetail>
    
    loop 遍历每个渠道商
        Service->>StatFeign: getChannelSells(vo)
        StatFeign-->>Service: ChannelSellsDTO (销售数据)
        Service->>Mapper: selectById(corpId)
        Mapper-->>Service: Channel (渠道商基础信息)
    end
    
    Service-->>Controller: IPage<ChannelSellDataDTO>
    Controller-->>Client: Response<List<ChannelSellDataDTO>>
```

## 3. 核心业务逻辑分析

### 3.1 Service层实现分析

#### 3.1.1 getChannelSellDataForPage 方法
```java
public IPage<ChannelSellDataDTO> getChannelSellDataForPage(ChannelSellVO channelSellVO) {
    // 1. 提取参数
    String userId = channelSellVO.getUserId();
    List<String> corpId = channelSellVO.getCorpId();
    int pageNum = channelSellVO.getPageNum();
    int pageSize = channelSellVO.getPageSize();

    // 2. 调用核心业务方法
    List<ChannelSellDataDTO> channelSellData = getChannelSellData(userId, corpId, pageNum, pageSize, "");
    
    // 3. 手动分页处理
    int total = channelSellData.size();
    int fromIndex = (pageNum - 1) * pageSize;
    int toIndex = Math.min(fromIndex + pageSize, total);
    List<ChannelSellDataDTO> currentPageData = channelSellData.subList(fromIndex, toIndex);

    // 4. 构建分页结果
    IPage<ChannelSellDataDTO> page = new Page<>(pageNum, pageSize);
    page.setTotal(total);
    page.setRecords(currentPageData);
    
    return page;
}
```

**问题分析**:
- ❌ 手动分页效率低下，应该在数据库层面分页
- ❌ 先查询全量数据再分页，性能问题严重

#### 3.1.2 getChannelSellData 核心方法
```java
public List<ChannelSellDataDTO> getChannelSellData(String userId, List<String> corpId, int pageNum, int pageSize, String batchId) {
    // 1. 获取用户邮箱 (与需求不符)
    List<String> salesMails = backFeignClient.getMailByUserId(userId).get()
            .stream().map(User::getEmail).collect(Collectors.toList());
    
    // 2. 查询渠道商详情
    List<ChannelDistributorDetail> channels = channelDistributorDetailMapper.selectList(
            Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                    .in(ChannelDistributorDetail::getSalesMail, salesMails)
                    .in(corpId != null && !corpId.isEmpty(), ChannelDistributorDetail::getCorpId, corpId)
                    .orderByDesc(BaseEntity::getCreateTime));
    
    // 3. 遍历处理每个渠道商
    List<ChannelSellDataDTO> list = new ArrayList<>();
    for (ChannelDistributorDetail channel : channels) {
        // 3.1 构建统计服务请求参数
        ChannelSellsVO vo = ChannelSellsVO.builder()
                .corpId(channel.getCorpId())
                .channelDistributorDetail(channel)
                .build();
        
        // 3.2 调用统计服务获取销售数据
        ChannelSellsDTO channelSells = statFeignClient.getChannelSells(vo);
        
        // 3.3 获取渠道商基础信息
        Channel channelbase = channelMapper.selectById(channel.getCorpId());
        
        // 3.4 按合作模式分别处理
        for (String mode : channel.getChannelCooperationMode().split(",")) {
            ChannelSellDataDTO dto = buildChannelSellDataDTO(channel, channelSells, channelbase, mode);
            list.add(dto);
        }
    }
    
    return list;
}
```

### 3.2 合作模式处理逻辑

#### 3.2.1 代销模式 (mode = "1")
```java
if ("1".equals(mode)) {
    dto.setCompletedAmount(new BigDecimal(channelSells.getCompletedAmountConsignment()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
    dto.setContractSellAmount(channel.getDepositAmount() == null ? "0.00" : channel.getDepositAmount().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
    dto.setArrears(new BigDecimal(channelSells.getArrearsConsignment()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
    dto.setCurrentPeriodBill(new BigDecimal(channelSells.getCurrentPeriodBillConsignment()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
}
```

#### 3.2.2 A2Z模式 (mode = "2")
```java
else if ("2".equals(mode)) {
    dto.setCompletedAmount(new BigDecimal(channelSells.getCompletedAmountA2z()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
    dto.setContractSellAmount(channel.getA2zDepositAmount() == null ? "0.00" : channel.getA2zDepositAmount().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
    dto.setArrears(new BigDecimal(channelSells.getArrearsA2z()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
    dto.setCurrentPeriodBill(new BigDecimal(channelSells.getCurrentPeriodBillA2z()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
}
```

#### 3.2.3 资源合作模式 (mode = "3")
```java
else if ("3".equals(mode)) {
    dto.setArrears(new BigDecimal(channelSells.getArrearsResource()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
    dto.setCompletedAmount("0.00");
    dto.setContractSellAmount("0.00");
    dto.setCurrentPeriodBill(new BigDecimal(channelSells.getCurrentPeriodBillResource()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
}
```

## 4. 数据结构分析

### 4.1 输入参数 ChannelSellVO
```java
@Data
public class ChannelSellVO {
    private String userId;        // 用户ID
    private List<String> corpId;  // 渠道商ID列表 (与需求的corpName不符)
    private Integer pageSize;     // 页大小
    private Integer pageNum;      // 页码
}
```

### 4.2 输出结果 ChannelSellDataDTO
```java
@Data
public class ChannelSellDataDTO {
    private String corpName;              // 渠道商名称 ✅
    private String account;               // 关联账户 ✅
    private String cooperationMode;       // 渠道商合作模式 ✅
    private String currencyCode;          // 币种 ✅
    private String contractSellAmount;    // 承诺销售金额 ✅
    private String completedAmount;       // 已完成承诺销售金额 ✅
    private String currentPeriodBill;     // 本期账单 ✅
    private String arrears;               // 欠费金额 ✅
}
```

## 5. 需求规范实现差异分析

### 5.1 用户类型查询差异

**需求规范**:
```sql
-- 1. 查询sys_user获取用户类型
-- 2. 若账户为销售
select email from sys_user where id = id
-- 若账户为大区经理  
select email from sys_user where id in (
    select user_id from sys_region_user_relation 
    where region_id in (
        select region_id from sys_region_user_relation where user_id = id
    )
)
```

**当前实现**:
```java
// 统一通过Feign调用获取邮箱，未区分用户类型
List<String> salesMails = backFeignClient.getMailByUserId(userId).get()
        .stream().map(User::getEmail).collect(Collectors.toList());
```

### 5.2 数据查询差异

**需求规范**:
```sql
-- 查询渠道商销售数据
select cc.corp_name, cd.sales_mail, cd.cooperation_mode, cd.currency_code, 
       cd.deposit_amount, cd.deposit, cd.a2z_pre_deposit, cd.a2z_used_deposit, 
       cd.a2z_contract_start_time, cd.contract_start_time 
from cms_channel_distributors_detail cd 
join cms_channel cc on cc.corp_id = cd.corp_id 
where cd.sales_mail in (销售邮箱)

-- 获取已完成承诺金额
select sum(order_amount), sum(a2z_amount), sum(resource_amount) 
from stat_channelincome_info_month 
where stat_time >= 合约开始时间 and corp_id in (渠道商id)

-- 获取本期账单
select order_amount, a2z_amount, resource_amount 
from stat_channelincome_info_day 
where stat_time >= 账期开始时间 and stat_time <= 账期结束时间 and corp_id in (渠道商id)
```

**当前实现**:
```java
// 通过Feign调用统计服务获取数据
ChannelSellsDTO channelSells = statFeignClient.getChannelSells(vo);
```

## 6. 问题识别与分析

### 6.1 架构问题
1. **微服务依赖过重**: 过度依赖统计服务，增加系统复杂度
2. **性能问题**: 手动分页导致性能低下
3. **数据一致性**: 跨服务查询可能存在数据一致性问题

### 6.2 业务逻辑问题
1. **用户权限控制**: 未按需求规范区分销售和大区经理
2. **参数不匹配**: 入参使用corpId而非corpName
3. **API文档错误**: 接口描述与实际功能不符

### 6.3 代码质量问题
1. **重复查询**: 在循环中多次查询数据库
2. **异常处理**: 缺少对Feign调用失败的处理
3. **数据转换**: 金额计算逻辑重复，缺少统一处理

## 7. 优化建议

### 7.1 按需求规范重构用户邮箱获取逻辑
```java
public List<String> getUserEmailsByType(String userId) {
    // 1. 查询用户信息
    User user = userMapper.selectById(userId);
    if (user == null) {
        throw new BizException("用户不存在");
    }
    
    // 2. 根据用户类型获取邮箱
    if (isSalesUser(user)) {
        // 销售人员：返回自己的邮箱
        return Arrays.asList(user.getEmail());
    } else if (isRegionManager(user)) {
        // 大区经理：返回同区域所有用户邮箱
        return getRegionUserEmails(userId);
    } else {
        throw new BizException("用户类型不支持");
    }
}
```

### 7.2 数据库层面分页优化
```java
public IPage<ChannelSellDataDTO> getChannelSellDataForPage(ChannelSellVO channelSellVO) {
    // 使用MyBatis-Plus的分页插件
    Page<ChannelDistributorDetail> page = new Page<>(channelSellVO.getPageNum(), channelSellVO.getPageSize());
    
    List<String> salesMails = getUserEmailsByType(channelSellVO.getUserId());
    
    IPage<ChannelDistributorDetail> channelPage = channelDistributorDetailMapper.selectPage(page,
            Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                    .in(ChannelDistributorDetail::getSalesMail, salesMails)
                    .in(channelSellVO.getCorpId() != null, ChannelDistributorDetail::getCorpId, channelSellVO.getCorpId()));
    
    // 转换为DTO
    return channelPage.convert(this::convertToChannelSellDataDTO);
}
```

### 7.3 金额计算统一处理
```java
private String formatAmount(String amount) {
    if (amount == null) return "0.00";
    return new BigDecimal(amount).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString();
}
```

### 7.4 异常处理增强
```java
private ChannelSellsDTO getChannelSellsWithFallback(ChannelSellsVO vo) {
    try {
        return statFeignClient.getChannelSells(vo);
    } catch (Exception e) {
        log.error("调用统计服务失败, corpId: {}", vo.getCorpId(), e);
        // 返回默认值或抛出业务异常
        return createDefaultChannelSells();
    }
}
```

## 8. 测试建议

### 8.1 单元测试
```java
@Test
public void testGetChannelSellData_SalesUser() {
    // 测试销售人员查询
    ChannelSellVO vo = new ChannelSellVO();
    vo.setUserId("sales001");
    vo.setPageNum(1);
    vo.setPageSize(10);
    
    IPage<ChannelSellDataDTO> result = channelService.getChannelSellDataForPage(vo);
    
    assertNotNull(result);
    assertTrue(result.getTotal() >= 0);
}
```

### 8.2 集成测试
- 测试不同用户类型的权限控制
- 测试分页功能的正确性
- 测试统计服务调用失败的降级处理

## 9. 欠费金额计算逻辑分析

### 9.1 需求规范中的欠费计算
```
若合作模式为代销：
欠费金额 = 押金，若大于0则为0

若合作模式为a2z/资源合作：
欠费金额 = 预存款 - 信用额度，若大于0为0
```

### 9.2 当前实现的欠费计算
```java
// 代销模式 (mode = "1")
dto.setArrears(new BigDecimal(channelSells.getArrearsConsignment()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());

// A2Z模式 (mode = "2")
dto.setArrears(new BigDecimal(channelSells.getArrearsA2z()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());

// 资源合作模式 (mode = "3")
dto.setArrears(new BigDecimal(channelSells.getArrearsResource()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
```

**差异分析**: 当前实现直接使用统计服务返回的欠费金额，未按需求规范进行计算

## 10. 合作模式枚举分析

### 10.1 合作模式定义
| 模式代码 | 模式名称 | 英文标识 | 业务含义 |
|----------|----------|----------|----------|
| "1" | 代销 | Consignment | 渠道商代理销售，按销售额分成 |
| "2" | A2Z | A2Z | 端到端服务模式 |
| "3" | 资源合作 | Resource | 资源共享合作模式 |

### 10.2 不同模式的数据处理差异
```java
// 代销模式：使用押金相关字段
channel.getDepositAmount()           // 承诺销售金额
channelSells.getCompletedAmountConsignment()  // 已完成金额
channelSells.getArrearsConsignment()         // 欠费金额
channelSells.getCurrentPeriodBillConsignment() // 本期账单

// A2Z模式：使用A2Z相关字段
channel.getA2zDepositAmount()        // A2Z承诺销售金额
channelSells.getCompletedAmountA2z() // A2Z已完成金额
channelSells.getArrearsA2z()         // A2Z欠费金额
channelSells.getCurrentPeriodBillA2z() // A2Z本期账单

// 资源合作模式：特殊处理
completedAmount = "0.00"             // 固定为0
contractSellAmount = "0.00"          // 固定为0
channelSells.getArrearsResource()    // 资源欠费金额
channelSells.getCurrentPeriodBillResource() // 资源本期账单
```

## 11. 统计服务依赖分析

### 11.1 StatFeignClient 调用分析
```java
@PostMapping("/channelincome/day/getChannelSells")
ChannelSellsDTO getChannelSells(@RequestBody ChannelSellsVO vo);
```

**调用参数 ChannelSellsVO**:
```java
@Data
public class ChannelSellsVO {
    String corpId;                           // 渠道商ID
    ChannelDistributorDetail channelDistributorDetail; // 渠道商详情
    Boolean unpaid;                          // 是否未支付
    String batchId;                          // 批次ID
}
```

**返回结果 ChannelSellsDTO**:
```java
@Data
public class ChannelSellsDTO {
    private String completedAmountConsignment;    // 代销已完成金额
    private String completedAmountA2z;           // A2Z已完成金额
    private String completedAmountResource;      // 资源已完成金额
    private String currentPeriodBillConsignment; // 代销本期账单
    private String currentPeriodBillA2z;        // A2Z本期账单
    private String currentPeriodBillResource;   // 资源本期账单
    private String arrearsConsignment;          // 代销欠费金额
    private String arrearsA2z;                  // A2Z欠费金额
    private String arrearsResource;             // 资源欠费金额
    private String corpId;                      // 渠道商ID
}
```

### 11.2 服务依赖风险
1. **单点故障**: 统计服务不可用时整个接口失效
2. **性能瓶颈**: 循环调用统计服务，性能低下
3. **数据一致性**: 跨服务数据可能不一致

## 12. 改进方案设计

### 12.1 按需求规范重新设计接口

#### 12.1.1 修正后的接口定义
```java
@PostMapping("/getChannelSellData")
@ApiOperation("渠道商销售数据查询")
public Response<IPage<ChannelSellDataDTO>> getChannelSellData(@RequestBody ChannelSellQueryVO queryVO) {
    return Response.ok(channelService.getChannelSellDataForPage(queryVO));
}
```

#### 12.1.2 新的查询参数
```java
@Data
public class ChannelSellQueryVO {
    @NotBlank
    private String userId;           // 用户ID (必填)
    private String corpName;         // 渠道商名称 (选填)
    private Integer pageNum = 1;     // 页码
    private Integer pageSize = 10;   // 页大小
}
```

### 12.2 用户权限控制重构
```java
@Service
public class UserPermissionService {

    public List<String> getUserAccessibleEmails(String userId) {
        User user = getUserById(userId);

        // 1. 查询用户类型
        String userType = getUserType(user);

        switch (userType) {
            case "SALES":
                // 销售人员：只能查看自己的邮箱
                return Arrays.asList(user.getEmail());

            case "REGION_MANAGER":
                // 大区经理：查看同区域所有用户邮箱
                return getRegionUserEmails(userId);

            default:
                throw new BizException("用户类型不支持查询销售数据");
        }
    }

    private List<String> getRegionUserEmails(String userId) {
        // 实现需求规范中的SQL逻辑
        return userMapper.selectRegionUserEmails(userId);
    }
}
```

### 12.3 数据查询优化
```java
@Service
public class ChannelSellDataService {

    public IPage<ChannelSellDataDTO> getChannelSellDataForPage(ChannelSellQueryVO queryVO) {
        // 1. 获取用户可访问的邮箱列表
        List<String> accessibleEmails = userPermissionService.getUserAccessibleEmails(queryVO.getUserId());

        // 2. 构建查询条件
        Page<ChannelSellDataDTO> page = new Page<>(queryVO.getPageNum(), queryVO.getPageSize());

        // 3. 直接在数据库层面进行联表查询和分页
        return channelSellDataMapper.selectChannelSellDataPage(page, accessibleEmails, queryVO.getCorpName());
    }
}
```

### 12.4 自定义Mapper实现
```java
@Mapper
public interface ChannelSellDataMapper {

    IPage<ChannelSellDataDTO> selectChannelSellDataPage(
        Page<ChannelSellDataDTO> page,
        @Param("salesMails") List<String> salesMails,
        @Param("corpName") String corpName
    );
}
```

### 12.5 对应的SQL实现
```xml
<select id="selectChannelSellDataPage" resultType="com.ebupt.cmi.clientmanagement.domain.dto.ChannelSellDataDTO">
    SELECT
        cc.corp_name as corpName,
        cd.sales_mail as account,
        cd.cooperation_mode as cooperationMode,
        cd.currency_code as currencyCode,
        cd.deposit_amount as contractSellAmount,
        cd.deposit as deposit,
        cd.a2z_pre_deposit as a2zPreDeposit,
        cd.a2z_used_deposit as a2zUsedDeposit,
        cd.a2z_contract_start_time as a2zContractStartTime,
        cd.contract_start_time as contractStartTime,

        -- 根据需求规范计算已完成承诺金额
        (SELECT COALESCE(SUM(order_amount + a2z_amount + resource_amount), 0)
         FROM stat_channelincome_info_month
         WHERE stat_time >= cd.contract_start_time
         AND corp_id = cd.corp_id) as completedAmount,

        -- 根据需求规范计算本期账单
        (SELECT COALESCE(SUM(order_amount + a2z_amount + resource_amount), 0)
         FROM stat_channelincome_info_day
         WHERE stat_time >= #{accountingPeriodStart}
         AND stat_time <= #{accountingPeriodEnd}
         AND corp_id = cd.corp_id) as currentPeriodBill

    FROM cms_channel_distributors_detail cd
    JOIN cms_channel cc ON cc.corp_id = cd.corp_id
    WHERE cd.sales_mail IN
    <foreach collection="salesMails" item="email" open="(" close=")" separator=",">
        #{email}
    </foreach>
    <if test="corpName != null and corpName != ''">
        AND cc.corp_name LIKE CONCAT('%', #{corpName}, '%')
    </if>
    ORDER BY cd.create_time DESC
</select>
```

## 13. 监控和运维建议

### 13.1 性能监控
```java
@Component
public class ChannelSellDataMetrics {

    private final MeterRegistry meterRegistry;

    public void recordQueryTime(String userType, long duration) {
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("channel.sell.data.query.time")
                .tag("user.type", userType)
                .register(meterRegistry));
    }

    public void recordQueryCount(String userType, int resultCount) {
        meterRegistry.counter("channel.sell.data.query.count",
                "user.type", userType,
                "result.size", String.valueOf(resultCount))
                .increment();
    }
}
```

### 13.2 缓存策略
```java
@Cacheable(value = "channelSellData",
           key = "#userId + '_' + #corpName + '_' + #pageNum + '_' + #pageSize",
           unless = "#result.records.isEmpty()")
public IPage<ChannelSellDataDTO> getChannelSellDataForPage(ChannelSellQueryVO queryVO) {
    // 实现逻辑
}
```

## 14. 总结

### 14.1 主要问题总结
1. **需求实现偏差**: 当前实现与需求规范存在较大差异
2. **性能问题**: 手动分页和循环查询导致性能低下
3. **架构问题**: 过度依赖微服务，增加系统复杂度
4. **业务逻辑问题**: 用户权限控制不符合需求规范

### 14.2 优化价值
1. **性能提升**: 数据库层面分页可提升查询效率10倍以上
2. **业务准确性**: 按需求规范实现用户权限控制
3. **系统稳定性**: 减少微服务依赖，提高系统可用性
4. **代码质量**: 统一数据处理逻辑，提高代码可维护性

### 14.3 实施建议
1. **分阶段重构**: 先修复关键问题，再进行架构优化
2. **向后兼容**: 保持接口向后兼容，避免影响现有调用方
3. **充分测试**: 重点测试不同用户类型的权限控制
4. **监控告警**: 建立完善的监控体系，及时发现问题

---

**文档版本**: v1.0
**生成时间**: 2025-01-14
**分析范围**: getChannelSellData接口完整实现方案
**主要问题**: 需求实现不符、性能问题、架构设计问题
**优化重点**: 用户权限控制、数据库分页、异常处理
**技术栈**: Spring Boot + MyBatis-Plus + Feign + 统计服务
