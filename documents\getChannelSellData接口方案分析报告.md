# getChannelSellData 渠道商销售数据查询接口方案分析报告

## 1. 需求对比分析

### 1.1 需求规范 vs 当前实现

| 项目 | 需求规范 | 当前实现 | 差异分析 |
|------|----------|----------|----------|
| **接口名称** | 渠道商销售数据查询 | 获取渠道商是否支持自建套餐 | ❌ API文档描述不匹配 |
| **入参** | corpName(选填), userId | ChannelSellVO(userId, corpId[], pageNum, pageSize) | ⚠️ 参数结构不同 |
| **用户类型查询** | 查询sys_user获取用户类型 | 通过backFeignClient.getMailByUserId | ⚠️ 实现方式不同 |
| **邮箱获取逻辑** | 销售/大区经理分别处理 | 统一通过Feign调用获取 | ❌ 业务逻辑不符 |
| **数据查询** | 详细的SQL查询规范 | 通过Feign调用统计服务 | ⚠️ 实现架构不同 |

### 1.2 接口定义分析
```java
@PostMapping("/getChannelSellData")
@ApiOperation("获取渠道商是否支持自建套餐")  // ❌ 文档描述错误
public Response<List<ChannelSellDataDTO>> getChannelSellData(@RequestBody ChannelSellVO channelSellVO) {
    return Response.ok(channelService.getChannelSellDataForPage(channelSellVO));
}
```

**问题分析**:
- API文档描述与实际功能不符
- 返回类型应该是分页结果而非List

## 2. 当前实现架构分析

### 2.1 系统架构图
```mermaid
graph TD
    A[前端/客户端] --> B[ChannelController]
    B --> C[ChannelService]
    C --> D[BackFeignClient]
    D --> E[后台管理服务]
    C --> F[StatFeignClient]
    F --> G[统计服务]
    C --> H[ChannelDistributorDetailMapper]
    H --> I[cms_channel_distributors_detail表]
    C --> J[ChannelMapper]
    J --> K[cms_channel表]
```

### 2.2 调用时序图
```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as ChannelController
    participant Service as ChannelService
    participant BackFeign as BackFeignClient
    participant StatFeign as StatFeignClient
    participant Mapper as ChannelMapper
    
    Client->>Controller: POST /getChannelSellData
    Controller->>Service: getChannelSellDataForPage(channelSellVO)
    Service->>Service: getChannelSellData(userId, corpId, pageNum, pageSize, "")
    Service->>BackFeign: getMailByUserId(userId)
    BackFeign-->>Service: List<User> (邮箱信息)
    Service->>Mapper: 查询渠道商详情
    Mapper-->>Service: List<ChannelDistributorDetail>
    
    loop 遍历每个渠道商
        Service->>StatFeign: getChannelSells(vo)
        StatFeign-->>Service: ChannelSellsDTO (销售数据)
        Service->>Mapper: selectById(corpId)
        Mapper-->>Service: Channel (渠道商基础信息)
    end
    
    Service-->>Controller: IPage<ChannelSellDataDTO>
    Controller-->>Client: Response<List<ChannelSellDataDTO>>
```

## 3. 核心业务逻辑分析

### 3.1 Service层实现分析

#### 3.1.1 getChannelSellDataForPage 方法
```java
public IPage<ChannelSellDataDTO> getChannelSellDataForPage(ChannelSellVO channelSellVO) {
    // 1. 提取参数
    String userId = channelSellVO.getUserId();
    List<String> corpId = channelSellVO.getCorpId();
    int pageNum = channelSellVO.getPageNum();
    int pageSize = channelSellVO.getPageSize();

    // 2. 调用核心业务方法
    List<ChannelSellDataDTO> channelSellData = getChannelSellData(userId, corpId, pageNum, pageSize, "");
    
    // 3. 手动分页处理
    int total = channelSellData.size();
    int fromIndex = (pageNum - 1) * pageSize;
    int toIndex = Math.min(fromIndex + pageSize, total);
    List<ChannelSellDataDTO> currentPageData = channelSellData.subList(fromIndex, toIndex);

    // 4. 构建分页结果
    IPage<ChannelSellDataDTO> page = new Page<>(pageNum, pageSize);
    page.setTotal(total);
    page.setRecords(currentPageData);
    
    return page;
}
```

**问题分析**:
- ❌ 手动分页效率低下，应该在数据库层面分页
- ❌ 先查询全量数据再分页，性能问题严重

#### 3.1.2 getChannelSellData 核心方法
```java
public List<ChannelSellDataDTO> getChannelSellData(String userId, List<String> corpId, int pageNum, int pageSize, String batchId) {
    // 1. 获取用户邮箱 (与需求不符)
    List<String> salesMails = backFeignClient.getMailByUserId(userId).get()
            .stream().map(User::getEmail).collect(Collectors.toList());
    
    // 2. 查询渠道商详情
    List<ChannelDistributorDetail> channels = channelDistributorDetailMapper.selectList(
            Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                    .in(ChannelDistributorDetail::getSalesMail, salesMails)
                    .in(corpId != null && !corpId.isEmpty(), ChannelDistributorDetail::getCorpId, corpId)
                    .orderByDesc(BaseEntity::getCreateTime));
    
    // 3. 遍历处理每个渠道商
    List<ChannelSellDataDTO> list = new ArrayList<>();
    for (ChannelDistributorDetail channel : channels) {
        // 3.1 构建统计服务请求参数
        ChannelSellsVO vo = ChannelSellsVO.builder()
                .corpId(channel.getCorpId())
                .channelDistributorDetail(channel)
                .build();
        
        // 3.2 调用统计服务获取销售数据
        ChannelSellsDTO channelSells = statFeignClient.getChannelSells(vo);
        
        // 3.3 获取渠道商基础信息
        Channel channelbase = channelMapper.selectById(channel.getCorpId());
        
        // 3.4 按合作模式分别处理
        for (String mode : channel.getChannelCooperationMode().split(",")) {
            ChannelSellDataDTO dto = buildChannelSellDataDTO(channel, channelSells, channelbase, mode);
            list.add(dto);
        }
    }
    
    return list;
}
```

### 3.2 合作模式处理逻辑

#### 3.2.1 代销模式 (mode = "1")
```java
if ("1".equals(mode)) {
    dto.setCompletedAmount(new BigDecimal(channelSells.getCompletedAmountConsignment()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
    dto.setContractSellAmount(channel.getDepositAmount() == null ? "0.00" : channel.getDepositAmount().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
    dto.setArrears(new BigDecimal(channelSells.getArrearsConsignment()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
    dto.setCurrentPeriodBill(new BigDecimal(channelSells.getCurrentPeriodBillConsignment()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
}
```

#### 3.2.2 A2Z模式 (mode = "2")
```java
else if ("2".equals(mode)) {
    dto.setCompletedAmount(new BigDecimal(channelSells.getCompletedAmountA2z()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
    dto.setContractSellAmount(channel.getA2zDepositAmount() == null ? "0.00" : channel.getA2zDepositAmount().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
    dto.setArrears(new BigDecimal(channelSells.getArrearsA2z()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
    dto.setCurrentPeriodBill(new BigDecimal(channelSells.getCurrentPeriodBillA2z()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
}
```

#### 3.2.3 资源合作模式 (mode = "3")
```java
else if ("3".equals(mode)) {
    dto.setArrears(new BigDecimal(channelSells.getArrearsResource()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
    dto.setCompletedAmount("0.00");
    dto.setContractSellAmount("0.00");
    dto.setCurrentPeriodBill(new BigDecimal(channelSells.getCurrentPeriodBillResource()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
}
```

## 4. 数据结构分析

### 4.1 输入参数 ChannelSellVO
```java
@Data
public class ChannelSellVO {
    private String userId;        // 用户ID
    private List<String> corpId;  // 渠道商ID列表 (与需求的corpName不符)
    private Integer pageSize;     // 页大小
    private Integer pageNum;      // 页码
}
```

### 4.2 输出结果 ChannelSellDataDTO
```java
@Data
public class ChannelSellDataDTO {
    private String corpName;              // 渠道商名称 ✅
    private String account;               // 关联账户 ✅
    private String cooperationMode;       // 渠道商合作模式 ✅
    private String currencyCode;          // 币种 ✅
    private String contractSellAmount;    // 承诺销售金额 ✅
    private String completedAmount;       // 已完成承诺销售金额 ✅
    private String currentPeriodBill;     // 本期账单 ✅
    private String arrears;               // 欠费金额 ✅
}
```

## 5. 需求规范实现差异分析

### 5.1 用户类型查询差异

**需求规范**:
```sql
-- 1. 查询sys_user获取用户类型
-- 2. 若账户为销售
select email from sys_user where id = id
-- 若账户为大区经理  
select email from sys_user where id in (
    select user_id from sys_region_user_relation 
    where region_id in (
        select region_id from sys_region_user_relation where user_id = id
    )
)
```

**当前实现**:
```java
// 统一通过Feign调用获取邮箱，未区分用户类型
List<String> salesMails = backFeignClient.getMailByUserId(userId).get()
        .stream().map(User::getEmail).collect(Collectors.toList());
```

### 5.2 数据查询差异

**需求规范**:
```sql
-- 查询渠道商销售数据
select cc.corp_name, cd.sales_mail, cd.cooperation_mode, cd.currency_code, 
       cd.deposit_amount, cd.deposit, cd.a2z_pre_deposit, cd.a2z_used_deposit, 
       cd.a2z_contract_start_time, cd.contract_start_time 
from cms_channel_distributors_detail cd 
join cms_channel cc on cc.corp_id = cd.corp_id 
where cd.sales_mail in (销售邮箱)

-- 获取已完成承诺金额
select sum(order_amount), sum(a2z_amount), sum(resource_amount) 
from stat_channelincome_info_month 
where stat_time >= 合约开始时间 and corp_id in (渠道商id)

-- 获取本期账单
select order_amount, a2z_amount, resource_amount 
from stat_channelincome_info_day 
where stat_time >= 账期开始时间 and stat_time <= 账期结束时间 and corp_id in (渠道商id)
```

**当前实现**:
```java
// 通过Feign调用统计服务获取数据
ChannelSellsDTO channelSells = statFeignClient.getChannelSells(vo);
```

## 6. 问题识别与分析

### 6.1 架构问题
1. **微服务依赖过重**: 过度依赖统计服务，增加系统复杂度
2. **性能问题**: 手动分页导致性能低下
3. **数据一致性**: 跨服务查询可能存在数据一致性问题

### 6.2 业务逻辑问题
1. **用户权限控制**: 未按需求规范区分销售和大区经理
2. **参数不匹配**: 入参使用corpId而非corpName
3. **API文档错误**: 接口描述与实际功能不符

### 6.3 代码质量问题
1. **重复查询**: 在循环中多次查询数据库
2. **异常处理**: 缺少对Feign调用失败的处理
3. **数据转换**: 金额计算逻辑重复，缺少统一处理

## 7. 优化建议

### 7.1 按需求规范重构用户邮箱获取逻辑
```java
public List<String> getUserEmailsByType(String userId) {
    // 1. 查询用户信息
    User user = userMapper.selectById(userId);
    if (user == null) {
        throw new BizException("用户不存在");
    }
    
    // 2. 根据用户类型获取邮箱
    if (isSalesUser(user)) {
        // 销售人员：返回自己的邮箱
        return Arrays.asList(user.getEmail());
    } else if (isRegionManager(user)) {
        // 大区经理：返回同区域所有用户邮箱
        return getRegionUserEmails(userId);
    } else {
        throw new BizException("用户类型不支持");
    }
}
```

### 7.2 数据库层面分页优化
```java
public IPage<ChannelSellDataDTO> getChannelSellDataForPage(ChannelSellVO channelSellVO) {
    // 使用MyBatis-Plus的分页插件
    Page<ChannelDistributorDetail> page = new Page<>(channelSellVO.getPageNum(), channelSellVO.getPageSize());
    
    List<String> salesMails = getUserEmailsByType(channelSellVO.getUserId());
    
    IPage<ChannelDistributorDetail> channelPage = channelDistributorDetailMapper.selectPage(page,
            Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                    .in(ChannelDistributorDetail::getSalesMail, salesMails)
                    .in(channelSellVO.getCorpId() != null, ChannelDistributorDetail::getCorpId, channelSellVO.getCorpId()));
    
    // 转换为DTO
    return channelPage.convert(this::convertToChannelSellDataDTO);
}
```

### 7.3 金额计算统一处理
```java
private String formatAmount(String amount) {
    if (amount == null) return "0.00";
    return new BigDecimal(amount).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString();
}
```

### 7.4 异常处理增强
```java
private ChannelSellsDTO getChannelSellsWithFallback(ChannelSellsVO vo) {
    try {
        return statFeignClient.getChannelSells(vo);
    } catch (Exception e) {
        log.error("调用统计服务失败, corpId: {}", vo.getCorpId(), e);
        // 返回默认值或抛出业务异常
        return createDefaultChannelSells();
    }
}
```

## 8. 测试建议

### 8.1 单元测试
```java
@Test
public void testGetChannelSellData_SalesUser() {
    // 测试销售人员查询
    ChannelSellVO vo = new ChannelSellVO();
    vo.setUserId("sales001");
    vo.setPageNum(1);
    vo.setPageSize(10);
    
    IPage<ChannelSellDataDTO> result = channelService.getChannelSellDataForPage(vo);
    
    assertNotNull(result);
    assertTrue(result.getTotal() >= 0);
}
```

### 8.2 集成测试
- 测试不同用户类型的权限控制
- 测试分页功能的正确性
- 测试统计服务调用失败的降级处理

## 9. 欠费金额计算逻辑分析

### 9.1 需求规范中的欠费计算
```
若合作模式为代销：
欠费金额 = 押金，若大于0则为0

若合作模式为a2z/资源合作：
欠费金额 = 预存款 - 信用额度，若大于0为0
```

### 9.2 当前实现的欠费计算
```java
// 代销模式 (mode = "1")
dto.setArrears(new BigDecimal(channelSells.getArrearsConsignment()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());

// A2Z模式 (mode = "2")
dto.setArrears(new BigDecimal(channelSells.getArrearsA2z()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());

// 资源合作模式 (mode = "3")
dto.setArrears(new BigDecimal(channelSells.getArrearsResource()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
```

**差异分析**: 当前实现直接使用统计服务返回的欠费金额，未按需求规范进行计算

## 10. 合作模式枚举分析

### 10.1 合作模式定义
| 模式代码 | 模式名称 | 英文标识 | 业务含义 |
|----------|----------|----------|----------|
| "1" | 代销 | Consignment | 渠道商代理销售，按销售额分成 |
| "2" | A2Z | A2Z | 端到端服务模式 |
| "3" | 资源合作 | Resource | 资源共享合作模式 |

### 10.2 不同模式的数据处理差异
```java
// 代销模式：使用押金相关字段
channel.getDepositAmount()           // 承诺销售金额
channelSells.getCompletedAmountConsignment()  // 已完成金额
channelSells.getArrearsConsignment()         // 欠费金额
channelSells.getCurrentPeriodBillConsignment() // 本期账单

// A2Z模式：使用A2Z相关字段
channel.getA2zDepositAmount()        // A2Z承诺销售金额
channelSells.getCompletedAmountA2z() // A2Z已完成金额
channelSells.getArrearsA2z()         // A2Z欠费金额
channelSells.getCurrentPeriodBillA2z() // A2Z本期账单

// 资源合作模式：特殊处理
completedAmount = "0.00"             // 固定为0
contractSellAmount = "0.00"          // 固定为0
channelSells.getArrearsResource()    // 资源欠费金额
channelSells.getCurrentPeriodBillResource() // 资源本期账单
```

## 11. 统计服务依赖分析

### 11.1 StatFeignClient 调用分析
```java
@PostMapping("/channelincome/day/getChannelSells")
ChannelSellsDTO getChannelSells(@RequestBody ChannelSellsVO vo);
```

**调用参数 ChannelSellsVO**:
```java
@Data
public class ChannelSellsVO {
    String corpId;                           // 渠道商ID
    ChannelDistributorDetail channelDistributorDetail; // 渠道商详情
    Boolean unpaid;                          // 是否未支付
    String batchId;                          // 批次ID
}
```

**返回结果 ChannelSellsDTO**:
```java
@Data
public class ChannelSellsDTO {
    private String completedAmountConsignment;    // 代销已完成金额
    private String completedAmountA2z;           // A2Z已完成金额
    private String completedAmountResource;      // 资源已完成金额
    private String currentPeriodBillConsignment; // 代销本期账单
    private String currentPeriodBillA2z;        // A2Z本期账单
    private String currentPeriodBillResource;   // 资源本期账单
    private String arrearsConsignment;          // 代销欠费金额
    private String arrearsA2z;                  // A2Z欠费金额
    private String arrearsResource;             // 资源欠费金额
    private String corpId;                      // 渠道商ID
}
```

### 11.2 getChannelSells 方法详细实现分析

#### 11.2.1 方法签名和核心逻辑
```java
@SneakyThrows
@Override
public ChannelSellsDTO getChannelSells(ChannelSellsVO vo) {
    ChannelDistributorDetail channelDistributorDetail = vo.getChannelDistributorDetail();
    String corpId = channelDistributorDetail.getCorpId();
    ChannelSellsDTO channelSells = new ChannelSellsDTO();
    channelSells.setCorpId(corpId);

    // 解析合作模式：1.代销 2.a2z 3.资源合作 4.代销+a2z 5.a2z+资源合作 6.代销+资源合作 7.代销+a2z+资源合作
    String[] modes = channelDistributorDetail.getChannelCooperationMode().split(",");

    // 遍历处理每种合作模式
    for (String mode : modes) {
        processCooperationMode(mode, channelDistributorDetail, channelSells, vo);
    }

    return channelSells;
}
```

#### 11.2.2 合作模式处理逻辑

**代销模式 (mode = "1") 处理流程**:
```java
if ("1".equals(mode)) {
    // 1. 确定统计开始时间
    String statDate = "********";  // 默认开始时间
    if (channelDistributorDetail.getContractStartTime() != null) {
        statDate = sdf.format(channelDistributorDetail.getContractStartTime());
    }

    // 2. 获取已完成销售额（从合约开始时间累计）
    StatChannelincomeInfoDay day = channelIncomeService.getSum(null, corpId, mode, statDate);
    channelSells.setCompletedAmountConsignment(day.getOrderAmount().toString());

    // 3. 查询月度账单数据
    List<ChannelIncomeInfoMonth> list = getMonthlyIncomeList(corpId, Arrays.asList(1, 4, 6, 7), vo);

    if (!list.isEmpty()) {
        ChannelIncomeInfoMonth info = list.get(0);

        // 4. 计算本期账单 = 代销订单收入 + 调账金额 + IMSI费收入 + IMSI费调账
        BigDecimal currentPeriodBill = info.getDirectIncome()
                .add(info.getAccountAdjustment())
                .add(info.getSellImsiAmount())
                .add(info.getSellImsiAdjustAmount());
        channelSells.setCurrentPeriodBillConsignment(currentPeriodBill.toString());

        // 5. 计算欠费金额（未支付的账单累计）
        BigDecimal arrears = calculateArrears(list, "consignment");
        channelSells.setArrearsConsignment(arrears.toString());
    }
}
```

**A2Z模式 (mode = "2") 处理流程**:
```java
else if ("2".equals(mode)) {
    // 1. 确定A2Z合约开始时间
    String statDate = "********";
    if (channelDistributorDetail.getA2zContractStartTime() != null) {
        statDate = sdf.format(channelDistributorDetail.getA2zContractStartTime());
    }

    // 2. 获取A2Z已完成销售额
    StatChannelincomeInfoDay day = channelIncomeService.getSum(null, corpId, mode, statDate);
    channelSells.setCompletedAmountA2z(day.getA2zAmount().toString());

    // 3. 查询A2Z月度账单数据 (accountingType: 2, 4, 5, 7)
    List<ChannelIncomeInfoMonth> list = getMonthlyIncomeList(corpId, Arrays.asList(2, 4, 5, 7), vo);

    if (!list.isEmpty()) {
        ChannelIncomeInfoMonth info = list.get(0);

        // 4. 计算A2Z本期账单 = A2Z收入 + A2Z调账 + A2ZIMSI费 + A2ZIMSI费调账
        BigDecimal currentPeriodBill = info.getA2zAmount()
                .add(info.getA2zAdjustAmount())
                .add(info.getA2zImsiAmount())
                .add(info.getA2zImsiAdjustAmount());
        channelSells.setCurrentPeriodBillA2z(currentPeriodBill.toString());

        // 5. 计算A2Z欠费金额
        BigDecimal arrears = calculateArrears(list, "a2z");
        channelSells.setArrearsA2z(arrears.toString());
    }
}
```

**资源合作模式 (mode = "3") 处理流程**:
```java
else if ("3".equals(mode)) {
    // 1. 使用A2Z合约开始时间作为资源合作开始时间
    String statDate = "********";
    if (channelDistributorDetail.getA2zContractStartTime() != null) {
        statDate = sdf.format(channelDistributorDetail.getA2zContractStartTime());
    }

    // 2. 获取资源合作数据
    StatChannelincomeInfoDay day = channelIncomeService.getSum(null, corpId, mode, statDate);
    channelSells.setCurrentPeriodBillResource(day.getResourceAmount().toString());

    // 3. 查询资源合作月度账单数据 (accountingType: 3, 5, 6, 7)
    List<ChannelIncomeInfoMonth> list = getMonthlyIncomeList(corpId, Arrays.asList(3, 5, 6, 7), vo);

    if (!list.isEmpty()) {
        ChannelIncomeInfoMonth info = list.get(0);

        // 4. 计算资源合作本期账单 = 资源收入 + 资源调账 + 资源IMSI费 + 资源IMSI费调账
        BigDecimal currentPeriodBill = info.getResourceAmount()
                .add(info.getResourceAdjust())
                .add(info.getResourceImsiAmount())
                .add(info.getResourceImsiAdjustAmount());
        channelSells.setCurrentPeriodBillResource(currentPeriodBill.toString());

        // 5. 计算资源合作欠费金额
        BigDecimal arrears = calculateArrears(list, "resource");
        channelSells.setArrearsResource(arrears.toString());
    }
}
```

#### 11.2.3 核心计算逻辑分析

**账单金额计算公式**:
```java
// 代销最终账单金额 = 代销订单收入总额 + 代销订单调账金额 + 代销IMSI费收入 + 代销IMSI费调账金额
BigDecimal consignmentBill = directIncome + accountAdjustment + sellImsiAmount + sellImsiAdjustAmount;

// A2Z最终账单金额 = A2Z收入金额 + A2Z调账金额 + A2ZIMSI费收入 + A2ZIMSI费调账
BigDecimal a2zBill = a2zAmount + a2zAdjustAmount + a2zImsiAmount + a2zImsiAdjustAmount;

// 资源合作最终金额 = 资源合作收入金额 + 资源合作收入调账 + 资源合作IMSI费收入 + 资源合作IMSI费调账
BigDecimal resourceBill = resourceAmount + resourceAdjust + resourceImsiAmount + resourceImsiAdjustAmount;
```

**欠费金额计算逻辑**:
```java
private BigDecimal calculateArrears(List<ChannelIncomeInfoMonth> list, String type) {
    BigDecimal arrears = BigDecimal.ZERO;
    for (ChannelIncomeInfoMonth e : list) {
        // 只计算未支付(chargeStatus != "2")且为正常渠道(channelType = "1")的账单
        if (!e.getChargeStatus().equals("2") && "1".equals(e.getChannelType())) {
            switch (type) {
                case "consignment":
                    arrears = arrears.add(e.getDirectIncome())
                            .add(e.getAccountAdjustment())
                            .add(e.getSellImsiAmount())
                            .add(e.getSellImsiAdjustAmount());
                    break;
                case "a2z":
                    arrears = arrears.add(e.getA2zAmount())
                            .add(e.getA2zAdjustAmount())
                            .add(e.getA2zImsiAmount())
                            .add(e.getA2zImsiAdjustAmount());
                    break;
                case "resource":
                    arrears = arrears.add(e.getResourceAmount())
                            .add(e.getResourceAdjust())
                            .add(e.getResourceImsiAmount())
                            .add(e.getResourceImsiAdjustAmount());
                    break;
            }
        }
    }
    return arrears.setScale(2, RoundingMode.HALF_UP);
}
```

#### 11.2.4 数据查询条件分析

**月度收入数据查询条件**:
```java
private List<ChannelIncomeInfoMonth> getMonthlyIncomeList(String corpId, List<Integer> accountingTypes, ChannelSellsVO vo) {
    LambdaQueryWrapper<ChannelIncomeInfoMonth> wrapper = Wrappers.lambdaQuery(ChannelIncomeInfoMonth.class)
            .eq(ChannelIncomeInfoMonth::getCorpId, corpId)
            .in(ChannelIncomeInfoMonth::getAccountingType, accountingTypes)
            .ge(ChannelIncomeInfoMonth::getSvcEndTime, "********")  // 固定从2024年9月开始
            .orderByDesc(ChannelIncomeInfoMonth::getSvcEndTime);

    // 如果是查询未支付数据，增加批次ID条件
    if (vo.isUnpaid()) {
        wrapper.eq(ChannelIncomeInfoMonth::getBatchId, vo.getBatchId());
    }

    return channelincomeInfoMonthMapper.selectList(wrapper);
}
```

**账务类型 (accountingType) 含义**:
| 账务类型 | 含义 | 适用模式 |
|----------|------|----------|
| 1 | 代销 | 代销模式 |
| 2 | A2Z | A2Z模式 |
| 3 | 资源合作 | 资源合作模式 |
| 4 | 代销+A2Z | 混合模式 |
| 5 | A2Z+资源合作 | 混合模式 |
| 6 | 代销+资源合作 | 混合模式 |
| 7 | 代销+A2Z+资源合作 | 全模式 |

#### 11.2.5 特殊业务逻辑分析

**未支付数据过滤逻辑**:
```java
// 在方法末尾的特殊判断
if (vo.isUnpaid() & a & b & c) {
    return null;  // 如果查询未支付数据且三种模式都没有数据，返回null
}
```

**变量含义**:
- `a`: 代销模式是否有数据 (false表示有数据)
- `b`: A2Z模式是否有数据 (false表示有数据)
- `c`: 资源合作模式是否有数据 (false表示有数据)

**业务逻辑**: 当查询未支付数据时，如果所有合作模式都没有未支付数据，则返回null，这样在上层调用中会被过滤掉。

#### 11.2.6 数据表结构依赖

**ChannelIncomeInfoMonth 表结构**:
```java
public class ChannelIncomeInfoMonth {
    private String corpId;                    // 渠道商ID
    private String svcEndTime;               // 服务结束时间
    private Integer accountingType;          // 账务类型
    private String chargeStatus;             // 收费状态: "2"-已支付
    private String channelType;              // 渠道类型: "1"-正常渠道
    private String batchId;                  // 批次ID

    // 代销相关字段
    private BigDecimal directIncome;         // 代销订单收入总额
    private BigDecimal accountAdjustment;    // 代销订单调账金额
    private BigDecimal sellImsiAmount;       // 代销IMSI费收入
    private BigDecimal sellImsiAdjustAmount; // 代销IMSI费调账金额

    // A2Z相关字段
    private BigDecimal a2zAmount;            // A2Z收入金额
    private BigDecimal a2zAdjustAmount;      // A2Z调账金额
    private BigDecimal a2zImsiAmount;        // A2ZIMSI费收入
    private BigDecimal a2zImsiAdjustAmount;  // A2ZIMSI费调账

    // 资源合作相关字段
    private BigDecimal resourceAmount;       // 资源合作收入金额
    private BigDecimal resourceAdjust;       // 资源合作收入调账
    private BigDecimal resourceImsiAmount;   // 资源合作IMSI费收入
    private BigDecimal resourceImsiAdjustAmount; // 资源合作IMSI费调账
}
```

**StatChannelincomeInfoDay 表结构**:
```java
public class StatChannelincomeInfoDay {
    private String corpId;                   // 渠道商ID
    private BigDecimal orderAmount;          // 代销订单总金额
    private BigDecimal a2zAmount;            // A2Z总金额
    private BigDecimal resourceAmount;       // 资源合作总金额
}
```

#### 11.2.7 性能问题分析

**查询性能问题**:
1. **多次数据库查询**: 每个合作模式都要查询月度收入表
2. **大数据量扫描**: `ge(ChannelIncomeInfoMonth::getSvcEndTime, "********")` 可能扫描大量数据
3. **内存计算**: 欠费金额需要在内存中遍历计算
4. **重复查询**: 相同corpId可能被多次查询

**优化建议**:
```java
// 1. 一次性查询所有账务类型的数据
List<ChannelIncomeInfoMonth> allList = channelincomeInfoMonthMapper.selectList(
    Wrappers.lambdaQuery(ChannelIncomeInfoMonth.class)
        .eq(ChannelIncomeInfoMonth::getCorpId, corpId)
        .ge(ChannelIncomeInfoMonth::getSvcEndTime, "********")
        .orderByDesc(ChannelIncomeInfoMonth::getSvcEndTime));

// 2. 按账务类型分组处理
Map<Integer, List<ChannelIncomeInfoMonth>> groupedData = allList.stream()
    .collect(Collectors.groupingBy(ChannelIncomeInfoMonth::getAccountingType));

// 3. 使用SQL聚合函数计算欠费金额，避免内存计算
```

### 11.3 统计服务架构问题分析

#### 11.3.1 服务职责边界模糊
```java
// 问题：统计服务承担了过多的业务逻辑
public ChannelSellsDTO getChannelSells(ChannelSellsVO vo) {
    // 1. 数据查询逻辑
    // 2. 业务规则计算
    // 3. 金额聚合计算
    // 4. 特殊业务判断
}
```

**问题分析**:
- 统计服务不应该包含复杂的业务逻辑
- 应该专注于数据聚合和统计计算
- 业务规则应该在业务服务层处理

#### 11.3.2 数据一致性风险
```java
// 风险点1: 跨表查询可能存在数据不一致
StatChannelincomeInfoDay day = channelIncomeService.getSum(null, corpId, mode, statDate);
List<ChannelIncomeInfoMonth> list = channelincomeInfoMonthMapper.selectList(...);

// 风险点2: 实时计算vs预计算数据的一致性问题
```

#### 11.3.3 错误处理缺失
```java
@SneakyThrows  // 问题：使用@SneakyThrows掩盖了异常处理
public ChannelSellsDTO getChannelSells(ChannelSellsVO vo) {
    // 缺少对数据库查询异常的处理
    // 缺少对空数据的验证
    // 缺少对计算溢出的处理
}
```

### 11.4 服务依赖风险总结
1. **单点故障**: 统计服务不可用时整个接口失效
2. **性能瓶颈**: 循环调用统计服务，性能低下
3. **数据一致性**: 跨服务数据可能不一致
4. **复杂计算逻辑**: 统计服务内部包含复杂的金额计算和数据聚合逻辑
5. **职责边界模糊**: 统计服务承担了过多的业务逻辑
6. **错误处理不足**: 缺少完善的异常处理机制

## 12. 统计服务优化方案

### 12.1 服务职责重新划分

#### 12.1.1 统计服务职责简化
```java
// 优化后的统计服务：只负责数据聚合，不包含业务逻辑
@Service
public class StatisticsService {

    // 获取已完成销售额（纯数据聚合）
    public BigDecimal getCompletedAmount(String corpId, String mode, String startDate) {
        return channelIncomeService.getSum(null, corpId, mode, startDate);
    }

    // 获取月度账单数据（纯数据查询）
    public List<ChannelIncomeInfoMonth> getMonthlyIncomeData(String corpId, List<Integer> accountingTypes, String startDate) {
        return channelincomeInfoMonthMapper.selectList(
            Wrappers.lambdaQuery(ChannelIncomeInfoMonth.class)
                .eq(ChannelIncomeInfoMonth::getCorpId, corpId)
                .in(ChannelIncomeInfoMonth::getAccountingType, accountingTypes)
                .ge(ChannelIncomeInfoMonth::getSvcEndTime, startDate)
                .orderByDesc(ChannelIncomeInfoMonth::getSvcEndTime));
    }

    // 批量获取多个渠道商的数据（性能优化）
    public Map<String, List<ChannelIncomeInfoMonth>> getBatchMonthlyIncomeData(List<String> corpIds, String startDate) {
        List<ChannelIncomeInfoMonth> allData = channelincomeInfoMonthMapper.selectList(
            Wrappers.lambdaQuery(ChannelIncomeInfoMonth.class)
                .in(ChannelIncomeInfoMonth::getCorpId, corpIds)
                .ge(ChannelIncomeInfoMonth::getSvcEndTime, startDate)
                .orderByDesc(ChannelIncomeInfoMonth::getSvcEndTime));

        return allData.stream().collect(Collectors.groupingBy(ChannelIncomeInfoMonth::getCorpId));
    }
}
```

#### 12.1.2 业务服务承担计算逻辑
```java
// 业务服务：负责业务规则和计算逻辑
@Service
public class ChannelSellCalculationService {

    public ChannelSellDataDTO calculateChannelSellData(ChannelDistributorDetail channel, String mode) {
        ChannelSellDataDTO dto = new ChannelSellDataDTO();

        // 1. 获取基础数据
        String corpId = channel.getCorpId();
        String startDate = getContractStartDate(channel, mode);
        List<Integer> accountingTypes = getAccountingTypes(mode);

        // 2. 调用统计服务获取数据
        BigDecimal completedAmount = statisticsService.getCompletedAmount(corpId, mode, startDate);
        List<ChannelIncomeInfoMonth> monthlyData = statisticsService.getMonthlyIncomeData(corpId, accountingTypes, "********");

        // 3. 业务计算
        dto.setCompletedAmount(formatAmount(completedAmount));
        dto.setCurrentPeriodBill(calculateCurrentPeriodBill(monthlyData, mode));
        dto.setArrears(calculateArrears(monthlyData, mode));
        dto.setContractSellAmount(getContractSellAmount(channel, mode));

        return dto;
    }

    private String calculateCurrentPeriodBill(List<ChannelIncomeInfoMonth> monthlyData, String mode) {
        if (monthlyData.isEmpty()) return "0.00";

        ChannelIncomeInfoMonth latestData = monthlyData.get(0);
        BigDecimal bill = BigDecimal.ZERO;

        switch (mode) {
            case "1": // 代销
                bill = latestData.getDirectIncome()
                        .add(latestData.getAccountAdjustment())
                        .add(latestData.getSellImsiAmount())
                        .add(latestData.getSellImsiAdjustAmount());
                break;
            case "2": // A2Z
                bill = latestData.getA2zAmount()
                        .add(latestData.getA2zAdjustAmount())
                        .add(latestData.getA2zImsiAmount())
                        .add(latestData.getA2zImsiAdjustAmount());
                break;
            case "3": // 资源合作
                bill = latestData.getResourceAmount()
                        .add(latestData.getResourceAdjust())
                        .add(latestData.getResourceImsiAmount())
                        .add(latestData.getResourceImsiAdjustAmount());
                break;
        }

        return formatAmount(bill);
    }

    private String calculateArrears(List<ChannelIncomeInfoMonth> monthlyData, String mode) {
        BigDecimal arrears = BigDecimal.ZERO;

        for (ChannelIncomeInfoMonth data : monthlyData) {
            // 只计算未支付且为正常渠道的账单
            if (!"2".equals(data.getChargeStatus()) && "1".equals(data.getChannelType())) {
                arrears = arrears.add(getAmountByMode(data, mode));
            }
        }

        return formatAmount(arrears);
    }
}
```

### 12.2 性能优化方案

#### 12.2.1 批量查询优化
```java
// 原有问题：循环调用统计服务
for (ChannelDistributorDetail channel : channels) {
    ChannelSellsDTO channelSells = statFeignClient.getChannelSells(vo); // N次调用
}

// 优化方案：批量查询
public List<ChannelSellDataDTO> getChannelSellDataBatch(List<ChannelDistributorDetail> channels) {
    // 1. 提取所有corpId
    List<String> corpIds = channels.stream()
            .map(ChannelDistributorDetail::getCorpId)
            .collect(Collectors.toList());

    // 2. 批量获取统计数据
    Map<String, List<ChannelIncomeInfoMonth>> batchData = statisticsService.getBatchMonthlyIncomeData(corpIds, "********");
    Map<String, BigDecimal> completedAmounts = statisticsService.getBatchCompletedAmounts(corpIds);

    // 3. 批量计算结果
    return channels.stream()
            .flatMap(channel -> processChannelModes(channel, batchData, completedAmounts).stream())
            .collect(Collectors.toList());
}
```

#### 12.2.2 SQL优化方案
```sql
-- 原有问题：多次查询同一张表
-- 优化方案：使用SQL聚合函数一次性计算所有数据

-- 一次性获取渠道商的所有统计数据
SELECT
    corp_id,
    accounting_type,
    -- 本期账单（最新一期）
    FIRST_VALUE(direct_income + account_adjustment + sell_imsi_amount + sell_imsi_adjust_amount)
        OVER (PARTITION BY corp_id, accounting_type ORDER BY svc_end_time DESC) as current_period_bill_consignment,
    FIRST_VALUE(a2z_amount + a2z_adjust_amount + a2z_imsi_amount + a2z_imsi_adjust_amount)
        OVER (PARTITION BY corp_id, accounting_type ORDER BY svc_end_time DESC) as current_period_bill_a2z,
    FIRST_VALUE(resource_amount + resource_adjust + resource_imsi_amount + resource_imsi_adjust_amount)
        OVER (PARTITION BY corp_id, accounting_type ORDER BY svc_end_time DESC) as current_period_bill_resource,

    -- 欠费金额（未支付账单累计）
    SUM(CASE WHEN charge_status != '2' AND channel_type = '1'
        THEN direct_income + account_adjustment + sell_imsi_amount + sell_imsi_adjust_amount
        ELSE 0 END) as arrears_consignment,
    SUM(CASE WHEN charge_status != '2' AND channel_type = '1'
        THEN a2z_amount + a2z_adjust_amount + a2z_imsi_amount + a2z_imsi_adjust_amount
        ELSE 0 END) as arrears_a2z,
    SUM(CASE WHEN charge_status != '2' AND channel_type = '1'
        THEN resource_amount + resource_adjust + resource_imsi_amount + resource_imsi_adjust_amount
        ELSE 0 END) as arrears_resource

FROM channel_income_info_month
WHERE corp_id IN (#{corpIds})
  AND svc_end_time >= '********'
GROUP BY corp_id, accounting_type
ORDER BY corp_id, accounting_type;
```

#### 12.2.3 缓存策略
```java
@Service
public class ChannelSellDataCacheService {

    @Cacheable(value = "channelSellData", key = "#corpId + '_' + #mode", unless = "#result == null")
    public ChannelSellDataDTO getCachedChannelSellData(String corpId, String mode) {
        return calculateChannelSellData(corpId, mode);
    }

    @CacheEvict(value = "channelSellData", key = "#corpId + '_*'")
    public void evictChannelSellDataCache(String corpId) {
        // 当渠道商数据更新时，清除相关缓存
    }

    // 预热缓存
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void warmUpCache() {
        List<String> activeCorpIds = getActiveCorpIds();
        for (String corpId : activeCorpIds) {
            for (String mode : Arrays.asList("1", "2", "3")) {
                getCachedChannelSellData(corpId, mode);
            }
        }
    }
}
```

### 12.3 错误处理增强

#### 12.3.1 统计服务异常处理
```java
@Service
public class RobustStatisticsService {

    public ChannelSellsDTO getChannelSellsWithFallback(ChannelSellsVO vo) {
        try {
            return getChannelSells(vo);
        } catch (DataAccessException e) {
            log.error("数据库查询异常, corpId: {}", vo.getCorpId(), e);
            return createDefaultChannelSells(vo.getCorpId());
        } catch (ArithmeticException e) {
            log.error("金额计算异常, corpId: {}", vo.getCorpId(), e);
            return createDefaultChannelSells(vo.getCorpId());
        } catch (Exception e) {
            log.error("统计服务异常, corpId: {}", vo.getCorpId(), e);
            throw new BizException("获取渠道商销售数据失败");
        }
    }

    private ChannelSellsDTO createDefaultChannelSells(String corpId) {
        ChannelSellsDTO defaultData = new ChannelSellsDTO();
        defaultData.setCorpId(corpId);
        defaultData.setCompletedAmountConsignment("0.00");
        defaultData.setCompletedAmountA2z("0.00");
        defaultData.setCompletedAmountResource("0.00");
        defaultData.setCurrentPeriodBillConsignment("0.00");
        defaultData.setCurrentPeriodBillA2z("0.00");
        defaultData.setCurrentPeriodBillResource("0.00");
        defaultData.setArrearsConsignment("0.00");
        defaultData.setArrearsA2z("0.00");
        defaultData.setArrearsResource("0.00");
        return defaultData;
    }
}
```

#### 12.3.2 数据验证增强
```java
@Service
public class DataValidationService {

    public void validateChannelSellsData(ChannelSellsDTO data) {
        if (data == null) {
            throw new BizException("渠道商销售数据为空");
        }

        // 验证金额格式
        validateAmount(data.getCompletedAmountConsignment(), "代销已完成金额");
        validateAmount(data.getCurrentPeriodBillConsignment(), "代销本期账单");
        validateAmount(data.getArrearsConsignment(), "代销欠费金额");

        // 验证业务逻辑
        if (isNegativeAmount(data.getCompletedAmountConsignment())) {
            log.warn("代销已完成金额为负数, corpId: {}, amount: {}", data.getCorpId(), data.getCompletedAmountConsignment());
        }
    }

    private void validateAmount(String amount, String fieldName) {
        if (amount == null) {
            throw new BizException(fieldName + "不能为空");
        }

        try {
            new BigDecimal(amount);
        } catch (NumberFormatException e) {
            throw new BizException(fieldName + "格式错误: " + amount);
        }
    }
}
```

## 13. 改进方案设计

### 12.1 按需求规范重新设计接口

#### 12.1.1 修正后的接口定义
```java
@PostMapping("/getChannelSellData")
@ApiOperation("渠道商销售数据查询")
public Response<IPage<ChannelSellDataDTO>> getChannelSellData(@RequestBody ChannelSellQueryVO queryVO) {
    return Response.ok(channelService.getChannelSellDataForPage(queryVO));
}
```

#### 12.1.2 新的查询参数
```java
@Data
public class ChannelSellQueryVO {
    @NotBlank
    private String userId;           // 用户ID (必填)
    private String corpName;         // 渠道商名称 (选填)
    private Integer pageNum = 1;     // 页码
    private Integer pageSize = 10;   // 页大小
}
```

### 12.2 用户权限控制重构
```java
@Service
public class UserPermissionService {

    public List<String> getUserAccessibleEmails(String userId) {
        User user = getUserById(userId);

        // 1. 查询用户类型
        String userType = getUserType(user);

        switch (userType) {
            case "SALES":
                // 销售人员：只能查看自己的邮箱
                return Arrays.asList(user.getEmail());

            case "REGION_MANAGER":
                // 大区经理：查看同区域所有用户邮箱
                return getRegionUserEmails(userId);

            default:
                throw new BizException("用户类型不支持查询销售数据");
        }
    }

    private List<String> getRegionUserEmails(String userId) {
        // 实现需求规范中的SQL逻辑
        return userMapper.selectRegionUserEmails(userId);
    }
}
```

### 12.3 数据查询优化
```java
@Service
public class ChannelSellDataService {

    public IPage<ChannelSellDataDTO> getChannelSellDataForPage(ChannelSellQueryVO queryVO) {
        // 1. 获取用户可访问的邮箱列表
        List<String> accessibleEmails = userPermissionService.getUserAccessibleEmails(queryVO.getUserId());

        // 2. 构建查询条件
        Page<ChannelSellDataDTO> page = new Page<>(queryVO.getPageNum(), queryVO.getPageSize());

        // 3. 直接在数据库层面进行联表查询和分页
        return channelSellDataMapper.selectChannelSellDataPage(page, accessibleEmails, queryVO.getCorpName());
    }
}
```

### 12.4 自定义Mapper实现
```java
@Mapper
public interface ChannelSellDataMapper {

    IPage<ChannelSellDataDTO> selectChannelSellDataPage(
        Page<ChannelSellDataDTO> page,
        @Param("salesMails") List<String> salesMails,
        @Param("corpName") String corpName
    );
}
```

### 12.5 对应的SQL实现
```xml
<select id="selectChannelSellDataPage" resultType="com.ebupt.cmi.clientmanagement.domain.dto.ChannelSellDataDTO">
    SELECT
        cc.corp_name as corpName,
        cd.sales_mail as account,
        cd.cooperation_mode as cooperationMode,
        cd.currency_code as currencyCode,
        cd.deposit_amount as contractSellAmount,
        cd.deposit as deposit,
        cd.a2z_pre_deposit as a2zPreDeposit,
        cd.a2z_used_deposit as a2zUsedDeposit,
        cd.a2z_contract_start_time as a2zContractStartTime,
        cd.contract_start_time as contractStartTime,

        -- 根据需求规范计算已完成承诺金额
        (SELECT COALESCE(SUM(order_amount + a2z_amount + resource_amount), 0)
         FROM stat_channelincome_info_month
         WHERE stat_time >= cd.contract_start_time
         AND corp_id = cd.corp_id) as completedAmount,

        -- 根据需求规范计算本期账单
        (SELECT COALESCE(SUM(order_amount + a2z_amount + resource_amount), 0)
         FROM stat_channelincome_info_day
         WHERE stat_time >= #{accountingPeriodStart}
         AND stat_time <= #{accountingPeriodEnd}
         AND corp_id = cd.corp_id) as currentPeriodBill

    FROM cms_channel_distributors_detail cd
    JOIN cms_channel cc ON cc.corp_id = cd.corp_id
    WHERE cd.sales_mail IN
    <foreach collection="salesMails" item="email" open="(" close=")" separator=",">
        #{email}
    </foreach>
    <if test="corpName != null and corpName != ''">
        AND cc.corp_name LIKE CONCAT('%', #{corpName}, '%')
    </if>
    ORDER BY cd.create_time DESC
</select>
```

## 13. 监控和运维建议

### 13.1 性能监控
```java
@Component
public class ChannelSellDataMetrics {

    private final MeterRegistry meterRegistry;

    public void recordQueryTime(String userType, long duration) {
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("channel.sell.data.query.time")
                .tag("user.type", userType)
                .register(meterRegistry));
    }

    public void recordQueryCount(String userType, int resultCount) {
        meterRegistry.counter("channel.sell.data.query.count",
                "user.type", userType,
                "result.size", String.valueOf(resultCount))
                .increment();
    }
}
```

### 13.2 缓存策略
```java
@Cacheable(value = "channelSellData",
           key = "#userId + '_' + #corpName + '_' + #pageNum + '_' + #pageSize",
           unless = "#result.records.isEmpty()")
public IPage<ChannelSellDataDTO> getChannelSellDataForPage(ChannelSellQueryVO queryVO) {
    // 实现逻辑
}
```

## 14. 总结

### 14.1 主要问题总结
1. **需求实现偏差**: 当前实现与需求规范存在较大差异
2. **性能问题**: 手动分页和循环查询导致性能低下
3. **架构问题**: 过度依赖微服务，增加系统复杂度
4. **业务逻辑问题**: 用户权限控制不符合需求规范

### 14.2 优化价值
1. **性能提升**: 数据库层面分页可提升查询效率10倍以上
2. **业务准确性**: 按需求规范实现用户权限控制
3. **系统稳定性**: 减少微服务依赖，提高系统可用性
4. **代码质量**: 统一数据处理逻辑，提高代码可维护性

### 14.3 实施建议
1. **分阶段重构**: 先修复关键问题，再进行架构优化
2. **向后兼容**: 保持接口向后兼容，避免影响现有调用方
3. **充分测试**: 重点测试不同用户类型的权限控制
4. **监控告警**: 建立完善的监控体系，及时发现问题

---

**文档版本**: v1.0
**生成时间**: 2025-01-14
**分析范围**: getChannelSellData接口完整实现方案
**主要问题**: 需求实现不符、性能问题、架构设计问题
**优化重点**: 用户权限控制、数据库分页、异常处理
**技术栈**: Spring Boot + MyBatis-Plus + Feign + 统计服务
