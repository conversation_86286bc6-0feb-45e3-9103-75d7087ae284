# vCardSurfing业务逻辑分析报告

## 1. 接口基本信息

### 1.1 接口概述
- **方法名称**: `vCardSurfing(LocationUpdateHContext context)`
- **所属类**: `PackageMultiTypeSurfingAdapter`
- **功能描述**: H流程中V卡上网激活的叠加需求实现，负责处理V卡上网激活的完整业务流程
- **业务目标**: 实现V卡上网激活功能，包括V卡分配/查询、核心网交互、数据记录等完整流程
- **叠加需求特性**: 这是在原有H流程基础上的叠加需求变更，重点处理V卡上网激活逻辑

### 1.2 方法签名
```java
@Override
public void vCardSurfing(LocationUpdateHContext context)
```

## 2. 输入输出规范

### 2.1 输入参数结构
**LocationUpdateHContext context** - H流程上下文对象，包含以下核心字段：

```java
public class LocationUpdateHContext {
    // 基础信息
    private String imsi;                    // 主卡IMSI
    private String mcc;                     // 移动国家码
    private List<String> mnc;               // 移动网络码列表
    private String vimsi;                   // V卡IMSI
    private String msisdnToV;               // V卡手机号
    
    // 卡片信息
    private CardLuDTO cardLuDTO;            // 主卡详细信息
    private ChannelCardDTO channelAndCard;  // 客户与卡关系信息
    
    // 业务上下文
    private SurfingContext surfingContext;  // 上网上下文
    private CoreNetContext coreNetContext;  // 核心网交互上下文
    private PreActiveContext preActiveContext; // 预激活上下文
    
    // 流程控制
    private boolean flowPool;               // 是否流量池套餐
    private boolean needActivate;           // 是否需要激活
    private boolean suspendable;            // 手动激活标识
    
    // 短信相关
    private Map<String, List<String>> params; // 短信变量映射
    
    // 其他业务字段
    private List<Integer> rg;               // RG列表
    private String messageType;             // 消息类型
    private Date requestTime;               // 请求时间
}
```

### 2.2 输出规范
- **返回类型**: void
- **副作用**: 
  - 更新数据库表记录（cms_channel_surf_info、cms_channel_package_card等）
  - 与核心网元交互（HSS、UPCC、OTA）
  - 发送短信通知
  - 记录上网信息和明细

## 3. 业务流程图

### 3.1 主流程概览
```mermaid
graph TD
    A[vCardSurfing入口] --> B[刷新主卡过期时间]
    B --> C[判断是否发送使用中短信]
    C --> D[H卡签约前置处理]
    D --> E[H卡与核心网交互-限速签约]
    E --> F[H卡签约后置处理]
    F --> G[获取V卡开户信息]
    G --> H[V卡后置处理-短信变量]
    H --> I[V卡与核心网交互]
    I --> J[事务提交后激活确认]
```

### 3.2 V卡开户信息获取详细流程
```mermaid
graph TD
    A[getVcardAccountDetails] --> B[查询上网信息表]
    B --> C{是否有V卡记录?}
    C -->|无| D[走V卡新分配流程]
    C -->|有| E[检查卡池与套餐匹配MCC]
    E --> F{卡池是否支持当前MCC?}
    F -->|不支持| D
    F -->|支持| G[查询当前MCC的V卡记录]
    G --> H{是否找到可用V卡?}
    H -->|否| I[查询卡池中最新V卡]
    H -->|是| J[验证V卡状态]
    I --> J
    J --> K{V卡状态是否已分配?}
    K -->|否| D
    K -->|是| L[返回V卡开户信息]
    D --> M[分配新V卡]
    M --> N[记录上网信息]
    N --> L
```

### 3.3 核心网交互流程
```mermaid
graph TD
    A[invokeCoreNetWithVcard] --> B[初始化网元上下文]
    B --> C{是否需要GTP路由?}
    C -->|是| D[添加GTP路由]
    C -->|否| E[HSS开户检查]
    D --> E
    E --> F{HSS是否已开户?}
    F -->|否| G[执行HSS开户]
    F -->|是| H[UPCC开户检查]
    G --> H
    H --> I{是否需要UPCC签约?}
    I -->|是| J{UPCC是否已开户?}
    I -->|否| M[更新lastVimsi]
    J -->|否| K[执行UPCC开户]
    J -->|是| L[执行UPCC签约]
    K --> L
    L --> M
    M --> N[设置V卡Cancel标志]
    N --> O[执行OTA写卡]
```

## 4. 核心组件清单

### 4.1 Controller层
- **PackageMultiTypeSurfingAdapter**: 抽象适配器类，定义V卡上网激活的核心流程

### 4.2 Service层
- **CoreNetCaller**: 核心网调用服务，负责与HSS、UPCC、OTA等网元交互
- **FlowPoolService**: 流量池服务，处理流量池相关的V卡分配
- **SmsLuService**: 短信服务，负责发送使用中短信和地区欢迎短信
- **ChannelSurfDetailService**: 上网明细服务

### 4.3 DAO层
- **ChannelSurfMapper**: 上网信息表数据访问
- **ChannelCardMapper**: 客户与卡关系表数据访问  
- **ChannelSurfDetailMapper**: 上网明细表数据访问
- **CmsFlowpoolCardpoolRelationMapper**: 流量池卡池关系表数据访问
- **CmsPackageCardUpccRelationMapper**: 套餐卡UPCC关系表数据访问
- **CmsMccOtaRelationMapper**: MCC与OTA关系表数据访问

### 4.4 Entity类
- **ChannelSurf**: 上网信息实体
- **ChannelPackageCard**: 套餐与卡关系实体
- **VcardAccountDetailsDTO**: V卡开户详情DTO
- **LocationUpdateHContext**: H流程上下文
- **SurfingContext**: 上网上下文
- **CoreNetContext**: 核心网交互上下文

### 4.5 Feign客户端
- **PmsFeignClient**: 产品管理服务客户端
- **SmsFeignClient**: 短信服务客户端
- **OmsFeignClient**: 运营管理服务客户端
- **RmsFeignClient**: 资源管理服务客户端

## 5. 数据库设计

### 5.1 核心数据表结构

#### 5.1.1 cms_channel_surf_info (上网信息表)
```sql
CREATE TABLE cms_channel_surf_info (
  surf_id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '上网ID',
  package_unique_id varchar(32) NOT NULL COMMENT '套餐唯一ID',
  order_unique_id varchar(200) NOT NULL COMMENT '订单唯一ID', 
  imsi varchar(30) NOT NULL COMMENT '上网号码(V卡IMSI)',
  made_imsi varchar(30) NOT NULL COMMENT '写卡IMSI',
  internet_type varchar(1) NOT NULL COMMENT '上网方式:1-H卡,2-V卡',
  pool_id varchar(32) DEFAULT NULL COMMENT '归属卡池ID',
  mcc varchar(50) NOT NULL COMMENT '上报的MCC',
  himsi varchar(30) DEFAULT NULL COMMENT 'V卡绑定的H卡IMSI',
  start_time datetime DEFAULT NULL COMMENT '开始使用时间',
  end_time datetime DEFAULT NULL COMMENT '结束时间',
  corp_id varchar(32) NOT NULL COMMENT '厂商ID',
  PRIMARY KEY (surf_id)
);
```

#### 5.1.2 cms_channel_package_card (套餐与卡关系表)
```sql
CREATE TABLE cms_channel_package_card (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  imsi varchar(30) NOT NULL COMMENT 'IMSI号码',
  msisdn varchar(30) NOT NULL COMMENT 'MSISDN号码', 
  iccid varchar(30) NOT NULL COMMENT 'ICCID号码',
  package_type varchar(1) NOT NULL COMMENT '套餐类型:1-套餐,2-终端线下卡池,3-流量池,4-加油包',
  package_id varchar(32) NOT NULL COMMENT '套餐ID',
  package_unique_id varchar(32) NOT NULL COMMENT '套餐唯一ID',
  vimsi varchar(30) DEFAULT NULL COMMENT 'V卡IMSI',
  sign_biz_id varchar(32) DEFAULT NULL COMMENT '高速签约模板ID',
  limit_speed_sign_biz_id varchar(32) DEFAULT NULL COMMENT '低速签约模板ID',
  slow_speed_sign_biz_id varchar(32) DEFAULT NULL COMMENT '限速签约模板ID',
  package_status varchar(1) DEFAULT NULL COMMENT '套餐状态:1-待激活,2-激活中,3-已激活,4-已过期',
  surf_status varchar(1) DEFAULT NULL COMMENT '上网状态:1-正常,2-限速',
  active_time datetime DEFAULT NULL COMMENT '激活时间',
  expire_time datetime DEFAULT NULL COMMENT '过期时间',
  PRIMARY KEY (id)
);
```

### 5.2 主外键关系
- **cms_channel_surf_info.package_unique_id** → **cms_channel_package_card.package_unique_id**
- **cms_channel_surf_info.himsi** → **cms_channel_package_card.imsi** 
- **cms_channel_surf_info.pool_id** → **卡池相关表.pool_id**

## 6. 异常处理机制

### 6.1 异常类型定义
- **BizException**: 业务异常，用于处理业务逻辑错误
- **NotRollBackBizException**: 不回滚业务异常
- **HssException**: HSS网元交互异常
- **IBossException**: IBoss系统异常

### 6.2 错误码定义
```java
// 核心网交互相关错误码
"1001" - "HSS开户失败"
"1002" - "UPCC开户失败" 
"1003" - "路由添加失败"
"9999999" - "系统错误，请求失败"
```

### 6.3 异常处理策略
1. **网元交互失败**: 通过CoreNetContext记录失败状态，支持重试机制
2. **数据库操作失败**: 事务回滚，保证数据一致性
3. **远程服务调用失败**: Response.checkRemoteData统一处理
4. **业务规则验证失败**: 抛出BizException，中断流程

## 7. 业务规则说明

### 7.1 V卡分配规则
1. **优先使用已有V卡**: 先查询是否有可用的V卡记录
2. **卡池匹配验证**: 验证卡池是否支持当前MCC和套餐
3. **V卡状态检查**: 只使用状态为"已分配"的V卡
4. **MCC匹配优先**: 优先使用当前MCC的V卡记录

### 7.2 核心网交互规则  
1. **HSS开户**: V卡必须先在HSS开户才能使用
2. **UPCC签约**: 根据套餐类型和限速策略选择签约模板
3. **GTP路由**: 支持GTP路由的卡需要添加路由规则
4. **OTA写卡**: 最后执行OTA写卡操作

### 7.3 数据验证规则
1. **IMSI格式验证**: 确保IMSI格式正确
2. **套餐状态验证**: 只处理有效状态的套餐
3. **时间有效性**: 验证套餐在有效期内
4. **权限验证**: 验证渠道商操作权限

## 8. 叠加需求变更内容

### 8.1 新增业务逻辑
1. **H卡限速签约**: 新增H卡签约限速模板的逻辑（283行）
2. **V卡后置处理增强**: 增强了V卡获取后的短信变量处理（292行）
3. **事务同步机制**: 新增事务提交后的激活确认机制（298-303行）

### 8.2 优化改进点
1. **流程标准化**: 将V卡上网激活流程进行了标准化封装
2. **异常处理增强**: 完善了各个环节的异常处理机制
3. **日志记录完善**: 增加了详细的业务日志记录
4. **性能优化**: 优化了数据库查询和网元交互的性能

### 8.3 与原有逻辑的区别
- **原有逻辑**: 相对简单的V卡激活流程
- **叠加需求**: 增加了H卡限速签约、完善的后置处理、事务同步等复杂业务逻辑
- **核心变更**: 从单一的V卡激活扩展为完整的H+V卡协同激活流程

## 9. 关键代码实现分析

### 9.1 vCardSurfing主方法实现
```java
@Override
public void vCardSurfing(LocationUpdateHContext context) {
    // 1. 刷新主卡过期时间
    CardLuDTO card = context.getCardLuDTO();
    if (!CardType.PROVINCIAL_MOBILE.getType().equals(card.getType())) {
        log.debug("非省移动卡，刷新主卡过期时间，iccid = {}", card.getIccid());
        flushCardExpireTime(card.getIccid(), new Date());
    }

    // 2. 判断是否发送使用中短信
    boolean isSend = needSendUsingSms(context, CardTypeEnum.V_CARD.getType());

    // 3. H卡签约流程[签约限速] - 叠加需求新增
    postProcessBeforeSignatureWithHcard(context);
    boolean isPostProcessNecessary = invokeCoreNetWithHcard(context, true);

    // 4. H卡签约后置处理 - 叠加需求新增
    postProcessAfterSignatureWithHcard4V(context);

    // 5. 获取V卡开户信息[没有即分配/存在即查询]
    VcardAccountDetailsDTO vcardAccountDetails = getVcardAccountDetails(context);

    // 6. V卡后置处理 - 获取短信变量,mnc,apn等信息
    postGetVcard(context, vcardAccountDetails, isSend);

    // 7. V卡与核心网交互
    invokeCoreNetWithVcard(context, vcardAccountDetails);

    // 8. 事务提交后激活确认 - 叠加需求新增
    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
        @Override
        public void afterCommit() {
            postFristActivated(context);
        }
    });
}
```

### 9.2 V卡开户信息获取核心逻辑
```java
protected VcardAccountDetailsDTO getVcardAccountDetails(LocationUpdateHContext context) {
    String tagName = getTagName();
    log.debug("[H流程] [{}套餐] [V卡激活] 判断卡池是否支持当前MCC", tagName);

    // 1. 查询上网信息表，判断是否有存在的V卡
    SurfingContext surfingContext = (SurfingContext) context.getSurfingContext();
    ChannelPackageCard packageCardRecord = surfingContext.getPackageCardRecord();
    List<ChannelSurf> surfRecords = channelSurfMapper.selectList(
        Wrappers.<ChannelSurf>lambdaQuery()
            .eq(ChannelSurf::getInternetType, ChannelSurf.InternetTypeEnum.V.getValue())
            .eq(ChannelSurf::getHimsi, context.getImsi())
            .eq(ChannelSurf::getPackageUniqueId, packageCardRecord.getPackageUniqueId())
    );

    // 2. 没有V卡上网记录，走新分配流程
    if (CollectionUtils.isEmpty(surfRecords)) {
        log.debug("[H流程] [{}套餐] [V卡激活] 未查询到V卡上网记录", tagName);
        return allocateVcard(context);
    }

    // 3. 有V卡记录，验证卡池与套餐是否匹配当前MCC
    String mcc = context.getMcc();
    List<String> checkedPoolIdList = validatePoolMccCompatibility(context, surfRecords);

    if (CollectionUtils.isEmpty(checkedPoolIdList)) {
        log.debug("[H流程] [{}套餐] [V卡激活] 卡池与套餐不匹配当前MCC", tagName);
        return allocateVcard(context);
    }

    // 4. 查询可用的V卡记录
    ChannelSurf presentSurfRecord = findAvailableVcard(context, checkedPoolIdList);
    if (presentSurfRecord == null) {
        return allocateVcard(context);
    }

    // 5. 验证V卡状态并返回开户信息
    return validateAndGetVcardDetails(context, presentSurfRecord);
}
```

### 9.3 核心网交互详细实现
```java
public void invokeCoreNetWithVcard(LocationUpdateHContext context, VcardAccountDetailsDTO vcardAccountDetails) {
    log.debug("[H流程] V卡与网元交互流程. 开始");

    // 1. 初始化网元上下文
    CoreNetContext coreNetContext = context.getCoreNetContext();
    String vimsi = vcardAccountDetails.getVimsi();
    CoreNetContext.VcardUpdateMarking vcardUpdateMarking = coreNetContext.initVcard(vimsi);

    // 2. GTP路由处理
    CardLuDTO card = context.getCardLuDTO();
    if ("1".equals(card.getSupportGtpRoute())) {
        log.debug("[H流程] V卡与网元交互流程， 需要在gtp路由");
        coreNetCaller.addRoute(vimsi, card.getRouteId());
        vcardUpdateMarking.setEdited(true);
        vcardUpdateMarking.setRouteId(card.getRouteId());
    }

    // 3. HSS开户处理
    processHssOpening(vcardAccountDetails, card, vcardUpdateMarking);

    // 4. UPCC开户和签约处理
    processUpccOperations(context, vcardAccountDetails, vcardUpdateMarking);

    // 5. 更新lastVimsi
    updateLastVimsi(context, vcardAccountDetails);

    // 6. 设置V卡Cancel标志
    redis.opsForValue().set(String.format(BizConstants.V_NEED_CANCEL_RECORD, vimsi), "-1");

    // 7. OTA写卡
    executeOtaWriting(context, vcardAccountDetails);

    log.debug("[H流程] V卡与网元交互流程. 结束");
}
```

## 10. 性能优化和监控

### 10.1 性能优化措施
1. **数据库查询优化**: 使用索引优化查询性能
2. **缓存机制**: Redis缓存热点数据
3. **异步处理**: 短信发送等非关键路径异步处理
4. **批量操作**: 减少数据库交互次数

### 10.2 监控指标
1. **接口响应时间**: 监控vCardSurfing方法执行时间
2. **成功率**: 监控V卡激活成功率
3. **异常率**: 监控各类异常发生频率
4. **核心网交互**: 监控HSS、UPCC、OTA交互成功率

## 11. 测试建议

### 11.1 单元测试覆盖
1. **V卡分配逻辑测试**: 测试各种场景下的V卡分配
2. **核心网交互测试**: Mock核心网服务进行测试
3. **异常处理测试**: 测试各种异常场景的处理
4. **数据库操作测试**: 测试数据一致性

### 11.2 集成测试场景
1. **完整流程测试**: 端到端的V卡激活流程测试
2. **并发测试**: 高并发场景下的稳定性测试
3. **故障恢复测试**: 网元故障时的恢复能力测试
4. **数据一致性测试**: 分布式事务一致性测试

---

**报告生成时间**: 2025-01-14
**分析范围**: PackageMultiTypeSurfingAdapter.vCardSurfing方法完整业务逻辑
**变更类型**: H流程V卡上网激活叠加需求实现
**技术栈**: Spring Boot + MyBatis-Plus + Redis + Feign + 分布式事务
