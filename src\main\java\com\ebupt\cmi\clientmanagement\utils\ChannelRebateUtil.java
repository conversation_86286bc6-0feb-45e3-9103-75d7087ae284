package com.ebupt.cmi.clientmanagement.utils;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelDistributorDetail;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelOrderDetail;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsChannelMarketBillflow;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsChannelMarketingRebate;
import com.ebupt.cmi.clientmanagement.domain.vo.RebateDeductionVO;
import com.ebupt.cmi.clientmanagement.domain.vo.RebateTransVO;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.mapper.ChannelDistributorDetailMapper;
import com.ebupt.cmi.clientmanagement.mapper.CmsChannelMarketBillFlowMapper;
import com.ebupt.cmi.clientmanagement.mapper.CmsChannelMarketingRebateMapper;
import com.ebupt.cmi.clientmanagement.service.OutSideApiRealNameSysService;
import com.ebupt.cmi.clientmanagement.service.channel.impl.ChannelDistributorsServiceImpl;
import com.ebupt.cmi.clientmanagement.service.channelself.BatchPackageBuyService;
import com.ebupt.cmi.clientmanagement.service.channelself.impl.BatchPackageBuyServiceImpl;
import com.ebupt.cmi.clientmanagement.service.channelself.impl.ChannelSelfServiceImpl;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@RefreshScope
public class ChannelRebateUtil {

    @Autowired
    private CmsChannelMarketingRebateMapper channelMarketingRebateMapper;

    @Autowired
    private CmsChannelMarketBillFlowMapper channelMarketBillFlowMapper;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private ChannelDistributorDetailMapper channelDistributorDetailMapper;

    @Autowired
    private BeanFactory beanFactory;

    @Value("${retryCount}")
    private Integer retryCount;

    public void getRebateInfo(RebateTransVO rebateTransVO) {

        if (rebateTransVO.getPackagePrice().compareTo(BigDecimal.ZERO) == 0){
            rebateTransVO.setRemainPackagePrice(BigDecimal.ZERO);
            return;
        }

        Date targetDate;

        try {

            log.info("开始计算营销返利扣除额度");

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            targetDate = sdf.parse("2099-12-31 23:59:59");

        }catch (Exception e){
            log.warn("扣除营销返利额度失败，原因为：",e);
            throw new BizException("deduct rebate amount fail");
        }

        Boolean isReSeach = true;

        Integer a = 0;

        while (isReSeach){

            if (a > retryCount){
                throw new BizException("The current number of purchasers is large, so please purchase later");
            }

            BigDecimal packagePrice = rebateTransVO.getPackagePrice();

            ChannelRebateUtil channelRebateUtil = beanFactory.getBean(ChannelRebateUtil.class);

            List<CmsChannelMarketingRebate> allRebateList = channelRebateUtil.getCmsChannelMarketingRebates(rebateTransVO);
            List<CmsChannelMarketingRebate> rebateList = new ArrayList<>();

            BigDecimal totalAmount = BigDecimal.ZERO;

            Date now = new Date();

            for (CmsChannelMarketingRebate rebate : allRebateList){
                totalAmount = totalAmount.add(rebate.getRemainAmount());
                if (rebate.getEffectiveTime()  == null ||  !rebate.getEffectiveTime().after(now)){
                    log.info("只使用生效时间为空或生效时间小于等于当前时间的营销款");
                    rebateList.add(rebate);
                }
            }


            if (CollectionUtils.isEmpty(rebateList)){
                rebateTransVO.setRemainPackagePrice(rebateTransVO.getPackagePrice());

                //记录渠道商押金流水
                recordDepositFlow(rebateTransVO);

                return;
            }

            List<CmsChannelMarketingRebate> expiredInfo = rebateList.stream()
                    .filter(rebate -> {
                        return rebate.getExpiryTime().equals(targetDate);
                    })
                    .collect(Collectors.toList());

            rebateList.removeAll(expiredInfo);

            List<RebateDeductionVO> deductionVOS = new ArrayList<>();

            //先扣除过期并且有退订金额的活动
            packagePrice = calculateDeduction(expiredInfo, packagePrice, deductionVOS);

            //再扣除正常活动
            packagePrice = calculateDeduction(rebateList, packagePrice, deductionVOS);

            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            def.setTimeout(30); // 设置超时时间为30秒,避免死锁。
            TransactionStatus status = transactionManager.getTransaction(def);

            try {
                rebateTransVO.setRemainPackagePrice(packagePrice);
                reduceRebateAndRecordFlow(deductionVOS,rebateTransVO,totalAmount);
                rebateTransVO.setTransactionManager(transactionManager);
                rebateTransVO.setStatus(status);
                rebateTransVO.setIsSuccess("1");
                isReSeach = false;

            }catch (Exception e){
                transactionManager.rollback(status);
                if (! (e instanceof BizException) || !"889900".equals(((BizException) e).getCode())){
                    throw e;
                }
                log.warn("重新计算使用金额");

                ++a;
            }

        }

        log.info("营销返利扣款流程结束");
    }

    private void recordDepositFlow(RebateTransVO rebateTransVO) {
        ChannelDistributorDetail targetChannelDetail;

        if ("1".equals(rebateTransVO.getIsGlobalTransactional())){
            targetChannelDetail = rebateTransVO.getChannelDistributorDetail();
        }else {
            targetChannelDetail = channelDistributorDetailMapper.selectChannelDetail(rebateTransVO.getCorpId());
        }

        BatchPackageBuyService batchPackageBuyService = beanFactory.getBean(BatchPackageBuyService.class);
        batchPackageBuyService.reduceDepositAndRecordFlow(targetChannelDetail, rebateTransVO.getRemainPackagePrice(),
                rebateTransVO.getCooperationMode(), rebateTransVO.getOrderUniqueId(), rebateTransVO.getOrderDate());
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<CmsChannelMarketingRebate> getCmsChannelMarketingRebates(RebateTransVO rebateTransVO) {
        List<CmsChannelMarketingRebate> rebateList = channelMarketingRebateMapper.selectList(Wrappers.lambdaQuery(CmsChannelMarketingRebate.class)
                .eq(CmsChannelMarketingRebate::getCorpId, rebateTransVO.getCorpId())
                .eq(CmsChannelMarketingRebate::getType,CmsChannelMarketingRebate.TypeEnum.CONSIGNMENT.getType())
                .ge(CmsChannelMarketingRebate::getExpiryTime, new Date())
                .gt(CmsChannelMarketingRebate::getRemainAmount, BigDecimal.ZERO)
                .orderByAsc(CmsChannelMarketingRebate::getExpiryTime));
        return rebateList;
    }

    public void reduceRebateAndRecordFlow(List<RebateDeductionVO> deductionVOS,RebateTransVO rebateTransVO,BigDecimal totalAmount) {

        //记录渠道商押金流水
        recordDepositFlow(rebateTransVO);

        //扣除账户返利额度
        for (RebateDeductionVO deductionVO:deductionVOS){

            Integer row = channelMarketingRebateMapper.updateRemainAmount(deductionVO.getId(),deductionVO.getDeductionAmount(),deductionVO.getVersion());

            if (row == 0){
                throw new BizException("deduct rebate amount fail","889900");
            }
            BigDecimal useMarket = deductionVO.getDeductionAmount();
            BigDecimal remainMarket = deductionVO.getDeposit();

            Iterator<ChannelOrderDetail> iterator = rebateTransVO.getOrderDetails().iterator();

            while (iterator.hasNext()) {
                ChannelOrderDetail orderDetail = iterator.next();

                if (useMarket.compareTo(BigDecimal.ZERO) <= 0){
                    break;
                }

                useMarket = useMarket.subtract(orderDetail.getAmount());

                if (useMarket.compareTo(BigDecimal.ZERO) < 0){
                    BigDecimal amount = useMarket.add(orderDetail.getAmount()).negate();
                    totalAmount = totalAmount.add(amount);
                    remainMarket = remainMarket.add(amount);
                    channelMarketBillFlowMapper.insert(CmsChannelMarketBillflow.builder()
                            .corpId(rebateTransVO.getCorpId())
                            .orderId(String.valueOf(orderDetail.getId()))
                            .type("1".equals(rebateTransVO.getIsRefuel())?"3":"2")
                            .currencyCode(rebateTransVO.getCurrencyCode())
                            .amount(amount)
                            .deposit(remainMarket.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : remainMarket)
                            .activityId(deductionVO.getActivityId())
                            .totalAmount(totalAmount.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : totalAmount )
                            .totalOrderId(rebateTransVO.getOrderUniqueId())
                            .rebateId(String.valueOf(deductionVO.getId()))
                            .build());
                    orderDetail.setAmount(useMarket.negate());
                    break;
                }else {

                    totalAmount = totalAmount.subtract(orderDetail.getAmount());
                    remainMarket = remainMarket.subtract(orderDetail.getAmount());

                    channelMarketBillFlowMapper.insert(CmsChannelMarketBillflow.builder()
                            .corpId(rebateTransVO.getCorpId())
                            .orderId(String.valueOf(orderDetail.getId()))
                            .type("1".equals(rebateTransVO.getIsRefuel())?"3":"2")
                            .currencyCode(rebateTransVO.getCurrencyCode())
                            .amount(orderDetail.getAmount().negate())
                            .deposit(remainMarket.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : remainMarket)
                            .activityId(deductionVO.getActivityId())
                            .totalAmount(totalAmount)
                            .totalOrderId(rebateTransVO.getOrderUniqueId())
                            .rebateId(String.valueOf(deductionVO.getId()))
                            .build());
                    if (rebateTransVO.getOrderDetails().size() > 1){
                        iterator.remove();
                    }else if (rebateTransVO.getOrderDetails().size() == 1){
                        rebateTransVO.getOrderDetails().clear();
                        break;
                    }


                }
            }

        };

    }


    private BigDecimal calculateDeduction(List<CmsChannelMarketingRebate> infos,BigDecimal packagePrice,List<RebateDeductionVO> deductionVOS){


        Map<Long,BigDecimal> activeDepositMap = new HashMap<>();

        infos.forEach(info->{
            if (activeDepositMap.get(info.getActivityId()) == null){
                activeDepositMap.put(info.getActivityId(),info.getRemainAmount());
            }else {
                activeDepositMap.put(info.getActivityId(),activeDepositMap.get(info.getActivityId()).add(info.getRemainAmount()));
            }
        });


        for (CmsChannelMarketingRebate rebate : infos){

            if (packagePrice.compareTo(BigDecimal.ZERO) <= 0) {
                // 如果 packagePrice 已经被扣减至 0 或以下，停止循环
                return packagePrice;
            }

            if (packagePrice.compareTo(rebate.getRemainAmount()) < 0){
                deductionVOS.add(RebateDeductionVO.builder()
                        .activityId(rebate.getActivityId())
                        .deductionAmount(packagePrice)
                        .version(rebate.getVersion())
                        .remainAmount(rebate.getRemainAmount())
                        .deposit(activeDepositMap.get(rebate.getActivityId()))
                        .id(rebate.getId())
                        .build());
                return BigDecimal.ZERO;
            }else {
                deductionVOS.add(RebateDeductionVO.builder()
                        .activityId(rebate.getActivityId())
                        .deductionAmount(rebate.getRemainAmount())
                        .version(rebate.getVersion())
                        .remainAmount(rebate.getRemainAmount())
                        .deposit(activeDepositMap.get(rebate.getActivityId()))
                        .id(rebate.getId())
                        .build());
                packagePrice = packagePrice.subtract(rebate.getRemainAmount());
                activeDepositMap.put(rebate.getActivityId(),activeDepositMap.get(rebate.getActivityId()).subtract(rebate.getRemainAmount()));
            }
        }

        return packagePrice;
    }

}
