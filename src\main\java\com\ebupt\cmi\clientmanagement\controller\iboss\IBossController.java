package com.ebupt.cmi.clientmanagement.controller.iboss;

import com.ebupt.cmi.clientmanagement.domain.dto.ChargeRecordDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelChargeRecord;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsChannelChargeDetail;
import com.ebupt.cmi.clientmanagement.domain.req.IBossRechargeReq;
import com.ebupt.cmi.clientmanagement.domain.req.IBossSelectReq;
import com.ebupt.cmi.clientmanagement.domain.req.RebateRechargeReq;
import com.ebupt.cmi.clientmanagement.domain.response.ChannelQuota;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.*;
import com.ebupt.cmi.clientmanagement.feign.back.BackFeignClient;
import com.ebupt.cmi.clientmanagement.service.iboss.IBossService;
import com.ebupt.cmi.common.operationlog.annotation.OperationLog;
import com.ebupt.cmi.common.operationlog.enums.OperationTypeEnum;
import com.ebupt.elk.annotion.NormalLog;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@RestController
@ApiOperation("IBoss相关接口")
@RequiredArgsConstructor
public class IBossController {

    private final IBossService iBossService;

    private final BackFeignClient backFeignClient;

    @PostMapping("/IBoss/recharge")
    @ApiOperation(value = "IBoss充值接口")
    public Response<Void> IBossRecharge(@RequestBody IBossRechargeReq iBossRechargeReq) {
        return iBossService.recharge(iBossRechargeReq);
    }

    @PostMapping("/rebate/recharge")
    @ApiOperation(value = "营销返利充值接口")
    public Response<Map<Long,Long>> rebateRecharge(@RequestBody List<RebateRechargeReq> rebateRechargeReqs) {
        return Response.ok(iBossService.rebateRecharge(rebateRechargeReqs));
    }

    @PostMapping("/IBoss/query")
    @ApiOperation(value = "IBoss查询接口")
    public Response<ChannelQuota> IBossSelect(@RequestBody IBossSelectReq iBossSelectReq) {
        return iBossService.query(iBossSelectReq);
    }

    @PostMapping("/IBoss/payBill")
    @ApiOperation(value = "账单缴付接口")
    @OperationLog(operationName = "渠道自服务-账单缴付", operationType = OperationTypeEnum.ADD)
    public Response<Void> payBill( PayBillVO payBillVO) {
        iBossService.payBill(payBillVO);
        return Response.ok();
    }

    @GetMapping("/IBoss/cancelPayBill")
    @ApiOperation(value = "账单缴付取消接口")
    @OperationLog(operationName = "渠道自服务-账单缴付取消", operationType = OperationTypeEnum.DELETE)
    public Response<Void> cancelPayBill(@RequestParam long id, @RequestParam(required = false) String userName) {
        iBossService.cancelPayBill(id, userName);
        return Response.ok();
    }


    @PostMapping("/IBoss/getPage")
    @ApiOperation(value = "账单缴付查询接口")
    public Response<List<CmsChannelChargeDetail>> getPage(@RequestBody @Validated ChargePageVO chargePageVO) {
        return Response.ok(iBossService.getPage(chargePageVO));
    }

    @PostMapping("/IBoss/auth")
    @ApiOperation(value = "账单缴付审批接口")
    public Response<Void> auth(@RequestBody @Validated AuthPayBillVO authPayBillVO) {
        iBossService.authPayBill(authPayBillVO);
        return Response.ok();
    }



    @PostMapping("/IBoss/pushFinish")
    @ApiOperation(value = "已阅接口")
    public Response<Void> pushFinish(@RequestBody PushFinishVO pushFinishVO) {
        SubmitProcessReq submitProcessReq = new SubmitProcessReq();
        submitProcessReq.setType("2");
        submitProcessReq.setProcUniqueId(pushFinishVO.getProcUniqueId());
        submitProcessReq.setTodoUniqueId(pushFinishVO.getTodoUniqueId());
        submitProcessReq.setProcId(pushFinishVO.getProcId());
        submitProcessReq.setTodoNodeId("1");
        backFeignClient.processSubmission(submitProcessReq);
        return Response.ok();
    }

    @GetMapping("/IBoss/downLoad")
    @ApiOperation(value = "文件下载接口-缴付账单下载付款证明")
    public void downLoad(HttpServletResponse response, @RequestParam long id) {
        iBossService.downLoad(id, response);
    }

    @GetMapping("/IBoss/downLoad/paymentProof")
    @ApiOperation(value = "文件下载接口-渠道线下充值下载付款证明")
    public void chargeDownLoad(HttpServletResponse response, @RequestParam long id) {
        iBossService.chargeDownLoad(id, response);
    }
    @GetMapping("/IBoss/downLoad2")
    @ApiOperation(value = "文件下载接口-缴付账单下载付款证明-API")
    public ResponseEntity<Resource> downLoad2(@RequestParam long id) {
        return iBossService.downLoad2(id);

    }

    @GetMapping("/IBoss/downLoad3")
    @ApiOperation(value = "文件下载接口-缴付账单下载发票-外部api")
    public ResponseEntity<Resource> downLoad3(@RequestParam String fileAddress, @RequestParam(required = false) String corpName, @RequestParam(required = false) String invoiceNo) {
        return iBossService.downLoad3(fileAddress, corpName,invoiceNo);

    }

    @PostMapping("getChargeRecord")
    @ApiOperation(value = "获取指定时间内预存款充值记录")
    public Response<List<ChannelChargeRecord>> getChargeRecord(@RequestBody ChargeRecordDTO chargeRecordDTO) {
        return Response.ok(iBossService.getChargeRecord(chargeRecordDTO));

    }




}
