package com.ebupt.cmi.clientmanagement.service.iboss.Impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebupt.cmi.clientmanagement.domain.dto.ChargeRecordDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.*;
import com.ebupt.cmi.clientmanagement.domain.entity.redis.RedissonLock;
import com.ebupt.cmi.clientmanagement.domain.enums.TodoProcessIdEnum;
import com.ebupt.cmi.clientmanagement.domain.properties.PaymentEmailProps;
import com.ebupt.cmi.clientmanagement.domain.req.ChannelIncomeInfoMonth;
import com.ebupt.cmi.clientmanagement.domain.req.IBossRechargeReq;
import com.ebupt.cmi.clientmanagement.domain.req.IBossSelectReq;
import com.ebupt.cmi.clientmanagement.domain.req.RebateRechargeReq;
import com.ebupt.cmi.clientmanagement.domain.response.ApiResponseEnum;
import com.ebupt.cmi.clientmanagement.domain.response.ChannelQuota;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.AuthPayBillVO;
import com.ebupt.cmi.clientmanagement.domain.vo.ChargePageVO;
import com.ebupt.cmi.clientmanagement.domain.vo.PayBillVO;
import com.ebupt.cmi.clientmanagement.domain.vo.SubmitProcessReq;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.exception.IBossException;
import com.ebupt.cmi.clientmanagement.feign.back.BackFeignClient;
import com.ebupt.cmi.clientmanagement.feign.stat.StatFeignClient;
import com.ebupt.cmi.clientmanagement.mapper.*;
import com.ebupt.cmi.clientmanagement.service.ChannelService;
import com.ebupt.cmi.clientmanagement.service.iboss.IBossService;
import com.ebupt.cmi.clientmanagement.service.iboss.strategy.ChannelTypeStrategy;

import com.ebupt.cmi.clientmanagement.utils.GetFilePathUtil;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class IBossServiceImpl implements IBossService {

    private final ChannelTypeStrategy depositStrategy;

    private final ChannelTypeStrategy preStorageStrategy;

    private final ChannelMapper channelMapper;

    private final ChannelDistributorDetailMapper channelDistributorDetailMapper;


    private final CmsChannelChargeDetailMapper cmsChannelChargeDetailMapper;

    private final ChannelService channelService;

    @Resource
    private PaymentEmailProps paymentEmailProps;

    private final StatFeignClient statFeignClient;

    @Value("${file-upload.base-path}")
    String nfspath;

    private final RedissonLock redissonLock;

    private final BackFeignClient backFeignClient;

    private final CmsDepositChargeRecordMapper cmsDepositChargeRecordMapper;

    private final CmsChannelMarketingRebateMapper channelMarketingRebateMapper;

    private final CmsChannelMarketBillFlowMapper channelMarketBillFlowMapper;

    private final CmsChannelMarketBillflowA2zMapper channelMarketBillflowA2zMapper;

    private final ChannelChargeRecordMapper channelChargeRecordMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<Void> recharge(IBossRechargeReq iBossRechargeReq) {
        log.debug("IBoss充值接收到请求：{}", iBossRechargeReq);
        String ebsCode = iBossRechargeReq.getEbsCode();
        ChannelDistributorDetail channelDistributorDetail = getChannelDistributorDetailByEbsCode(ebsCode, false);
        checkData(iBossRechargeReq, channelDistributorDetail);
        String channelType = channelDistributorDetail.getChannelType();
        ChannelTypeStrategy channelTypeStrategy;
        if (iBossRechargeReq.isSpecial()) {
            channelTypeStrategy = depositStrategy;
        } else if ("12348".contains(iBossRechargeReq.getType())) {
            switch (channelType) {
                case "1":
                    channelTypeStrategy = depositStrategy;
                    break;
                case "2":
                    channelTypeStrategy = preStorageStrategy;
                    break;
                default:
                    return Response.error(ApiResponseEnum.CHANNEL_TYPE_ERROR);
            }
        } else if ("56910".contains(iBossRechargeReq.getType())) {
            channelTypeStrategy = depositStrategy;
        } else {
            return Response.error(ApiResponseEnum.DEPOSIT_TYPE_ERROR);
        }
        return channelTypeStrategy.proceed(iBossRechargeReq, channelDistributorDetail);
    }


    /**
     * 检查币种和合作模式
     *
     * @param iBossRechargeReq         x
     * @param channelDistributorDetail x
     */
    private void checkData(IBossRechargeReq iBossRechargeReq, ChannelDistributorDetail channelDistributorDetail) {
        String currencyCode = channelDistributorDetail.getCurrencyCode();
        String currencyCodeReq = iBossRechargeReq.getCurrencyCode();
        switch (currencyCodeReq) {
            case "HKD":
                currencyCodeReq = "344";
                break;
            case "CNY":
                currencyCodeReq = "156";
                break;
            case "USD":
                currencyCodeReq = "840";
                break;
            default:
                throw new IBossException(ApiResponseEnum.CURRENCY_ERROR.getCode());
        }
        iBossRechargeReq.setCurrencyCode(currencyCodeReq);
        if (!currencyCodeReq.equals(currencyCode)) {
            throw new IBossException(ApiResponseEnum.CURRENCY_NOT_FIT.getCode());
        }

        List<String> allowRechargeType = new ArrayList<>();
        if (channelDistributorDetail.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.CONSIGNMENT.getType())) {
            allowRechargeType.add("1");
            allowRechargeType.add("2");
            allowRechargeType.add("3");
            allowRechargeType.add("4");
            allowRechargeType.add("8");
        }

        if (channelDistributorDetail.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.A2Z.getType()) ||
                channelDistributorDetail.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.RESOURCE_COOPERATION.getType())) {
            allowRechargeType.add("5");
            allowRechargeType.add("6");
            allowRechargeType.add("9");
            allowRechargeType.add("10");
        }

        if (!allowRechargeType.contains(iBossRechargeReq.getType())) {
            throw new IBossException(ApiResponseEnum.COOPERATION_MODE_NOT_FIT.getCode());
        }
    }

    @Override
    public Response<ChannelQuota> query(IBossSelectReq iBossSelectReq) {
        String ebsCode = iBossSelectReq.getEbsCode();
        ChannelDistributorDetail channelDistributorDetail = getChannelDistributorDetailByEbsCode(ebsCode, true);
        ChannelQuota channelQuota = new ChannelQuota();
        if (channelDistributorDetail.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.CONSIGNMENT.getType())) {
            String total = channelDistributorDetail.getTotalDeposit().toString();
            String deposit = channelDistributorDetail.getDeposit().toString();
            String available = (channelDistributorDetail.getDeposit().add(channelDistributorDetail.getCreditAmount())).toString();
            String used = channelDistributorDetail.getTotalDeposit().subtract(channelDistributorDetail.getDeposit()).toString();
            //押金模式，不变，总额度就是总额度;预存款模式，返回的的总额度等于可用额度。
            channelQuota.setTotal("1".equals(channelDistributorDetail.getChannelType()) ? total : deposit);
            channelQuota.setAvailable(available);
            channelQuota.setUsed(used);
            channelQuota.setCurrencyCode(ChannelQuota.getCurrency(channelDistributorDetail.getCurrencyCode()));
        }

        if (channelDistributorDetail.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.A2Z.getType()) ||
                channelDistributorDetail.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.RESOURCE_COOPERATION.getType())) {

            channelQuota.setCurrencyCode(ChannelQuota.getCurrency(channelDistributorDetail.getCurrencyCode()));
            channelQuota.setA2zPreDeposit(channelDistributorDetail.getA2zPreDeposit().toString());
        }

        return Response.okForApi(channelQuota);
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional(rollbackFor = Exception.class)
    public void payBill(PayBillVO payBillVO) {

        String corpId = payBillVO.getCorpId();

        String lockName = "charging" + payBillVO.getAccountId();

        boolean lock = redissonLock.tryLock(lockName);

        if (!lock) {
            throw new BizException("invoice is in progress, please try again later");
        }
        try {
            //校验账单状态
            if ("1".equals(payBillVO.getChargeType())) {
                ChannelIncomeInfoMonth channelIncomeInfoMonth = statFeignClient.getBillChannelType(payBillVO.getAccountId()).get();
                if (!"034".contains(channelIncomeInfoMonth.getChargeStatus())) {
                    throw new BizException("the status is not allowed to pay");
                }
                if (payBillVO.isRepeatedUpload() && !"34".contains(channelIncomeInfoMonth.getChargeStatus())) {
                    throw new BizException("the status is not allowed to upload repeatedly, please try to refresh page");
                }
            } else {
                CmsDepositChargeRecord cmsDepositChargeRecord = cmsDepositChargeRecordMapper.selectOne(Wrappers.lambdaQuery(CmsDepositChargeRecord.class)
                        .eq(CmsDepositChargeRecord::getId, payBillVO.getAccountId()));
                if (payBillVO.isRepeatedUpload() && !"6".equals(cmsDepositChargeRecord.getChargeStatus())) {
                    throw new BizException("the status is not allowed to upload repeatedly, please try to refresh page");
                }
                if (!payBillVO.isRepeatedUpload() && !"4".equals(cmsDepositChargeRecord.getChargeStatus())) {
                    throw new BizException("the status is not allowed to charge");
                }
            }
            MultipartFile file = payBillVO.getPaymentProofs();

            String coverPath = null;

            if (file != null && !file.isEmpty()) {
                //判断文件后缀是否满足要求
                String originalFilename = file.getOriginalFilename();
                assert originalFilename != null;
                String extension = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase();

                if (!paymentEmailProps.getSuffix().contains(extension)) {
                    throw new BizException("Payslip document type incorrect，Current file support format：" + paymentEmailProps.getSuffix());
                }

                File dir = new File(nfspath);
                if (!dir.exists()) {
                    if (!dir.mkdirs()) {
                        throw new BizException("Payslip upload failed");
                    }
                }
                coverPath = dir + File.separator + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmmssSSS")) + file.getOriginalFilename();
                File f = new File(coverPath);

                //保存付款证明
                try {
                    file.transferTo(f);
                } catch (Exception e) {
                    log.warn("保存付款证明失败原因: ", e);
                    throw new BizException("Payslip upload failed");
                }
            }

            String procUniqueId = UUID.randomUUID().toString().replace("-", "");
            CmsChannelChargeDetail cmsChannelChargeDetail = CmsChannelChargeDetail.builder()
                    .amount(payBillVO.getAmount().multiply(new BigDecimal(100)))
                    .paymentProofAddress(coverPath)
                    .corpId(corpId)
                    .authStatus("1")
                    .chargeStatus("1")
                    .chargeType(payBillVO.getChargeType())
                    .chargeTime(new Date())
                    .toDoItemId(procUniqueId)
                    .accountId(payBillVO.getAccountId())
                    .build();

            cmsChannelChargeDetailMapper.insert(cmsChannelChargeDetail);

            Channel channel = channelMapper.selectOne(Wrappers.lambdaQuery(Channel.class)
                    .eq(Channel::getCorpId, corpId));

            SubmitProcessReq submitProcessReq = new SubmitProcessReq();
            HashMap<String, Object> map = new HashMap<>();
            HashMap<String, Object> titleMap = new HashMap<>();
            titleMap.put("corpName", channel.getCorpName());
            titleMap.put("date", new SimpleDateFormat("yyyyMMdd").format(new Date()));
            submitProcessReq.setTitleParamMap(titleMap);
            if ("1".equals(payBillVO.getChargeType())) {
                statFeignClient.updateChargeStatus(payBillVO.getAccountId(), "1").get();
                submitProcessReq.setTitle(channel.getCorpName() + "缴费确认");
                submitProcessReq.setProcId(TodoProcessIdEnum.PAY_BILL.getId());
                map.put("page", "4");
            } else {
                //修改状态为缴费待审批
                UpdateWrapper<CmsDepositChargeRecord> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("id", payBillVO.getAccountId()).set("charge_status", "5");
                cmsDepositChargeRecordMapper.update(null, updateWrapper);
                submitProcessReq.setTitle(channel.getCorpName() + "充值确认");
                submitProcessReq.setProcId("16");
                map.put("page", "6");
            }

            submitProcessReq.setType("1");
            submitProcessReq.setProcUniqueId(procUniqueId);
            submitProcessReq.setTodoNodeId("1");
            map.put("id", cmsChannelChargeDetail.getId());
            submitProcessReq.setParamMap(map);
            backFeignClient.processSubmission(submitProcessReq).get();

        } finally {
            try {
                if (redissonLock.isHeldByCurrentThread(lockName)) {
                    redissonLock.unlock(lockName);
                }
            } catch (Exception e) {
                log.warn("redis解锁失败");
            }
        }
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional(rollbackFor = Exception.class)
    public void cancelPayBill(long id, String userName) {
        CmsChannelChargeDetail cmsChannelChargeDetail = cmsChannelChargeDetailMapper.selectOne(Wrappers.lambdaQuery(CmsChannelChargeDetail.class)
                .eq(CmsChannelChargeDetail::getAccountId, id)
                .orderByDesc(CmsChannelChargeDetail::getChargeTime)
                .orderByDesc(CmsChannelChargeDetail::getId));
        if (cmsChannelChargeDetail == null || !cmsChannelChargeDetail.getChargeStatus().equals("1")) {
            throw new BizException("Invoice status is not \"Pending payment confirmed\"");
        }
        boolean lock = redissonLock.tryLock(String.valueOf(cmsChannelChargeDetail.getId()));
        if (!lock) {
            throw new BizException("invoice is in progress, please try again later");
        }
        try {
            cmsChannelChargeDetail.setChargeStatus("4");
            cmsChannelChargeDetailMapper.updateById(cmsChannelChargeDetail);
            statFeignClient.updateChargeStatus(id, "4").get();
            // 删除代办
            SubmitProcessReq submitProcessReq = new SubmitProcessReq();
            submitProcessReq.setProcUniqueId(cmsChannelChargeDetail.getToDoItemId());
            submitProcessReq.setType("3");
            submitProcessReq.setProcId("4");
            submitProcessReq.setTodoNodeId("1");
            backFeignClient.processSubmission(submitProcessReq);
        } finally {
            try {
                if (redissonLock.isHeldByCurrentThread(String.valueOf(cmsChannelChargeDetail.getId()))) {
                    redissonLock.unlock(String.valueOf(cmsChannelChargeDetail.getId()));
                }
            } catch (Exception e) {
                log.warn("redis解锁失败");
            }
        }
    }

    @Override
    public Page<CmsChannelChargeDetail> getPage(ChargePageVO chargePageVO) {
        List<String> corpIdList = new ArrayList<>();
        if (StringUtils.hasText(chargePageVO.getCorpName())) {
            corpIdList = channelService.getCorpIdByName(chargePageVO.getCorpName());
            if (CollectionUtils.isEmpty(corpIdList)) {
                return null;
            }
        }
        QueryWrapper<CmsChannelChargeDetail> wrapper = new QueryWrapper<>();
        wrapper.in(CollectionUtils.isNotEmpty(corpIdList), "corp_id", corpIdList)
                .eq(!StringUtils.isEmpty(chargePageVO.getStatus()), "auth_status", chargePageVO.getStatus())
                .eq(!StringUtils.isEmpty(chargePageVO.getId()), "id", chargePageVO.getId())
                .like(!StringUtils.isEmpty(chargePageVO.getAuthName()), "auth_name", chargePageVO.getAuthName());
        Page<CmsChannelChargeDetail> page = new Page<>(chargePageVO.getPageNum(), chargePageVO.getPageSize());
        Page<CmsChannelChargeDetail> cmsChannelChargeDetailPage = cmsChannelChargeDetailMapper.selectPage(page, wrapper);
        for (CmsChannelChargeDetail record : cmsChannelChargeDetailPage.getRecords()) {
            Channel channel = channelMapper.selectOne(Wrappers.lambdaQuery(Channel.class)
                    .eq(Channel::getCorpId, record.getCorpId()));
            record.setCropName(channel.getCorpName());
            record.setEbsCode(channel.getEbsCode());
            record.setCurrency(channel.getCurrencyCode());
            String invoiceAddress = "";
            if ("1".equals(record.getChargeType())) {
                ChannelIncomeInfoMonth incomeInfoMonth = statFeignClient.getInvoice(record.getAccountId()).get();
                invoiceAddress = incomeInfoMonth.getInvoicePath();
                record.setInvoiceNo(incomeInfoMonth.getInvoiceNo());
            } else {
                QueryWrapper<CmsDepositChargeRecord> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("id", record.getAccountId());
                CmsDepositChargeRecord cmsDepositChargeRecord = cmsDepositChargeRecordMapper.selectOne(queryWrapper);
                invoiceAddress = cmsDepositChargeRecord.getInvoiceAddress();
                record.setChannelType(cmsDepositChargeRecord.getChannelType());
            }
            record.setInvoiceAddress(invoiceAddress);
            record.setAmount(record.getAmount().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        }
        return cmsChannelChargeDetailPage;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional(rollbackFor = Exception.class)
    public void authPayBill(AuthPayBillVO authPayBillVO) {
        boolean lock = redissonLock.tryLock(String.valueOf(authPayBillVO.getId()));
        if (!lock) {
            throw new BizException("invoice is in progress, please try again later");
        }
        try {
            String procUniqueId = UUID.randomUUID().toString().replace("-", "");
            CmsChannelChargeDetail cmsChannelChargeDetail = cmsChannelChargeDetailMapper.selectOne(Wrappers.lambdaQuery(CmsChannelChargeDetail.class)
                    .eq(CmsChannelChargeDetail::getId, authPayBillVO.getId()));
            if (cmsChannelChargeDetail == null || !cmsChannelChargeDetail.getAuthStatus().equals("1")) {
                throw new BizException("审批状态错误，不能进行审批");
            }
            CmsDepositChargeRecord cmsDepositChargeRecord = cmsDepositChargeRecordMapper.selectOne(Wrappers.lambdaQuery(CmsDepositChargeRecord.class)
                    .eq(CmsDepositChargeRecord::getId, cmsChannelChargeDetail.getAccountId()));
            cmsChannelChargeDetail.setAuthName(authPayBillVO.getUserName());
            String corpId = cmsChannelChargeDetail.getCorpId();
            Channel channel = channelMapper.selectOne(Wrappers.lambdaQuery(Channel.class)
                    .eq(Channel::getCorpId, corpId));
            if (authPayBillVO.getStatus().equals(Boolean.FALSE)) {
                cmsChannelChargeDetail.setNoPassReason(authPayBillVO.getNoPassReason());
                cmsChannelChargeDetail.setAuthStatus("3");
                cmsChannelChargeDetail.setChargeStatus("3");

                if ("1".equals(cmsChannelChargeDetail.getChargeType())) {
                    statFeignClient.updateChargeStatus(cmsChannelChargeDetail.getAccountId(), "3").get();
                } else {
                    //修改状态为缴费审批拒绝
                    cmsDepositChargeRecord.setChargeStatus("6");
                    cmsDepositChargeRecord.setUpdateTime(new Date());
                    cmsDepositChargeRecordMapper.updateById(cmsDepositChargeRecord);
                }
            } else {
                cmsChannelChargeDetail.setAuthStatus("2");
                cmsChannelChargeDetail.setChargeStatus("2");
                cmsChannelChargeDetail.setConfirmTime(new Date());
                cmsChannelChargeDetail.setReceiptNum(authPayBillVO.getReceiptNum());
                ChannelDistributorDetail channelDistributorDetail = channelDistributorDetailMapper.selectOne(Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                        .eq(ChannelDistributorDetail::getCorpId, corpId));
                IBossRechargeReq iBossRechargeReq = new IBossRechargeReq();
                iBossRechargeReq.setNewAmount(cmsChannelChargeDetail.getAmount().longValue());
                iBossRechargeReq.setCurrencyCode(channelDistributorDetail.getCurrencyCode());
                iBossRechargeReq.setEbsCode(channel.getEbsCode());
                String channelType = "";
                ChannelTypeStrategy channelTypeStrategy;
                if ("1".equals(cmsChannelChargeDetail.getChargeType())) {
                    iBossRechargeReq.setCode("pay_" + IdUtil.simpleUUID());
                    channelTypeStrategy = depositStrategy;
                    ChannelIncomeInfoMonth channelIncomeInfoMonth = statFeignClient.getBillChannelType(cmsChannelChargeDetail.getAccountId()).get();
                    if ("01".contains(channelIncomeInfoMonth.getAccountingType())) {
                        iBossRechargeReq.setType("1");
                    } else if ("235".contains(channelIncomeInfoMonth.getAccountingType())) {
                        iBossRechargeReq.setType("6");
                    } else if ("467".contains(channelIncomeInfoMonth.getAccountingType())) {
                        iBossRechargeReq.setType("1");
                        BigDecimal sellIncome = channelIncomeInfoMonth.getDirectIncome().add(channelIncomeInfoMonth.getAccountAdjustment())
                                .add(channelIncomeInfoMonth.getSellImsiAmount().add(channelIncomeInfoMonth.getSellImsiAdjustAmount()).add(channelIncomeInfoMonth.getArrears()));
                        if (sellIncome.compareTo(BigDecimal.ZERO) != 0) {
                            iBossRechargeReq.setNewAmount(sellIncome.longValue());
                            channelTypeStrategy.proceed(iBossRechargeReq, channelDistributorDetail);
                        }
                        BigDecimal flowIncome = channelIncomeInfoMonth.getRealIncome().subtract(sellIncome);
                        iBossRechargeReq.setCode("pay_" + IdUtil.simpleUUID());
                        iBossRechargeReq.setType("6");
                        iBossRechargeReq.setNewAmount(flowIncome.longValue());
                    }
                } else {
                    iBossRechargeReq.setCode("charge_" + IdUtil.simpleUUID());
                    if ("1".equals(cmsDepositChargeRecord.getCooperationMode())) {
                        channelType = channelDistributorDetail.getChannelType();
                    } else if ("2".equals(cmsDepositChargeRecord.getCooperationMode())) {
                        channelType = channelDistributorDetail.getA2zChannelType();
                    }
                    if (!channelType.equals(cmsDepositChargeRecord.getChannelType())) {
                        throw new BizException("channel type has been changed, Please reapply for an invoice and initiate payment");
                    }
                    if ("1".equals(channelType)) {
                        //押金
                        if ("1".equals(cmsDepositChargeRecord.getCooperationMode())) {
                            //代销
                            iBossRechargeReq.setType("2");
                        } else {
                            //A2Z
                            iBossRechargeReq.setType("5");
                        }
                        channelTypeStrategy = depositStrategy;
                    } else {
                        //预存款
                        if ("1".equals(cmsDepositChargeRecord.getCooperationMode())) {
                            //代销
                            iBossRechargeReq.setType("3");
                        } else {
                            //A2Z
                            iBossRechargeReq.setType("10");
                        }
                        channelTypeStrategy = preStorageStrategy;
                    }
                }
                if (iBossRechargeReq.getNewAmount() != 0) {
                    channelTypeStrategy.proceed(iBossRechargeReq, channelDistributorDetail);
                } else {
                    log.warn("充值缴付金额为0，不用调用iboss接口，accountId:{}", authPayBillVO.getId());
                }

                if ("1".equals(cmsChannelChargeDetail.getChargeType())) {
                    statFeignClient.updateChargeStatus(cmsChannelChargeDetail.getAccountId(), "2").get();
                } else {
                    //修改状态为已缴费
                    cmsDepositChargeRecord.setChargeStatus("7");
                    cmsDepositChargeRecord.setUpdateTime(new Date());
                    cmsDepositChargeRecordMapper.updateById(cmsDepositChargeRecord);
                }
            }
            SubmitProcessReq submitProcessReq = new SubmitProcessReq();
            submitProcessReq.setType("2");
            submitProcessReq.setProcUniqueId(cmsChannelChargeDetail.getToDoItemId());
            cmsChannelChargeDetail.setToDoItemId(procUniqueId);
            log.info("ReceiptNum: {}", cmsChannelChargeDetail.getReceiptNum());
            cmsChannelChargeDetailMapper.updateById(cmsChannelChargeDetail);
            //todo 推送办结给财务
            submitProcessReq.setTodoUniqueId(authPayBillVO.getTodoUniqueId());
            if ("1".equals(cmsChannelChargeDetail.getChargeType())) {
                submitProcessReq.setProcId("4");
            } else {
                submitProcessReq.setProcId("16");
            }
            submitProcessReq.setTodoNodeId("1");
            backFeignClient.processSubmission(submitProcessReq);
            //todo 推送代办给运营
            submitProcessReq.setTitle(new SimpleDateFormat("yyyyMMdd").format(new Date()) + "完成审批");
            HashMap<String, Object> map = new HashMap<>();
            if ("1".equals(cmsChannelChargeDetail.getChargeType())) {
                submitProcessReq.setProcId(TodoProcessIdEnum.AUTH.getId());
                map.put("page", "5");
            } else {
                submitProcessReq.setProcId("17");
                map.put("page", "7");
            }
            submitProcessReq.setType("1");
            submitProcessReq.setProcUniqueId(procUniqueId);
            submitProcessReq.setTodoUniqueId(null);
            submitProcessReq.setTodoNodeId("1");
            map.put("id", cmsChannelChargeDetail.getId());
            HashMap<String, Object> titleMap = new HashMap<>();
            titleMap.put("corpName", channel.getCorpName());
            titleMap.put("date", new SimpleDateFormat("yyyyMMdd").format(new Date()));
            submitProcessReq.setTitleParamMap(titleMap);
            submitProcessReq.setParamMap(map);
            backFeignClient.processSubmission(submitProcessReq);
        } finally {
            try {
                if (redissonLock.isHeldByCurrentThread(String.valueOf(authPayBillVO.getId()))) {
                    redissonLock.unlock(String.valueOf(authPayBillVO.getId()));
                }
            } catch (Exception e) {
                log.warn("redis解锁失败");
            }
        }
    }

    @Override
    public void downLoad(long id, HttpServletResponse response) {
        CmsChannelChargeDetail cmsChannelChargeDetail = cmsChannelChargeDetailMapper.selectOne(Wrappers.lambdaQuery(CmsChannelChargeDetail.class)
                .eq(CmsChannelChargeDetail::getId, id));
        if (cmsChannelChargeDetail == null) {
            throw new BizException("数据异常");
        }
        GetFilePathUtil.downloadFile(response, cmsChannelChargeDetail.getPaymentProofAddress());
    }

    @Override
    public ResponseEntity<org.springframework.core.io.Resource> downLoad2(long id) {
        CmsChannelChargeDetail cmsChannelChargeDetail = cmsChannelChargeDetailMapper.selectOne(Wrappers.lambdaQuery(CmsChannelChargeDetail.class)
                .eq(CmsChannelChargeDetail::getId, id));
        if (cmsChannelChargeDetail == null) {
            throw new BizException("数据异常");
        }
        return getResourceResponseEntity(cmsChannelChargeDetail.getPaymentProofAddress(), null,null);
    }

    private ResponseEntity<org.springframework.core.io.Resource> getResourceResponseEntity(String fileAddress, String corpName,String invoiceNo) {
        File file = new File(fileAddress);
        if (!file.exists()) {
            throw new BizException("文件不存在，下载失败");
        }
        org.springframework.core.io.Resource resource = new FileSystemResource(file);
        String filename;
        if (StringUtils.hasText(corpName)) {
            if (StrUtil.isBlank(invoiceNo)){
                filename = new String(("Invoice_" + corpName + "_" + fileAddress.substring(fileAddress.lastIndexOf('.') - 17)).getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
            }else {
                filename = new String(("Invoice_" + corpName + "_" + invoiceNo + ".pdf").getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
            }

        } else {
            filename = new String(file.getName().getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
        }
        log.debug("原来得文件名：{}", filename);
        String cleanedFileName = filename.replaceAll("^_+|_+$", "").replaceAll(",", ""); // 仅去掉开头和结尾的下划线
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Disposition", "attachment; filename=" + cleanedFileName);
        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(file.length())
                .contentType(org.springframework.http.MediaType.APPLICATION_OCTET_STREAM)
                .body(resource);
    }

    @Override
    public ResponseEntity<org.springframework.core.io.Resource> downLoad3(String fileAddress, String corpName,String invoiceNo) {
        return getResourceResponseEntity(fileAddress, corpName,invoiceNo);
    }

    @Override
    public void chargeDownLoad(long id, HttpServletResponse response) {
        CmsChannelChargeDetail cmsChannelChargeDetail = cmsChannelChargeDetailMapper.selectOne(Wrappers.lambdaQuery(CmsChannelChargeDetail.class)
                .eq(CmsChannelChargeDetail::getAccountId, id)
                .orderByDesc(CmsChannelChargeDetail::getChargeTime)
                .orderByDesc(CmsChannelChargeDetail::getId));
        if (cmsChannelChargeDetail == null) {
            throw new BizException("数据异常");
        }
        GetFilePathUtil.downloadFile(response, cmsChannelChargeDetail.getPaymentProofAddress());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<Long,Long> rebateRecharge(List<RebateRechargeReq> rebateRechargeReqs) {

        Map<Long,Long> chargeRebate = new HashMap<>();

        if ( "2".equals(rebateRechargeReqs.get(0).getReturnType())){

            immediateRebatesDeal(rebateRechargeReqs,chargeRebate);

            return chargeRebate;
        }

        rebateRechargeReqs.forEach(rebateRechargeReq -> {
            ChannelDistributorDetail channelDistributorDetail = channelDistributorDetailMapper.selectOne(Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                    .eq(ChannelDistributorDetail::getCorpId, rebateRechargeReq.getCorpId()));

            if (ObjectUtils.isEmpty(channelDistributorDetail)){
                throw new BizException("未找到渠道商信息");
            }

            BigDecimal targetAmount = getTargetAmount(rebateRechargeReq, channelDistributorDetail);

            channelMarketingRebateMapper.insert(CmsChannelMarketingRebate.builder()
                    .corpId(rebateRechargeReq.getCorpId())
                    .type(rebateRechargeReq.getType())
                    .remainAmount(targetAmount)
                    .rebateAmount(targetAmount)
                    .activityId(rebateRechargeReq.getCampaignId())
                    .expiryTime(rebateRechargeReq.getRestitutionFundsTime())
                    .version(1L)
                    .build());


            BigDecimal totalAmount = channelMarketingRebateMapper.getTotalAmount(rebateRechargeReq.getCorpId(),rebateRechargeReq.getType());

            if ("2".equals(rebateRechargeReq.getType())){

                BigDecimal deposit = channelMarketingRebateMapper.getChannelRebateDeposit(rebateRechargeReq.getCorpId(),rebateRechargeReq.getCampaignId());

                channelMarketBillFlowMapper.insert(CmsChannelMarketBillflow.builder()
                        .corpId(rebateRechargeReq.getCorpId())
                        .type("1")
                        .currencyCode(channelDistributorDetail.getCurrencyCode())
                        .amount(targetAmount)
                        .deposit(deposit)
                        .activityId(rebateRechargeReq.getCampaignId())
                        .totalAmount(totalAmount)
                        .totalOrderId("")
                        .build());
            }else {

                channelMarketBillflowA2zMapper.insert(CmsChannelMarketBillflowA2z.builder()
                        .corpId(rebateRechargeReq.getCorpId())
                        .type("1")
                        .currencyCode(channelDistributorDetail.getCurrencyCode())
                        .amount(targetAmount)
                        .deposit(targetAmount)
                        .activityId(rebateRechargeReq.getCampaignId())
                        .totalAmount(totalAmount)
                        .usedFlow(BigDecimal.ZERO)
                        .build());
            }
        });

        return chargeRebate;

    }

    private void immediateRebatesDeal(List<RebateRechargeReq> rebateRechargeReqs, Map<Long, Long> chargeRebate) {

        Map<String, List<RebateRechargeReq>> channelRebateMap = rebateRechargeReqs.stream().collect(Collectors.groupingBy(RebateRechargeReq::getCorpId));

        channelRebateMap.forEach((key,value)->{

            BigDecimal billFlow = BigDecimal.ZERO;

            Long activityId = null;

            ChannelDistributorDetail channelDistributorDetail = channelDistributorDetailMapper.selectOne(Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                    .eq(ChannelDistributorDetail::getCorpId, key));

            if (ObjectUtils.isEmpty(channelDistributorDetail)){
                throw new BizException("未找到渠道商信息");
            }

            for (RebateRechargeReq rebateRechargeReq : value){

                if (activityId == null) {
                    activityId = rebateRechargeReq.getCampaignId();
                }

                BigDecimal targetAmount = getTargetAmount(rebateRechargeReq, channelDistributorDetail);

                if (rebateRechargeReq.getRebateId() == null){
                    if (targetAmount.compareTo(BigDecimal.ZERO) != 0){
                        CmsChannelMarketingRebate channelRebate = CmsChannelMarketingRebate.builder()
                                .corpId(rebateRechargeReq.getCorpId())
                                .type(rebateRechargeReq.getType())
                                .remainAmount(targetAmount)
                                .rebateAmount(targetAmount)
                                .activityId(rebateRechargeReq.getCampaignId())
                                .expiryTime(rebateRechargeReq.getRestitutionFundsTime())
                                .effectiveTime(rebateRechargeReq.getEffectiveTime())
                                .version(1L)
                                .build();

                        channelMarketingRebateMapper.insert(channelRebate);
                        billFlow = billFlow.add(targetAmount);
                        chargeRebate.put(rebateRechargeReq.getChargeId(),channelRebate.getId());
                    }
                }else {
                    CmsChannelMarketingRebate channelRebate = channelMarketingRebateMapper.selectById(rebateRechargeReq.getRebateId());


                    BigDecimal incrementalAmount = targetAmount.subtract(channelRebate.getRebateAmount());

                    if (targetAmount.compareTo(BigDecimal.ZERO) == 0){
                        channelMarketingRebateMapper.deleteById(rebateRechargeReq.getRebateId());
                        billFlow = billFlow.add(incrementalAmount);
                        chargeRebate.put(rebateRechargeReq.getChargeId(),null);
                    }else if (incrementalAmount.compareTo(BigDecimal.ZERO) != 0){
                        channelMarketingRebateMapper.updateById(CmsChannelMarketingRebate.builder()
                                .id(rebateRechargeReq.getRebateId())
                                .remainAmount(targetAmount)
                                .rebateAmount(targetAmount)
                                .build());
                        billFlow = billFlow.add(incrementalAmount);
                    }
                }


            };

            BigDecimal totalAmount = channelMarketingRebateMapper.getTotalAmount(key,"2");

            BigDecimal deposit = channelMarketingRebateMapper.getChannelRebateDeposit(key,activityId);

            if (billFlow.compareTo(BigDecimal.ZERO) != 0){
                channelMarketBillFlowMapper.insert(CmsChannelMarketBillflow.builder()
                        .corpId(key)
                        .type(billFlow.compareTo(BigDecimal.ZERO) > 0 ? "1" : "7")
                        .currencyCode(channelDistributorDetail.getCurrencyCode())
                        .amount(billFlow)
                        .deposit(deposit)
                        .activityId(activityId)
                        .totalAmount(totalAmount)
                        .totalOrderId("")
                        .build());
            }
        });

    }

    private BigDecimal getTargetAmount(RebateRechargeReq rebateRechargeReq, ChannelDistributorDetail channelDistributorDetail) {
        BigDecimal hkdRate = new BigDecimal(1);
        if (!"344".equals(channelDistributorDetail.getCurrencyCode())) {

            Map<String, BigDecimal> rateTable = Response.getAndCheckRemoteData(statFeignClient.getRateTable(new SimpleDateFormat("yyyyMM").format(rebateRechargeReq.getSettlementTime())));

            if (CollectionUtils.isEmpty(rateTable)){
                throw new BizException("没有找到"+ new SimpleDateFormat("yyyyMM").format(rebateRechargeReq.getSettlementTime()) + "汇率数据");
            }

            hkdRate = Optional.ofNullable(rateTable.get(channelDistributorDetail.getCurrencyCode() + "-" + "344" ))
                    .map(m -> m.setScale(6, RoundingMode.HALF_UP))
                    .orElse(new BigDecimal(0));
        }

        log.info(String.format("充值计算公式：%s / %s", rebateRechargeReq.getExpectedReturn(),hkdRate));

         return rebateRechargeReq.getExpectedReturn().divide(hkdRate,6,RoundingMode.HALF_UP);
    }

    @Override
    public List<ChannelChargeRecord> getChargeRecord(ChargeRecordDTO chargeRecordDTO) {
        return channelChargeRecordMapper.selectList(Wrappers.lambdaQuery(ChannelChargeRecord.class)
                .in(ChannelChargeRecord::getCorpId, chargeRecordDTO.getCorpId())
                .eq(ChannelChargeRecord::getChargeType,"3")
                .between(ChannelChargeRecord::getChargeTime,chargeRecordDTO.getStartTime(),chargeRecordDTO.getEndTime()));
    }

    public ChannelDistributorDetail getChannelDistributorDetailByEbsCode(String ebsCode, boolean isQuery) {
        Channel channel = channelMapper.selectOne(Wrappers.lambdaQuery(Channel.class)
                .eq(Channel::getEbsCode, ebsCode)
                .eq(Channel::getType, "1"));
        if (channel == null) {
            throw new IBossException(ApiResponseEnum.EBSCODE_ERROR.getCode());
        }
        String corpId = channel.getCorpId();
        ChannelDistributorDetail channelDistributorDetail;
        if (isQuery) {
            channelDistributorDetail = channelDistributorDetailMapper.selectOne(Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                    .eq(ChannelDistributorDetail::getCorpId, corpId));
        } else {
            channelDistributorDetail = channelDistributorDetailMapper.selectChannelDetail(corpId);
        }
        if (channelDistributorDetail == null) {
            throw new IBossException(ApiResponseEnum.EBSCODE_ERROR.getCode());
        }
        return channelDistributorDetail;
    }
}
