package com.ebupt.cmi.clientmanagement.feign.mkt;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebupt.cmi.clientmanagement.domain.dto.MktCampaignCorpDto;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.feign.mkt.domain.MarketingCampaignDTO;
import com.ebupt.cmi.clientmanagement.feign.mkt.domain.MktCampaignCorpDTO;
import com.ebupt.cmi.clientmanagement.feign.mkt.domain.SelectMarketingCampaignDTO;
import com.ebupt.cmi.clientmanagement.feign.mkt.domain.SettlementForm;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = "${feign.clients.mkt}", path = "mkt", fallbackFactory = MktFeignClient.MktFeignFallbackFactory.class)
public interface MktFeignClient {


    @PostMapping("/campaign/getMarketingCampaign")
    Response<List<MarketingCampaignDTO>> getMarketingCampaign(@RequestBody SelectMarketingCampaignDTO selectMarketingCampaignDTO);

    @PostMapping("/campaign/getMarketingCampaignCorp")
    Response<List<MktCampaignCorpDTO>> getMarketingCampaignCorp(@RequestBody SelectMarketingCampaignDTO selectMarketingCampaignDTO);
    @PostMapping("/campaign/getMarketingCampaignCorpList")
    Response<List<MktCampaignCorpDto>> getMarketingCampaignCorpList(@RequestBody SelectMarketingCampaignDTO selectMarketingCampaignDTO);
    @PostMapping("/settlement/singleSettlement")
    Response<Void> singleSettlement(@RequestBody SettlementForm form);

    @GetMapping("/campaign/getRuleDetails")
    Response<MarketingCampaignDTO> getRuleDetails(Long id);

    @Component
    @Slf4j
    class MktFeignFallbackFactory implements FallbackFactory<MktFeignClient> {

        @Override
        public MktFeignClient create(Throwable throwable) {
            log.warn("调用营销服务失败 -> " + throwable);
            return new MktFeignClient() {

                @Override
                public Response<List<MarketingCampaignDTO>> getMarketingCampaign(SelectMarketingCampaignDTO selectMarketingCampaignDTO) {
                    return Response.error();
                }

                @Override
                public Response<List<MktCampaignCorpDTO>> getMarketingCampaignCorp(SelectMarketingCampaignDTO selectMarketingCampaignDTO) {
                    return Response.error();
                }

                @Override
                public Response<List<MktCampaignCorpDto>> getMarketingCampaignCorpList(SelectMarketingCampaignDTO selectMarketingCampaignDTO) {
                    return Response.error();
                }

                @Override
                public Response<Void> singleSettlement(SettlementForm form) {
                    return Response.error();
                }

                @Override
                public Response<MarketingCampaignDTO> getRuleDetails(Long id) {
                    return Response.error();
                }
            };
        }
    }

}
