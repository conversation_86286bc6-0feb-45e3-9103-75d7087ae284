package com.ebupt.cmi.clientmanagement.domain.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RebateRechargeReq {

    private Long campaignId;

    private String corpId;


    /**
     * 充值类型：
     * 1、a2z充值
     * 2、代销充值
     */
    private String type;

    //预存款充值id
    private Long chargeId;

    //
    private Long rebateId;

    /**
     * 返利类型 1.累计返利 2.立即返利
     */
    private String returnType;

    private BigDecimal expectedReturn;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date settlementTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date restitutionFundsTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date effectiveTime;


}
