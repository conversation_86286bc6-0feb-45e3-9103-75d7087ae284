package com.ebupt.cmi.clientmanagement.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.*;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.ssh.ChannelType;
import cn.hutool.extra.ssh.JschUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ebupt.cmi.clientmanagement.config.*;
import com.ebupt.cmi.clientmanagement.controller.channel.ChannelA2zruleRelationVo;
import com.ebupt.cmi.clientmanagement.domain.dto.*;
import com.ebupt.cmi.clientmanagement.domain.dto.Context.ChannelContext;
import com.ebupt.cmi.clientmanagement.domain.dto.Context.RecordChannelFlowContext;
import com.ebupt.cmi.clientmanagement.domain.dto.channel.MktChannelPageDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.channel.SubCnlDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.channelself.MccDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.*;
import com.ebupt.cmi.clientmanagement.domain.entity.realname.ChannelRealNameInfo;
import com.ebupt.cmi.clientmanagement.domain.enums.*;
import com.ebupt.cmi.clientmanagement.domain.enums.lu.CurrencyCodeEnum;
import com.ebupt.cmi.clientmanagement.domain.req.RuleInformation;
import com.ebupt.cmi.clientmanagement.domain.response.PageResult;
import com.ebupt.cmi.clientmanagement.domain.response.RealRule;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.*;
import com.ebupt.cmi.clientmanagement.domain.vo.channel.ChannelCorpPageVO;
import com.ebupt.cmi.clientmanagement.domain.vo.channel.IsChargingForm;
import com.ebupt.cmi.clientmanagement.domain.vo.channel.SubChannelVO;
import com.ebupt.cmi.clientmanagement.domain.vo.channel.SubCnlSearchVO;
import com.ebupt.cmi.clientmanagement.domain.vo.channelself.DepositRecordVO;
import com.ebupt.cmi.clientmanagement.domain.vo.channelself.GetDepositVo;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.exoprtTask.ChannelAtzFlowExportTask;
import com.ebupt.cmi.clientmanagement.feign.back.BackFeignClient;
import com.ebupt.cmi.clientmanagement.feign.back.vo.BatchSyncfileTack;
import com.ebupt.cmi.clientmanagement.feign.back.vo.MailSendParam;
import com.ebupt.cmi.clientmanagement.feign.back.vo.User;
import com.ebupt.cmi.clientmanagement.feign.jms.JmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.jms.domain.AccountingPeriodDetail;
import com.ebupt.cmi.clientmanagement.feign.jms.domain.ChannelChargingVO;
import com.ebupt.cmi.clientmanagement.feign.mkt.MktFeignClient;
import com.ebupt.cmi.clientmanagement.feign.mkt.domain.MarketingCampaignDTO;
import com.ebupt.cmi.clientmanagement.feign.mkt.domain.MktCampaignCorpDTO;
import com.ebupt.cmi.clientmanagement.feign.mkt.domain.SelectMarketingCampaignDTO;
import com.ebupt.cmi.clientmanagement.feign.oms.OmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.oms.domain.OmsCountry;
import com.ebupt.cmi.clientmanagement.feign.pms.Package;
import com.ebupt.cmi.clientmanagement.feign.pms.PmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.rms.RmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.rms.domain.RmsAssignedImsi;
import com.ebupt.cmi.clientmanagement.feign.stat.StatFeignClient;
import com.ebupt.cmi.clientmanagement.feign.stat.vo.ChannelCloseAccounts;
import com.ebupt.cmi.clientmanagement.feign.stat.vo.ChannelIncomeExportVO;
import com.ebupt.cmi.clientmanagement.feign.stat.vo.ChannelSellsDTO;
import com.ebupt.cmi.clientmanagement.feign.stat.vo.ChannelSellsVO;
import com.ebupt.cmi.clientmanagement.mapper.*;
import com.ebupt.cmi.clientmanagement.mapper.realname.ChannelRealNameInfoMapper;
import com.ebupt.cmi.clientmanagement.service.ChannelService;
import com.ebupt.cmi.clientmanagement.service.CmsCorpFlowdetailService;
import com.ebupt.cmi.clientmanagement.service.channelself.ChannelSelfService;
import com.ebupt.cmi.clientmanagement.utils.*;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.Session;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.TextStyle;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.GZIPInputStream;

/**
 * <AUTHOR> lingsong
 * @Date 2021/4/25 10:52
 */
@Service
@Slf4j
public class ChannelServiceImpl extends ServiceImpl<ChannelMapper, Channel> implements ChannelService {

    private static final ThreadLocal<Boolean> updateChannelThreadLoacl = new ThreadLocal<>();

    private static final String IOT_FILE_A2Z = "billing_result_info_#_01.csv";

    private static final String IOT_FILE_GTP = "billing_result_info_#_02.csv";

    //# like 2023091200001
    private static final String RECORD_DETAIL_FILE = "cdr-#.cdr.gz";

    private static final String RUNOUT_OF_BALANCE = "runoutofBalance_%s";

    private static final String STOP_USE = "stopUse_%s";

    private static final String PROHIBITIVE_BUY = "prohibitiveBuy_%s";

//    private static final String PACKAGE_USE_PERCENTAGE_KEY = "update_packageUsePercentage_%s";

    private static final String RECORD_DETAIL_HEADERS = "cdr time|start time|imsi|vplmn|uplink|downlink|total usage|rat-type";

    /**
     * 是自建套餐组
     */
    private final static String IS_SELF_CREATE = "1";

    /**
     * 不是自建套餐组
     */
    private final static String NO_SELF_CREATE = "2";

    private static final String A2ZModel = "1";

    private static final String resourceModel = "2";

    @Autowired
    private IotConfig iotConfig;

    @Autowired
    private ChannelMapper channelMapper;
    @Autowired
    private ChannelContractConfig channelContractConfig;
    @Autowired
    private RecordDetailConfig recordDetailConfig;
    @Autowired
    private ChannelDistributorDetailMapper channelDistributorDetailMapper;
    @Autowired
    private ChannelPackageRelationMapper channelPackageRelationMapper;
    @Autowired
    private CmsChannelPackageRelationAuthMapper cmsChannelPackageRelationAuthMapper;
    @Autowired
    private ChannelOrderDetailMapper channelOrderDetailMapper;
    @Autowired
    private ChannelRealNameInfoMapper channelRealNameInfoMapper;
    @Autowired
    private ChannelChargeRecordMapper channelChargeRecordMapper;
    @Autowired
    private IotRecordMapper iotRecordMapper;
    @Autowired
    private IotRecordDetailMapper iotRecordDetailMapper;
    @Autowired
    private ChannelA2zOperatorMapper a2zOperatorMapper;
    @Autowired
    private BackFeignClient backFeignClient;
    @Autowired
    private PmsFeignClient pmsFeignClient;
    @Autowired
    private RmsFeignClient rmsFeignClient;
    @Autowired
    private OmsFeignClient omsFeignClient;

    @Autowired
    private MktFeignClient mktFeignClient;

    @Autowired
    private CmsResourceflowdetailMapper resourceflowdetailMapper;
    @Autowired
    private CmsChannelMarketingRebateMapper marketingRebateMapper;
    @Autowired
    private CmsRecordDetailConfigMapper recordDetailConfigMapper;
    @Autowired
    private ChannelRecordDetailMapper channelRecordDetailMapper;
    @Autowired
    private ChannelCardMapper channelCardMapper;
    @Autowired
    private TestImsiMapper testImsiMapper;
    @Autowired
    private IService<CmsChannelAuth> channelAuthService;
    @Autowired
    private IService<CmsChannelDistributorsAuth> channelDistributorsAuthIService;
    @Autowired
    private CmsTopchannelMapper topchannelMapper;
    @Autowired
    private CmsChannelRelationMapper channelRelationMapper;
    @Autowired
    private EopAccessDetailMapper eopAccessDetailMapper;

    @Autowired
    private JmsFeignClient jmsFeignClient;
    @Resource
    private CmsChannelAuthMapper channelAuthMapper;
    @Resource
    private CmsChannelDistributorsAuthMapper channelDistributorsAuthMapper;
    @Resource
    private CmsChannelA2zruleRelationMapper channelA2zruleRelationMapper;
    @Resource
    private CmsChannelSmsTemplateRelationMapper channelSmsTemplateRelationMapper;
    @Resource
    private CmsChannelImsiAmountRelationMapper channelImsiAmountRelationMapper;
    @Resource
    private CmsChannelSelfpackageCountryRelationMapper channelSelfpackageCountryRelationMapper;
    @Resource
    private CmsChannelBillflowRecordMapper channelBillflowRecordMapper;
    @Autowired
    private CmsCorpFlowdetailMapper corpFlowdetailMapper;
    @Autowired
    private CmsCorpFlowdetailService corpFlowdetailService;
    @Autowired
    private ChannelUpcctemplateRelationMapper channelUpcctemplateRelationMapper;
    @Autowired
    private ChannelUpcctemplateRelationAuthMapper channelUpcctemplateRelationAuthMapper;
    @Autowired
    private CmsChannelPackageDetailMapper channelPackageDetailMapper;
    @Autowired
    private CmsChannelMarketBillflowA2zMapper channelMarketBillflowA2zMapper;
    @Autowired
    private ChannelPackageCardMapper channelPackageCardMapper;
    @Autowired
    private ExportResourceFlowTask exportResourceFlowTask;
    @Autowired
    private CmsAssignedImsiRecordMapper assignedImsiRecordMapper;
    @Autowired
    private ChannelSurfInfoMapper channelSurfInfoMapper;
    @Autowired
    private PlatformTransactionManager transactionManager;
    @Autowired
    private CmsChannelDirectionalRelationMapper cmsChannelDirectionalRelationMapper;
    @Autowired
    private CmsChannelDirectionalRelationAuthMapper cmsChannelDirectionalRelationAuthMapper;
    @Autowired
    private CmsMarketingRateMapper marketingRateMapper;
    @Autowired
    private CmsChannelDirectionalRelationServiceImpl cmsChannelDirectionalRelationService;
    @Autowired
    private CmsChannelDirectionalRelationAuthServiceImpl cmsChannelDirectionalRelationAuthService;
    @Autowired
    private CmsChannelMarketingRebateMapper channelMarketingRebateMapper;
    @Autowired
    private ChannelCardServiceImpl channelCardService;
    @Autowired
    private RedisUtil<String> redisUtil;
    @Autowired
    private EmailUtil emailUtil;
    @Autowired
    private CmsChannelResourceruleRelationMapper channelResourceruleRelationMapper;
    @Autowired
    private StatFeignClient statFeignClient;

    @Autowired
    private CmsChannelMarketingRebateMapper cmsChannelMarketingRebateMapper;

    @Resource
    private ChannelAtzFlowExportTask channelAtzFlowExportTask;

    @Value("${channelFlowDetail-export.file_path}")
    private String channelFlowDetailPath;

    @Value("${summary-size:100000}")
    private int batchSize;

    @Resource
    private NotNeedVerifyConfig notNeedVerifyConfig;
    @Autowired
    private CmsAssignedImsiRecordMapper cmsAssignedImsiRecordMapper;
    @Autowired
    private CmsChannelSelfpackageCountryRelationAuthMapper cmsChannelSelfpackageCountryRelationAuthMapper;
    @Autowired
    private CmsChannelBlankcardOrderMapper channelBlankcardOrderMapper;
    @Autowired
    private ChannelSelfService channelSelfService;

    @Autowired
    private GetExportTaskMessage getExportTaskMessage;

    @Autowired
    private ExportAsyncTask exportAsyncTask;
    @Autowired
    private CmsIotRebateamountDetailMapper iotRebateamountDetailMapper;
    @Autowired
    private ChannelA2zOperatorMapper channelA2zOperatorMapper;
    @Autowired
    private CmsChannelCdrConfigMapper cmsChannelCdrConfigMapper;
    @Autowired
    private ExecutorService recordExecutorService;

    @Override
    public List<CmsAssignedImsiRecord> getResourceFeeImsi(List<String> imsis) {

        return cmsAssignedImsiRecordMapper.selectList(new LambdaQueryWrapper<CmsAssignedImsiRecord>()
                .select(CmsAssignedImsiRecord::getImsi, CmsAssignedImsiRecord::getCorpId)
                .in(CmsAssignedImsiRecord::getImsi, imsis)
        );
    }

    @Override
    public Channel getOne(String corpId) {
        return this.baseMapper.selectOne(new LambdaQueryWrapper<Channel>()
                .eq(Channel::getCorpId, corpId));
    }

    @Override
    public List<Channel> getList(Set<String> corpIds) {
        if (corpIds == null || corpIds.isEmpty()) {
            return list();
        }
        return this.listByIds(corpIds);
    }

    @Override
    public Page<Channel> pagelist(ChannelVO form) {
        Set<Long> groupIds = new HashSet<>();
        if (StringUtils.hasLength(form.getMcc())) {
            groupIds = pmsFeignClient.getGroupIdByMcc(form.getMcc()).get();

            if (CollectionUtils.isEmpty(groupIds)) {
                return new Page<>();
            }
        }
        Page<Channel> pagelist = channelMapper.pagelist(new Page<>(form.getCurrent(), form.getSize()), Wrappers.<Channel>query()
                .eq("c.status", "1")
                .eq("c.check_status", CheckStatus.PASS.getStatus())
                .in(StringUtils.hasLength(form.getMcc()), "d.group_id", groupIds)
                .like(StringUtils.hasLength(form.getCorpName()), "c.corp_name", form.getCorpName())
                .like(StringUtils.hasLength(form.getCompanyName()), "c.company_name", form.getCompanyName())
                .in(CollectionUtils.isNotEmpty(form.getCorpIds()), "c.corp_id", form.getCorpIds())
                .like(StringUtils.hasLength(form.getCooperationMode()), "d.channel_cooperation_mode", form.getCooperationMode()));
        pagelist.getRecords().forEach(channel -> channel.setRelationGroupNames(channelCardService.getChannelCardPoolMcc(channel.getCorpId())));
        return pagelist;
    }

    @Override
    public List<Channel> getList(ChannelVO channelVO) {
        ChannelVO vo = Optional.ofNullable(channelVO).orElse(new ChannelVO());
        if (StringUtils.hasLength(vo.getType())) {
            List<String> list = Optional.ofNullable(vo.getTypes()).orElse(new ArrayList<>());
            list.add(vo.getType());
            vo.setTypes(list);
        }
        Boolean isType = Optional.ofNullable(channelVO).map(ChannelVO::getTypes).map(m -> !m.isEmpty()).orElse(false);
        Boolean isCorp = Optional.ofNullable(channelVO).map(ChannelVO::getCorpIds).map(m -> !m.isEmpty()).orElse(false);
        LambdaQueryWrapper<Channel> wrapper = new LambdaQueryWrapper<Channel>()
                .eq(StringUtils.hasLength(vo.getCorpId()), Channel::getCorpId, vo.getCorpId())
                .in(isType, Channel::getType, vo.getTypes())
                .in(isCorp, Channel::getCorpId, vo.getCorpIds())
                .eq(StringUtils.hasLength(vo.getStatus()), Channel::getStatus, vo.getStatus())
                .eq(StringUtils.hasLength(vo.getCheckStatus()), Channel::getCheckStatus, vo.getCheckStatus());

        List<Channel> channels = this.list(wrapper);
        List<String> corpIds = channels.stream().map(Channel::getCorpId).collect(Collectors.toList());

        List<ChannelDistributorDetail> channelDistributorDetails = channelDistributorDetailMapper.selectList(Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                .select(ChannelDistributorDetail::getCorpId,
                        ChannelDistributorDetail::getChannelCooperationMode,
                        ChannelDistributorDetail::getChannelType,
                        ChannelDistributorDetail::getA2zChannelType,
                        ChannelDistributorDetail::getResourceChannelType,
                        ChannelDistributorDetail::getEmail,
                        ChannelDistributorDetail::getSalesMail)
                .in(ChannelDistributorDetail::getCorpId, corpIds));

        Map<String, String> modesMap = channelDistributorDetails
                .stream()
                .collect(Collectors.toMap(ChannelDistributorDetail::getCorpId, ChannelDistributorDetail::getChannelCooperationMode));


        Map<String, String> channelTypeMap = channelDistributorDetails
                .stream()
                .filter(detail -> detail.getChannelType() != null)
                .collect(Collectors.toMap(
                        ChannelDistributorDetail::getCorpId,
                        ChannelDistributorDetail::getChannelType,
                        (existing, replacement) -> existing
                ));

        Map<String, String> a2zChannelTypeMap = channelDistributorDetails
                .stream()
                .filter(detail -> detail.getA2zChannelType() != null)
                .collect(Collectors.toMap(
                        ChannelDistributorDetail::getCorpId,
                        ChannelDistributorDetail::getA2zChannelType,
                        (existing, replacement) -> existing
                ));

        Map<String, String> resourceChannelTypeMap = channelDistributorDetails
                .stream()
                .filter(detail -> detail.getResourceChannelType() != null)
                .collect(Collectors.toMap(
                        ChannelDistributorDetail::getCorpId,
                        ChannelDistributorDetail::getResourceChannelType,
                        (existing, replacement) -> existing
                ));

        Map<String, String> salesMap = channelDistributorDetails.stream()
                .collect(Collectors.toMap(ChannelDistributorDetail::getCorpId, ChannelDistributorDetail::getSalesMail));

        Map<String, String> emailMap = channelDistributorDetails.stream()
                .collect(Collectors.toMap(ChannelDistributorDetail::getCorpId, ChannelDistributorDetail::getEmail));

        for (Channel channel : channels) {
            channel.setModes(
                    Utils.string2List(modesMap.getOrDefault(channel.getCorpId(), "1"), StringPool.COMMA)
            );
            channel.setChannelType("1".equals(channelTypeMap.get(channel.getCorpId())) ? "Deposit" : "Prepayment");
            channel.setA2zChannelType("1".equals(a2zChannelTypeMap.get(channel.getCorpId())) ? "Deposit" : "Prepayment");
            channel.setResourceChannelType("1".equals(resourceChannelTypeMap.get(channel.getCorpId())) ? "Deposit" : "Prepayment");
            channel.setSalesMail(salesMap.getOrDefault(channel.getCorpId(), ""));
            channel.setEmail(emailMap.getOrDefault(channel.getCorpId(), ""));
        }
        return channels;
    }

    @Override
    public List<ChannelPackage> channelPackage() {
        List<ChannelPackage> channelPackages = this.baseMapper.channelPackage();
        return channelPackages;
    }

    @Override
    public Response<Channel> searchChannelInfo(String corpName) {
        QueryWrapper<Channel> wrapper = new QueryWrapper<>();
        wrapper.eq("corp_name", corpName);
        Channel channel = this.baseMapper.selectOne(wrapper);
        return Response.ok(channel);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOne(ChannelVO channelvo) {
        this.baseMapper.insert(Channel.builder().checkStatus(channelvo.getCheckStatus())
                .corpId(channelvo.getCorpId())
                .status(channelvo.getStatus())
                .corpName(channelvo.getCorpName())
                .type(channelvo.getType()).build());
    }

    @Override
    public List<ChannelCardVO> getChannelCardByImsis(List<String> imsis) {
        if (imsis.isEmpty()) {
            return new ArrayList<>();
        }
        String imsisStr = imsis.stream().collect(Collectors.joining("\",\"", "\"", "\""));
        return this.baseMapper.getChannelCardByImsis(imsisStr);
    }

    @Override
    public List<ChannelCardVO> getChannelCardBakByImsis(List<String> imsis) {
        if (imsis.isEmpty()) {
            return new ArrayList<>();
        }
        String imsisStr = imsis.stream().collect(Collectors.joining("\",\"", "\"", "\""));
        return this.baseMapper.getChannelCardByBakImsis(imsisStr);
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional(rollbackFor = Exception.class)
    public void newChannel(NewChannelVO newChannelVO) {
        try {
            UpdateChannelVO updateChannelVO = new UpdateChannelVO();
            BeanUtils.copyProperties(newChannelVO, updateChannelVO);

            allowUpdateCheck(updateChannelVO, null, true);

            String uuid = Utils.randomUUID();

            if (CollectionUtils.isNotEmpty(newChannelVO.getAppids())) {
                // 判断渠道商合作模式是否包含A2Z，同时判断是否允许自建套餐
                if (updateChannelVO.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.A2Z.getType()) && "1".equals(newChannelVO.getAllowNewPackage())) {
                    // 定向应用数据写入cms_channel_directional_relation 和 cms_channel_directional_relation_auth表
                    List<CmsChannelDirectionalRelation> cmsChannelDirectionalRelations = new ArrayList<>();
                    List<CmsChannelDirectionalRelationAuth> cmsChannelDirectionalRelationAuths = new ArrayList<>();
                    newChannelVO.getAppids().forEach(id -> {
                        cmsChannelDirectionalRelations.add(CmsChannelDirectionalRelation.builder().appId(Long.parseLong(id)).corpId(uuid).build());
                        cmsChannelDirectionalRelationAuths.add(CmsChannelDirectionalRelationAuth.builder().appId(Long.parseLong(id)).corpId(uuid).build());
                    });
                    cmsChannelDirectionalRelationService.saveBatch(cmsChannelDirectionalRelations);
                    cmsChannelDirectionalRelationAuthService.saveBatch(cmsChannelDirectionalRelationAuths);
                } else {
                    throw new BizException("合作模式不包含A2Z，不允许选择定向应用");
                }
            }

            if (!(ChannelDistributorDetail.CooperationModeEnum.RESOURCE_COOPERATION.getType()
                    .equals(updateChannelVO.getChannelCooperationMode().get(0)) && updateChannelVO.getPackageInfos().size() == 0)) {
                log.debug("渠道商不只有资源合作模式，校验套餐组");
                //对套餐组进行校验
                filteratePackageGroup(newChannelVO.getPackageInfos(), newChannelVO.getChannelCooperationMode());

                insertChannelPackageRelation(updateChannelVO, uuid, true, false);
            }

            //写channel表
            Channel channel = Channel.builder()
                    .corpId(uuid)
                    .corpName(newChannelVO.getCorpName().trim())
                    .status(CorpStatus.COMMON.getStatus())
                    .type(Channel.ChannelTypeEnum.CHANNEL.getValue())
                    .ebsCode(newChannelVO.getEbsCode())
                    .currencyCode(newChannelVO.getCurrencyCode())
                    .internalOrder(newChannelVO.getInternalOrder())
                    .companyName(newChannelVO.getCompanyName())
                    .address(newChannelVO.getAddress())
                    .checkStatus(CheckStatus.NEW_NEED_APPROVAL.getStatus()).build();
            this.baseMapper.insert(channel);
            //1、新增渠道商入库cms_channel同时入库cms_channel_auth表，表数据一致，两张表数据check_status=1
            CmsChannelAuth cmsChannelAuth = new CmsChannelAuth();
            cmsChannelAuth.setEBSCode(channel.getEbsCode());
            BeanUtils.copyProperties(channel, cmsChannelAuth);
            channelAuthMapper.insert(cmsChannelAuth);

            //需要创建账户
            if (ObjectUtil.isNotNull(newChannelVO.getCreateAccountNumber())) {
                if (newChannelVO.getCreateAccountNumber() < 0) {
                    throw new BizException("至少创建一个账户");
                }
                backFeignClient.createAccountForNewChannel(uuid, newChannelVO.getMail(), newChannelVO.getCreateAccountNumber(), newChannelVO.getCorpName());
            }

            ChannelDistributorDetail channelDistributorDetail = ChannelDistributorDetail.builder()
                    .corpId(uuid)
                    .isSub(newChannelVO.getChannelStatus())
//                    .channelCode(newChannelVO.getChannelCode())
//                    .channelUrl(newChannelVO.getChannelUrl())
                    .appKey(Utils.randomUUID())
                    .appSecret(Utils.randomUUID())
                    .depositeReset("2")
                    .deposit(BigDecimal.valueOf(0.000))
                    .depositeRemindThreshold(newChannelVO.getDepositNotify())
                    .currencyCode(newChannelVO.getCurrencyCode())
                    .discount(newChannelVO.getDiscount())
                    .contractStartTime(newChannelVO.getContractBeginTime())
                    .contractEndTime(newChannelVO.getContractEndTime())
                    .depositAmount(newChannelVO.getContractSellAmount())
//                    .directRatio(newChannelVO.getDirectEarningsRatio())
//                    .indirectType(newChannelVO.getLimitType())
//                    .indirectRatio(newChannelVO.getIndirectEarningsRatio())
//                    .indirectCount(IndirectCountEnum.BY_COUNT.getType().equals(newChannelVO.getLimitType()) ? newChannelVO.getAccountNum() : newChannelVO.getCreateTime())
                    .email(newChannelVO.getMail())
                    .accountNum(newChannelVO.getCreateAccountNumber())
                    .channelType(newChannelVO.getChannelType())
                    .activateNotification(newChannelVO.getActivateNotification())
                    .unsubscribeRule(newChannelVO.getUnsubscribeRule())
                    .totalDeposit(BigDecimal.valueOf(0.000))
                    .activateNotificationUrl(newChannelVO.getActivateNotificationUrl())
                    .allowNewPackage(newChannelVO.getAllowNewPackage())
                    .runoutofBalanceRemindThreshold(newChannelVO.getRunoutofBalanceRemindThreshold())
                    .stopUseRemindThreshold(newChannelVO.getStopUseRemindThreshold())
                    .prohibitiveBuyRemindThreshold(newChannelVO.getProhibitiveBuyRemindThreshold())
                    .channelCooperationMode(String.join(StringPool.COMMA, newChannelVO.getChannelCooperationMode()))
                    .packageUsePercentage(String.join(StringPool.COMMA, newChannelVO.getPackageUsePercentage()))
                    .overdueNotify(newChannelVO.getOverdueNotify())
                    .overdueNotifyUrl(newChannelVO.getOverdueNotifyUrl())
                    .packageUseNotifyUrl(newChannelVO.getPackageUseNotifyUrl())
                    .esimNotification(newChannelVO.getEsimNotifySwitch())
                    .esimNotificationUrl(newChannelVO.getEsimNotifyUrl())
                    .approvalPackage(newChannelVO.getApprovalPackage())
                    .a2zContractStartTime(newChannelVO.getA2zContractStartTime())
                    .a2zContractEndTime(newChannelVO.getA2zContractEndTime())
                    .salesMail(newChannelVO.getSalesMail())
                    .a2zDepositAmount(StrUtil.isBlank(newChannelVO.getA2zDepositAmount()) ? null : new BigDecimal(newChannelVO.getA2zDepositAmount()))
                    .a2zAccountingPeriodId(newChannelVO.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.A2Z.getType()) ?
                            getAccountingPeriodId(newChannelVO.getA2zAccountingPeriodId()) : null)
                    .distributionAccountingPeriodId(newChannelVO.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.CONSIGNMENT.getType()) ?
                            getAccountingPeriodId(newChannelVO.getDistributionAccountingPeriodId()) : null)
                    .a2zChannelType(newChannelVO.getA2zChannelType())
                    .isSubA2z("1")
                    .resourceAccountingPeriodId(newChannelVO.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.RESOURCE_COOPERATION.getType()) ?
                            getAccountingPeriodId(newChannelVO.getResourceAccountingPeriodId()) : null)
                    .resourceStopUseRemindThreshold(newChannelVO.getResourceStopUseRemindThreshold())
                    .resourceProhibitiveBuyRemindThreshold(newChannelVO.getResourceProhibitiveBuyRemindThreshold())
                    .resourceRunoutofBalanceRemindThreshold(newChannelVO.getResourceRunoutofBalanceRemindThreshold())
                    .resourceChannelType(newChannelVO.getResourceChannelType())
                    .marketingAmount(newChannelVO.getMarketingAmount())
                    .creditAmount(newChannelVO.getCreditAmount())
                    .a2zMarketingAmount(newChannelVO.getA2zMarketingAmount())
                    .build();
            //写运营商
            if (updateChannelVO.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.A2Z.getType())) {
                Set<String> collect = updateChannelVO.getA2zOperators().stream().map(NewChannelVO.A2zOperators::getA2zOperatorMcc).collect(Collectors.toSet());
                if (collect.size() != updateChannelVO.getA2zOperators().size()) {
                    throw new BizException("同一国家不允许创建多条规则");
                }
                for (NewChannelVO.A2zOperators a2zOperator : updateChannelVO.getA2zOperators()) {
                    channelA2zOperatorMapper.insert(CmsChannelA2zOperator.builder()
                            .corpId(uuid)
                            .a2zOperator(String.join(",", a2zOperator.getA2zOperator()))
                            .a2zOperatorMcc(a2zOperator.getA2zOperatorMcc())
                            .a2zOperatorChargeType(a2zOperator.getA2zOperatorChargeType()).build());
                }
            }

            if (ChannelDistributorDetail.AllowNewChannelPackage.YES.getValue().equals(newChannelVO.getAllowNewPackage())) {
                for (String groupid : newChannelVO.getGroupId()) {
                    channelSelfpackageCountryRelationMapper.insert(
                            CmsChannelSelfpackageCountryRelation.builder()
                                    .corpId(uuid)
                                    .groupId(Long.valueOf(groupid))
                                    .build()
                    );
                }
                channelDistributorDetail.setLimitPackageNum(newChannelVO.getLimitPacakageNum());

                List<ChannelUpcctemplateRelation> upcctemplateRelations = newChannelVO.getUpccTemplateIds()
                        .stream()
                        .map(m -> new ChannelUpcctemplateRelation()
                                .setTemplateId(m)
                                .setCorpId(uuid))
                        .collect(Collectors.toList());
                channelUpcctemplateRelationMapper.batchInsert(upcctemplateRelations);

                List<ChannelUpcctemplateRelationAuth> upcctemplateRelationAuths = newChannelVO.getUpccTemplateIds()
                        .stream()
                        .map(m -> new ChannelUpcctemplateRelationAuth()
                                .setTemplateId(m)
                                .setCorpId(uuid))
                        .collect(Collectors.toList());
                channelUpcctemplateRelationAuthMapper.batchInsert(upcctemplateRelationAuths);
            }

            channelDistributorDetailMapper.insert(channelDistributorDetail);
            if (newChannelVO.getA2zImsiAmount() != null) {
                for (String imsiAmount : newChannelVO.getA2zImsiAmount()) {
                    channelImsiAmountRelationMapper.insert(CmsChannelImsiAmountRelation.builder()
                            .ruleId(Long.parseLong(imsiAmount))
                            .corpId(uuid)
                            .cooperationMode(ChannelDistributorDetail.CooperationModeEnum.A2Z.getType())
                            .build());
                }
            }
            if (newChannelVO.getConsignmentImsiAmount() != null) {
                for (String imsiAmount : newChannelVO.getConsignmentImsiAmount()) {
                    channelImsiAmountRelationMapper.insert(CmsChannelImsiAmountRelation.builder()
                            .ruleId(Long.parseLong(imsiAmount))
                            .corpId(uuid)
                            .cooperationMode(ChannelDistributorDetail.CooperationModeEnum.CONSIGNMENT.getType())
                            .build());
                }
            }
            if (newChannelVO.getResourceImsiAmount() != null) {
                for (String imsiAmount : newChannelVO.getResourceImsiAmount()) {
                    channelImsiAmountRelationMapper.insert(CmsChannelImsiAmountRelation.builder()
                            .ruleId(Long.parseLong(imsiAmount))
                            .corpId(uuid)
                            .cooperationMode(ChannelDistributorDetail.CooperationModeEnum.RESOURCE_COOPERATION.getType())
                            .build());
                }
            }
            if (newChannelVO.getA2zRuleId() != null) {
                for (String ruleId : newChannelVO.getA2zRuleId()) {
                    channelA2zruleRelationMapper.insert(CmsChannelA2zruleRelation.builder()
                            .corpId(uuid)
                            .ruleId(Long.parseLong(ruleId))
                            .build());
                }
            }
            if (newChannelVO.getResourceRuleId() != null) {
                for (String ruleId : newChannelVO.getResourceRuleId()) {
                    channelResourceruleRelationMapper.insert(CmsChannelResourceruleRelation.builder()
                            .corpId(uuid)
                            .ruleId(Long.parseLong(ruleId))
                            .build());
                }
            }
            if (newChannelVO.getSmsTemplateVo() != null) {
                for (NewChannelVO.SmsTemplateVO vo : newChannelVO.getSmsTemplateVo()) {
                    channelSmsTemplateRelationMapper.insert(CmsChannelSmsTemplateRelation.builder()
                            .corpId(uuid)
                            .templateId(Long.parseLong(vo.getTemplateId()))
                            .templateName(vo.getTemplateName())
                            .cooperationMode(vo.getCooperationMode())
                            .build());
                }
            }

            CmsChannelDistributorsAuth cmsChannelDistributorsAuth = new CmsChannelDistributorsAuth();
            BeanUtils.copyProperties(channelDistributorDetail, cmsChannelDistributorsAuth);
            channelDistributorsAuthMapper.insert(cmsChannelDistributorsAuth);
        } catch (DuplicateKeyException e) {
            throw new BizException("已存在渠道商");
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("", e);
            throw new BizException("新增渠道商失败");
        }
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class, timeoutMills = 600000)
    @Transactional(rollbackFor = Exception.class)
    public void updateChannel(UpdateChannelVO updateChannelVO) {
        try {

            //如果产生了订单，则不允许修改酬金比例
            ChannelDistributorDetail channelDistributorDetail =
                    Optional.ofNullable(channelDistributorDetailMapper.selectOne(Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                                    .eq(ChannelDistributorDetail::getCorpId, updateChannelVO.getCorpId())))
                            .orElseThrow(() -> new BizException("没有该渠道商"));

            //修改之前杂七杂八的检查
            allowUpdateCheck(updateChannelVO, channelDistributorDetail, false);
            boolean appIdChangeFlag = false;

            // 合作模式包含A2Z
            if (updateChannelVO.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.A2Z.getType())) {
                // 应用组处理
                appIdChangeFlag = appIdsCollectionChangesCheck(updateChannelVO.getCorpId(), updateChannelVO.getAppids());
            } else if (CollectionUtils.isNotEmpty(updateChannelVO.getAppids())) {
                throw new BizException("合作模式不包含A2Z，不允许选择定向应用");
            }
            if (!(ChannelDistributorDetail.CooperationModeEnum.RESOURCE_COOPERATION.getType()
                    .equals(updateChannelVO.getChannelCooperationMode().get(0)) && updateChannelVO.getPackageInfos().size() == 0)) {
                log.debug("渠道商不只有资源合作模式，校验套餐组");

                //对套餐组进行校验
                filteratePackageGroup(updateChannelVO.getPackageInfos(), updateChannelVO.getChannelCooperationMode());

                insertChannelPackageRelation(updateChannelVO, updateChannelVO.getCorpId(), false, false);
            }

            CmsChannelAuth cmsChannelAuth = channelAuthMapper.selectOne(Wrappers.lambdaQuery(CmsChannelAuth.class).eq(CmsChannelAuth::getCorpId, updateChannelVO.getCorpId()));
            boolean isNewChannel = cmsChannelAuth != null && CheckStatus.NEW_NEED_APPROVAL.getStatus().equals(cmsChannelAuth.getCheckStatus());
            boolean isUpdateNotpassChannel = false;
            boolean writeInternalOrderFlag = false;
            boolean deleteInternalOrderFlag = false;
            if (!isNewChannel) {
                Channel channel = Optional.ofNullable(channelMapper.selectById(updateChannelVO.getCorpId())).orElseThrow(() -> new BizException("渠道商不存在"));
                isUpdateNotpassChannel = CheckStatus.NOT_PASS.getStatus().equals(channel.getCheckStatus());
                if (isUpdateNotpassChannel) {
                    isNewChannel = true;
                } else {
                    //是->否
                    writeInternalOrderFlag = channel.getInternalOrder().equals("0") && updateChannelVO.getInternalOrder().equals("1");
                    //否->是
                    deleteInternalOrderFlag = channel.getInternalOrder().equals("1") && updateChannelVO.getInternalOrder().equals("0");
                }
            }


            Map<String, List<CmsChannelImsiAmountRelation>> map = channelImsiAmountRelationMapper.selectList(Wrappers.lambdaQuery(CmsChannelImsiAmountRelation.class)
                            .eq(CmsChannelImsiAmountRelation::getCorpId, updateChannelVO.getCorpId()))
                    .stream()
                    .collect(Collectors.groupingBy(CmsChannelImsiAmountRelation::getCooperationMode));

            List<CmsChannelImsiAmountRelation> l = map.get("2");
            if (l != null) {
                List<String> a2zlist = l.stream().map(CmsChannelImsiAmountRelation::getRuleId).map(e -> e.toString()).collect(Collectors.toList());
                List<String> diff = getDiff(a2zlist, updateChannelVO.getA2zImsiAmount());
                if (!diff.isEmpty()) {
                    Integer i = channelBlankcardOrderMapper.selectCount(Wrappers.lambdaQuery(CmsChannelBlankcardOrder.class)
                            .in(CmsChannelBlankcardOrder::getFreeimsiId, diff)
                            .eq(CmsChannelBlankcardOrder::getCooperationMode, "2")
                            .eq(CmsChannelBlankcardOrder::getOrderUserId, updateChannelVO.getCorpId()));
                    if (i > 0) {
                        throw new BizException("不允许删除已绑定a2z imsi费规则，id：" + diff);
                    }

                    Integer j = channelCardMapper.selectCount(Wrappers.lambdaQuery(ChannelCard.class)
                            .in(ChannelCard::getFreeimsiId, diff)
                            .eq(ChannelCard::getCooperationMode, "2")
                            .eq(ChannelCard::getCorpId, updateChannelVO.getCorpId()));
                    if (j > 0) {
                        throw new BizException("不允许删除已绑定a2z imsi费规则，id：" + diff);
                    }
                }
            }
            channelImsiAmountRelationMapper.delete(Wrappers.lambdaQuery(CmsChannelImsiAmountRelation.class)
                    .eq(CmsChannelImsiAmountRelation::getCorpId, updateChannelVO.getCorpId()));
            if (updateChannelVO.getA2zImsiAmount() != null) {
                for (String imsiAmount : updateChannelVO.getA2zImsiAmount()) {
                    channelImsiAmountRelationMapper.insert(CmsChannelImsiAmountRelation.builder()
                            .ruleId(Long.parseLong(imsiAmount))
                            .corpId(updateChannelVO.getCorpId())
                            .cooperationMode(ChannelDistributorDetail.CooperationModeEnum.A2Z.getType())
                            .build());
                }
            }

            List<CmsChannelImsiAmountRelation> l1 = map.get("1");
            if (l1 != null) {
                List<String> consignmentlist = map.get("1").stream().map(CmsChannelImsiAmountRelation::getRuleId).map(e -> e.toString()).collect(Collectors.toList());
                List<String> diff1 = getDiff(consignmentlist, updateChannelVO.getConsignmentImsiAmount());
                if (!diff1.isEmpty()) {
                    Integer i = channelBlankcardOrderMapper.selectCount(Wrappers.lambdaQuery(CmsChannelBlankcardOrder.class)
                            .in(CmsChannelBlankcardOrder::getFreeimsiId, diff1)
                            .eq(CmsChannelBlankcardOrder::getCooperationMode, "1")
                            .eq(CmsChannelBlankcardOrder::getOrderUserId, updateChannelVO.getCorpId()));
                    if (i > 0) {
                        throw new BizException("不允许删除已绑定代销imsi费规则，id：" + diff1);
                    }

                    Integer j = channelCardMapper.selectCount(Wrappers.lambdaQuery(ChannelCard.class)
                            .in(ChannelCard::getFreeimsiId, diff1)
                            .eq(ChannelCard::getCooperationMode, "1")
                            .eq(ChannelCard::getCorpId, updateChannelVO.getCorpId()));
                    if (j > 0) {
                        throw new BizException("不允许删除已绑定代销imsi费规则，id：" + diff1);
                    }
                }
            }

            if (updateChannelVO.getConsignmentImsiAmount() != null) {
                for (String imsiAmount : updateChannelVO.getConsignmentImsiAmount()) {
                    channelImsiAmountRelationMapper.insert(CmsChannelImsiAmountRelation.builder()
                            .ruleId(Long.parseLong(imsiAmount))
                            .corpId(updateChannelVO.getCorpId())
                            .cooperationMode(ChannelDistributorDetail.CooperationModeEnum.CONSIGNMENT.getType())
                            .build());
                }
            }


            List<CmsChannelImsiAmountRelation> l2 = map.get("3");
            if (l2 != null) {
                List<String> resourcelist = map.get("3").stream().map(CmsChannelImsiAmountRelation::getRuleId).map(e -> e.toString()).collect(Collectors.toList());
                List<String> diff2 = getDiff(resourcelist, updateChannelVO.getResourceImsiAmount());
                if (!diff2.isEmpty()) {
                    if (rmsFeignClient.getUsedCount(diff2, "1", updateChannelVO.getCorpId()).get() > 0) {
                        throw new BizException("不允许删除已绑定资源合作imsi费规则，id：" + diff2);
                    }
                }
            }

            if (updateChannelVO.getResourceImsiAmount() != null) {
                for (String imsiAmount : updateChannelVO.getResourceImsiAmount()) {
                    channelImsiAmountRelationMapper.insert(CmsChannelImsiAmountRelation.builder()
                            .ruleId(Long.parseLong(imsiAmount))
                            .corpId(updateChannelVO.getCorpId())
                            .cooperationMode(ChannelDistributorDetail.CooperationModeEnum.RESOURCE_COOPERATION.getType())
                            .build());
                }
            }

            List<CmsChannelA2zruleRelation> cmsChannelA2zruleRelations = channelA2zruleRelationMapper.selectList(Wrappers.lambdaQuery(CmsChannelA2zruleRelation.class)
                    .eq(CmsChannelA2zruleRelation::getCorpId, updateChannelVO.getCorpId()));
            if (!cmsChannelA2zruleRelations.isEmpty()) {
                List<String> list = cmsChannelA2zruleRelations.stream().map(CmsChannelA2zruleRelation::getRuleId).map(e -> e.toString()).collect(Collectors.toList());
                List<String> diff = getDiff(list, updateChannelVO.getA2zRuleId());
                if (!diff.isEmpty()) {
                    Integer i = channelBlankcardOrderMapper.selectCount(Wrappers.lambdaQuery(CmsChannelBlankcardOrder.class)
                            .in(CmsChannelBlankcardOrder::getRuleId, diff)
                            .eq(CmsChannelBlankcardOrder::getCooperationMode, "2")
                            .eq(CmsChannelBlankcardOrder::getOrderUserId, updateChannelVO.getCorpId()));
                    if (i > 0) {
                        throw new BizException("不允许删除已绑定a2z规则，id：" + diff);
                    }

                    Integer j = channelCardMapper.selectCount(Wrappers.lambdaQuery(ChannelCard.class)
                            .in(ChannelCard::getRuleId, diff)
                            .eq(ChannelCard::getCooperationMode, "2")
                            .eq(ChannelCard::getCorpId, updateChannelVO.getCorpId()));
                    if (j > 0) {
                        throw new BizException("不允许删除已绑定a2z规则，id：" + diff);
                    }
                }
            }
            channelA2zruleRelationMapper.delete(Wrappers.lambdaQuery(CmsChannelA2zruleRelation.class)
                    .eq(CmsChannelA2zruleRelation::getCorpId, updateChannelVO.getCorpId()));
            if (updateChannelVO.getA2zRuleId() != null) {
                for (String ruleId : updateChannelVO.getA2zRuleId()) {
                    channelA2zruleRelationMapper.insert(CmsChannelA2zruleRelation.builder()
                            .corpId(updateChannelVO.getCorpId())
                            .ruleId(Long.parseLong(ruleId))
                            .build());
                }
            }

            List<CmsChannelResourceruleRelation> cmsChannelResourceruleRelations = channelResourceruleRelationMapper.selectList(Wrappers.lambdaQuery(CmsChannelResourceruleRelation.class)
                    .eq(CmsChannelResourceruleRelation::getCorpId, updateChannelVO.getCorpId()));
            if (!cmsChannelResourceruleRelations.isEmpty()) {
                List<String> list = cmsChannelResourceruleRelations.stream().map(CmsChannelResourceruleRelation::getRuleId).map(e -> e.toString()).collect(Collectors.toList());
                List<String> diff = getDiff(list, updateChannelVO.getResourceRuleId());
                if (!diff.isEmpty()) {
                    if (rmsFeignClient.getUsedCount(diff, "2", updateChannelVO.getCorpId()).get() > 0) {
                        throw new BizException("不允许删除已绑定资源合作规则，id：" + diff);
                    }
                }
            }

            channelResourceruleRelationMapper.delete(Wrappers.lambdaQuery(CmsChannelResourceruleRelation.class)
                    .eq(CmsChannelResourceruleRelation::getCorpId, updateChannelVO.getCorpId()));
            if (updateChannelVO.getResourceRuleId() != null) {
                for (String ruleId : updateChannelVO.getResourceRuleId()) {
                    channelResourceruleRelationMapper.insert(CmsChannelResourceruleRelation.builder()
                            .corpId(updateChannelVO.getCorpId())
                            .ruleId(Long.parseLong(ruleId))
                            .build());
                }
            }
            channelSmsTemplateRelationMapper.delete(Wrappers.lambdaQuery(CmsChannelSmsTemplateRelation.class)
                    .eq(CmsChannelSmsTemplateRelation::getCorpId, updateChannelVO.getCorpId()));
            if (updateChannelVO.getSmsTemplateVo() != null) {
                for (NewChannelVO.SmsTemplateVO vo : updateChannelVO.getSmsTemplateVo()) {
                    channelSmsTemplateRelationMapper.insert(CmsChannelSmsTemplateRelation.builder()
                            .corpId(updateChannelVO.getCorpId())
                            .templateId(Long.parseLong(vo.getTemplateId()))
                            .templateName(vo.getTemplateName())
                            .cooperationMode(vo.getCooperationMode())
                            .build());
                }
            }
            if (ChannelDistributorDetail.AllowNewChannelPackage.YES.getValue().equals(updateChannelVO.getAllowNewPackage())) {
                cmsChannelSelfpackageCountryRelationAuthMapper.delete(Wrappers.lambdaQuery(CmsChannelSelfpackageCountryRelationAuth.class)
                        .eq(CmsChannelSelfpackageCountryRelationAuth::getCorpId, updateChannelVO.getCorpId()));
                for (String groupid : updateChannelVO.getGroupId()) {
                    cmsChannelSelfpackageCountryRelationAuthMapper.insert(
                            CmsChannelSelfpackageCountryRelationAuth.builder()
                                    .corpId(updateChannelVO.getCorpId())
                                    .groupId(Long.valueOf(groupid))
                                    .build()
                    );
                }
            }

            //写运营商
            if (updateChannelVO.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.A2Z.getType())) {
                Set<String> collect = updateChannelVO.getA2zOperators().stream().map(NewChannelVO.A2zOperators::getA2zOperatorMcc).collect(Collectors.toSet());
                if (collect.size() != updateChannelVO.getA2zOperators().size()) {
                    throw new BizException("同一国家不允许创建多条规则");
                }
                channelA2zOperatorMapper.delete(Wrappers.lambdaQuery(CmsChannelA2zOperator.class).eq(CmsChannelA2zOperator::getCorpId, updateChannelVO.getCorpId()));
                for (NewChannelVO.A2zOperators a2zOperator : updateChannelVO.getA2zOperators()) {
                    channelA2zOperatorMapper.insert(CmsChannelA2zOperator.builder()
                            .corpId(updateChannelVO.getCorpId())
                            .a2zOperator(String.join(",", a2zOperator.getA2zOperator()))
                            .a2zOperatorMcc(a2zOperator.getA2zOperatorMcc())
                            .a2zOperatorChargeType(a2zOperator.getA2zOperatorChargeType()).build());
                }
            }

            // 修改审核字段 || 更新不通过的渠道商 || 更新新建待审核的渠道商走更新审核流程
            if (isUpdate(updateChannelVO, channelDistributorDetail, isUpdateNotpassChannel, appIdChangeFlag)) {
                updateChannelVO.setChannelDistributorDetail(channelDistributorDetail);
                writeAuthTable(updateChannelVO, isNewChannel);
                if (deleteInternalOrderFlag) {
                    redisUtil.del(String.format(BizConstants.INTERNAL_ORDER_FLAG, updateChannelVO.getCorpId()));
                }
                if (writeInternalOrderFlag) {
                    redisUtil.set(String.format(BizConstants.INTERNAL_ORDER_FLAG, updateChannelVO.getCorpId()), new Date().toString());
                }
                return;
            }

            //写channel表
            log.debug("没有需要审核的字段，直接更新");
            this.baseMapper.updateById(buildChannel(updateChannelVO));
            channelDistributorDetailMapper.update(buildChannelDistributorDetail(updateChannelVO),
                    Wrappers.lambdaUpdate(ChannelDistributorDetail.class)
                            .eq(ChannelDistributorDetail::getCorpId, updateChannelVO.getCorpId())
                            .set(ChannelDistributorDetail::getChannelType, updateChannelVO.getChannelType())
                            .set(ChannelDistributorDetail::getDepositeRemindThreshold, updateChannelVO.getDepositNotify())
                            .set(ChannelDistributorDetail::getA2zContractEndTime, updateChannelVO.getA2zContractEndTime())
                            .set(ChannelDistributorDetail::getA2zContractStartTime, updateChannelVO.getA2zContractStartTime())
                            .set(ChannelDistributorDetail::getSalesMail, updateChannelVO.getSalesMail())
                            .set(StrUtil.isNotBlank(updateChannelVO.getA2zDepositAmount()), ChannelDistributorDetail::getA2zDepositAmount, updateChannelVO.getA2zDepositAmount())
                            .set(updateChannelVO.getContractEndTime() != null &&
                                            !updateChannelVO.getContractEndTime().equals(channelDistributorDetail.getContractEndTime()),
                                    ChannelDistributorDetail::getContractConsignmentNotify, "1")
                            .set(updateChannelVO.getA2zContractEndTime() != null &&
                                            !updateChannelVO.getA2zContractEndTime().equals(channelDistributorDetail.getA2zContractEndTime()),
                                    ChannelDistributorDetail::getA2zConsignmentNotify, "1"));

            //存在审核数据则更新数据
            if (cmsChannelAuth != null) {
                updateChannelVO.setChannelDistributorDetail(channelDistributorDetail);
                writeAuthTable(updateChannelVO, isNewChannel);
            }

            Date a2zContractEndTime = channelDistributorDetail.getA2zContractEndTime();
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE, -channelContractConfig.getA2zAfterExpire());

            if (a2zContractEndTime != null && updateChannelVO.getA2zContractEndTime() == null) {
                unfreezeChannelAccount(updateChannelVO.getCorpId(), false);
                channelDistributorDetailMapper.update(null,
                        Wrappers.lambdaUpdate(ChannelDistributorDetail.class)
                                .eq(ChannelDistributorDetail::getCorpId, updateChannelVO.getCorpId())
                                .set(ChannelDistributorDetail::getIsSubA2z, "1"));
            } else if (a2zContractEndTime != null && new Date(calendar.getTime().getTime()).before(updateChannelVO.getA2zContractEndTime())) {
                log.debug("a2z当前：修改={}:{}", a2zContractEndTime, updateChannelVO.getA2zContractEndTime());
                if (!(a2zContractEndTime.compareTo(updateChannelVO.getA2zContractEndTime()) == 0)) {
                    unfreezeChannelAccount(updateChannelVO.getCorpId(), false);
                    channelDistributorDetailMapper.update(null,
                            Wrappers.lambdaUpdate(ChannelDistributorDetail.class)
                                    .eq(ChannelDistributorDetail::getCorpId, updateChannelVO.getCorpId())
                                    .set(ChannelDistributorDetail::getIsSubA2z, "1"));
                }
            } else if (a2zContractEndTime == null && updateChannelVO.getA2zContractEndTime() != null && new Date(calendar.getTime().getTime()).before(updateChannelVO.getA2zContractEndTime())) {
                unfreezeChannelAccount(updateChannelVO.getCorpId(), false);
                channelDistributorDetailMapper.update(null,
                        Wrappers.lambdaUpdate(ChannelDistributorDetail.class)
                                .eq(ChannelDistributorDetail::getCorpId, updateChannelVO.getCorpId())
                                .set(ChannelDistributorDetail::getIsSubA2z, "1"));
            }


            Calendar calendar1 = Calendar.getInstance();
            calendar1.add(Calendar.DATE, -channelContractConfig.getAfterExpire());
            if (channelDistributorDetail.getContractEndTime() != null && updateChannelVO.getContractEndTime() == null) {
                unfreezeChannelAccount(updateChannelVO.getCorpId(), true);
                channelDistributorDetailMapper.update(null,
                        Wrappers.lambdaUpdate(ChannelDistributorDetail.class)
                                .eq(ChannelDistributorDetail::getCorpId, updateChannelVO.getCorpId())
                                .set(ChannelDistributorDetail::getIsSub, "1"));
            } else if (channelDistributorDetail.getContractEndTime() != null && new Date(calendar1.getTime().getTime()).before(updateChannelVO.getContractEndTime())) {
                log.debug("代销当前：修改={}:{}", channelDistributorDetail.getContractEndTime(), updateChannelVO.getContractEndTime());
                if (!(channelDistributorDetail.getContractEndTime().compareTo(updateChannelVO.getContractEndTime()) == 0)) {
                    unfreezeChannelAccount(updateChannelVO.getCorpId(), true);
                    channelDistributorDetailMapper.update(null,
                            Wrappers.lambdaUpdate(ChannelDistributorDetail.class)
                                    .eq(ChannelDistributorDetail::getCorpId, updateChannelVO.getCorpId())
                                    .set(ChannelDistributorDetail::getIsSub, "1"));
                }
            } else if (channelDistributorDetail.getContractEndTime() == null && updateChannelVO.getContractEndTime() != null && new Date(calendar1.getTime().getTime()).before(updateChannelVO.getContractEndTime())) {
                unfreezeChannelAccount(updateChannelVO.getCorpId(), true);
                channelDistributorDetailMapper.update(null,
                        Wrappers.lambdaUpdate(ChannelDistributorDetail.class)
                                .eq(ChannelDistributorDetail::getCorpId, updateChannelVO.getCorpId())
                                .set(ChannelDistributorDetail::getIsSub, "1"));
            }
            if (deleteInternalOrderFlag) {
                redisUtil.del(String.format(BizConstants.INTERNAL_ORDER_FLAG, updateChannelVO.getCorpId()));
            }
            if (writeInternalOrderFlag) {
                redisUtil.set(String.format(BizConstants.INTERNAL_ORDER_FLAG, updateChannelVO.getCorpId()), new Date().toString());
            }
        } catch (DuplicateKeyException e) {
            throw new BizException("已存在渠道商");
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("", e);
//            final String flag = String.format(PACKAGE_USE_PERCENTAGE_KEY, updateChannelVO.getCorpId());
//            if (redisUtil.hasKey(flag)) {
//                redisUtil.del(flag);
//            }
            channelDistributorDetailMapper.update(null, new LambdaUpdateWrapper<ChannelDistributorDetail>()
                    .eq(ChannelDistributorDetail::getCorpId, updateChannelVO.getCorpId())
                    .set(ChannelDistributorDetail::getWhetherUpdateThreshold, null));
            throw new BizException("更新渠道商失败");
        } finally {
            updateChannelThreadLoacl.remove();
        }
    }

    private Long getAccountingPeriodId(String accountingPeriodId) {
        if (StringUtils.isEmpty(accountingPeriodId)) {
            AccountingPeriodDetail accountingPeriodDetail = jmsFeignClient.getDefaultAccountingPeriod().get();
            if (accountingPeriodDetail == null) {
                throw new BizException("没有可用的默认账期");
            }
            return accountingPeriodDetail.getAccountingPeriodId();
        }

        return Long.parseLong(accountingPeriodId);
    }

    public void unfreezeChannelAccount(String corpId, boolean unfreezeAll) {
        List<String> unfreezeChannelList = new ArrayList<>();
        if (unfreezeAll) {
            unfreezeChannelList.addAll(
                    getLowerChannel(corpId, true)
                            .stream()
                            .map(Channel::getCorpId)
                            .collect(Collectors.toList())
            );
        } else {
            unfreezeChannelList.add(corpId);
        }

        backFeignClient.unfreezeAccount(unfreezeChannelList);
    }

    public void freezeChannelAccount(String corpId, String type) {
        List<String> freezeChannelList = new ArrayList<>();
        if ("1".equals(type)) {
            freezeChannelList.addAll(
                    getLowerChannel(corpId, true)
                            .stream()
                            .map(Channel::getCorpId)
                            .collect(Collectors.toList())
            );
        } else if ("2".equals(type)) {
            freezeChannelList.add(corpId);
        } else {
            List<String> list = getLowerChannel(corpId, false)
                    .stream()
                    .map(Channel::getCorpId)
                    .collect(Collectors.toList());
            if (list.isEmpty()) {
                return;
            }

            freezeChannelList.addAll(list);
        }

        backFeignClient.freezeAccount(freezeChannelList);
    }

    /**
     * 应用集合改动校验
     *
     * @param corpId
     * @param appids
     * @return
     */
    public boolean appIdsCollectionChangesCheck(String corpId, List<String> appids) {
        // 应用组id校验
        List<String> originRgList = cmsChannelDirectionalRelationMapper.selectList(Wrappers.lambdaQuery(CmsChannelDirectionalRelation.class)
                        .select(CmsChannelDirectionalRelation::getAppId)
                        .eq(CmsChannelDirectionalRelation::getCorpId, corpId))
                .stream().map(relation -> String.valueOf(relation.getAppId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(originRgList) && CollectionUtils.isEmpty(appids)) {
            log.debug("渠道商修改、原始应用组不变且都为空");
            return false;
        }
        if (CollectionUtils.isEmpty(originRgList)) {
            log.debug("渠道商原始应用组为空");
            return true;
        }
        // 有变动
        if (!compareLists(originRgList, appids)) {
            List<String> needCheckList = new ArrayList<>();
            if (CollectionUtils.isEmpty(appids)) {
                needCheckList.addAll(originRgList);
            } else {
                originRgList.forEach(origin -> {
                    if (!appids.contains(origin)) {
                        needCheckList.add(origin);
                    }
                });
            }
            if (CollectionUtils.isNotEmpty(needCheckList)) {
                List<String> appNameList = Response.getAndCheckRemoteData(pmsFeignClient.appIdsIsBind(needCheckList, corpId));
                if (CollectionUtils.isNotEmpty(appNameList)) {
                    throw new BizException(appNameList.get(0) + "已被自建套餐使用，不允许删除");
                }
            }
//            // 删除基础表数据
//            cmsChannelDirectionalRelationMapper.delete(Wrappers.lambdaUpdate(CmsChannelDirectionalRelation.class).eq(CmsChannelDirectionalRelation::getCorpId, corpId));
//            // 新增基础表数据
//            List<CmsChannelDirectionalRelation> cmsChannelDirectionalRelations = new ArrayList<>();
//            appids.forEach(id -> {
//                cmsChannelDirectionalRelations.add(CmsChannelDirectionalRelation.builder().appId(Long.parseLong(id)).corpId(corpId).build());
//            });
//            cmsChannelDirectionalRelationService.saveBatch(cmsChannelDirectionalRelations);
            return true;
        }
        return false;
    }

    /**
     * 集合比较
     *
     * @param list1
     * @param list2
     * @param <T>
     * @return
     */
    private static <T> boolean compareLists(List<T> list1, List<T> list2) {
        if (list1 == null || list2 == null) {
            return false;
        } else if (list1.size() != list2.size()) {
            return false;
        } else {
            for (int i = 0; i < list1.size(); i++) {
                T element1 = list1.get(i);
                T element2 = list2.get(i);

                if (!element1.equals(element2)) {
                    return false;
                }
            }
            return true;
        }
    }

    private void filteratePackageGroup(List<NewChannelVO.PackageInfo> packageInfos, List<String> channelCooperationMode) {

        List<String> groupIds = packageInfos.stream().map(NewChannelVO.PackageInfo::getGroupId).collect(Collectors.toList());

        List<String> targetGroupIds = Response.getAndCheckRemoteData(pmsFeignClient.getPackageGroupsByGroupIds(groupIds, channelCooperationMode));

        List<String> errorNames = new ArrayList<>();

        if (CollectionUtils.isEmpty(targetGroupIds)) {
            throw new BizException("请选择跟渠道商合作模式相同的套餐组");
        }
        Iterator<NewChannelVO.PackageInfo> iterator = packageInfos.iterator();
        while (iterator.hasNext()) {
            NewChannelVO.PackageInfo info = iterator.next();
            if (!targetGroupIds.contains(info.getGroupId())) {
                errorNames.add(info.getGroupName());
            }
        }
        if (CollectionUtils.isNotEmpty(errorNames)) {
            throw new BizException("不符合当前合作模式的套餐组：" + errorNames.stream().collect(Collectors.joining(",")));
        }

    }

    private void insertChannelPackageRelation(UpdateChannelVO vo, String corpId, boolean isNewChannel, boolean isSelfCreate) {
        //套餐组不为空
        if (ObjectUtil.isNotNull(vo.getPackageInfos())) {
            List<ChannelPackageRelation> channelPackageRelationList = new ArrayList<>();
            List<CmsChannelPackageRelationAuth> channelPackageRelationListAuth = new ArrayList<>();
            List<String> groupIdList = new ArrayList<>();
            for (NewChannelVO.PackageInfo packageInfo : vo.getPackageInfos()) {
                groupIdList.add(packageInfo.getGroupId());
                final CmsChannelPackageRelationAuth auth = CmsChannelPackageRelationAuth.builder()
                        .corpId(corpId)
                        .groupId(packageInfo.getGroupId())
                        .groupName(packageInfo.getGroupName())
                        .isChannelCreate(isSelfCreate ? IS_SELF_CREATE : NO_SELF_CREATE)
                        .cooperationMode(packageInfo.getChannelCooperationMode()).build();
                ChannelPackageRelation entity = new ChannelPackageRelation();
                BeanUtils.copyProperties(auth, entity);

                channelPackageRelationListAuth.add(auth);
                channelPackageRelationList.add(entity);
            }


            //判断套餐组是否存在重复套餐
            Boolean haveSamePackageInSameGroup = pmsFeignClient.haveSamePackageInSameGroup(groupIdList).get();
            if (haveSamePackageInSameGroup) {
                throw new BizException("存在相同套餐");
            }

            if (isNewChannel) {
                channelPackageRelationMapper.inserBatch(channelPackageRelationList);
                if (isSelfCreate) {
                    return;
                }
            } else {
                cmsChannelPackageRelationAuthMapper.delete(Wrappers.lambdaQuery(CmsChannelPackageRelationAuth.class)
                        .eq(CmsChannelPackageRelationAuth::getCorpId, vo.getCorpId()));

                final Set<String> set = channelPackageRelationMapper.selectList(Wrappers.lambdaQuery(ChannelPackageRelation.class)
                                .eq(ChannelPackageRelation::getCorpId, vo.getCorpId())
                                .eq(ChannelPackageRelation::getIsChannelCreate, NO_SELF_CREATE))
                        .stream().map(ChannelPackageRelation::getGroupId).collect(Collectors.toSet());
                if (set.size() > 0) {
                    final Set<String> setAuth = channelPackageRelationListAuth.stream().map(CmsChannelPackageRelationAuth::getGroupId).collect(Collectors.toSet());
                    if (set.size() == setAuth.size() && set.containsAll(setAuth)) {
                        updateChannelThreadLoacl.set(false);
                    } else {
                        updateChannelThreadLoacl.set(true);
                    }
                }
            }

            cmsChannelPackageRelationAuthMapper.inserBatch(channelPackageRelationListAuth);
        }
    }

    private void allowUpdateCheck(UpdateChannelVO vo, ChannelDistributorDetail channelDistributorDetail, boolean isNewChannel) {
        //若激活通知开关为开状态，新增渠道商URL必填判断
        if ("1".equals(vo.getActivateNotification()) &&
                StringUtils.isEmpty(vo.getActivateNotificationUrl())) {
            throw new BizException("激活通知URL不能为空");
        }

        // 若esim通知开关为开状态，新增esim通知url必填判断
        if ("1".equals(vo.getEsimNotifySwitch()) &&
                StringUtils.isEmpty(vo.getEsimNotifyUrl())) {
            throw new BizException("esim通知URL不能为空");
        }

        if (vo.getCorpName().contains("&") || vo.getCompanyName().contains("&")) {
            throw new BizException("&号不能包含在渠道商名称或公司名称中");
        }

        List<Channel> channels = this.baseMapper.selectList(Wrappers.lambdaQuery(Channel.class)
                .select(Channel::getCorpId)
                .eq(Channel::getCorpName, vo.getCorpName())
                .or()
                .eq(Channel::getEbsCode, vo.getEbsCode()));
        if (isNewChannel) {
            if (channels.size() > 0) {
                throw new BizException("EBSCode或渠道商名称不能重复");
            }
        } else {
            if (
                    channels.size() > 1 ||
                            (channels.size() == 1 && !channels.get(0).getCorpId().equals(vo.getCorpId()))
            ) {
                throw new BizException("EBSCode或渠道商名称不能重复");
            }

//            if (updateChannelBlankCheck(channelDistributorDetail.getDirectRatio(), vo.getDirectEarningsRatio())
//                    || updateChannelBlankCheck(channelDistributorDetail.getIndirectRatio(), vo.getIndirectEarningsRatio())) {
//                if (channelOrderDetailMapper.selectCount(Wrappers.lambdaQuery(ChannelOrderDetail.class)
//                        .eq(ChannelOrderDetail::getCorpId, vo.getCorpId())) > 0) {
//                    throw new BizException("该渠道商已产生了酬金，不允许修改酬金比例");
//                }
//            }

            if (!String.join(StringPool.COMMA, vo.getChannelCooperationMode())
                    .contains(channelDistributorDetail.getChannelCooperationMode())) {
                throw new BizException("不允许变更或减少渠道商合作模式");
            }

            if (ChannelDistributorDetail.AllowNewChannelPackage.YES.getValue().equals(vo.getAllowNewPackage())) {
                ChannelPackageVo channelPackageVo = new ChannelPackageVo();
                channelPackageVo.setCorpId(vo.getCorpId());

                if (channelDistributorDetail.getLimitPackageNum().compareTo(vo.getLimitPacakageNum()) > 0) {
                    List<Package> channelPackages = pmsFeignClient.getChannelPackage(channelPackageVo).get();
                    final Set<String> set = channelPackages.stream()
                            .filter(e -> !AuditStatusEnum.NOT_PASS.matches(e.getAuditStatus()))
                            .map(Package::getId).collect(Collectors.toSet());
                    if (set.size() > vo.getLimitPacakageNum()) {
                        throw new BizException("修改的套餐上限数量过小，渠道商当前自建套餐数量：" + set.size());
                    }
                }

                Set<Long> groupIds = channelSelfpackageCountryRelationMapper.selectList(Wrappers.lambdaQuery(CmsChannelSelfpackageCountryRelation.class)
                                .eq(CmsChannelSelfpackageCountryRelation::getCorpId, vo.getCorpId()))
                        .stream()
                        .map(CmsChannelSelfpackageCountryRelation::getGroupId)
                        .collect(Collectors.toSet());

                if (groupIds != null && vo.getGroupId() != null) {
                    channelPackageVo.setGroupIds(groupIds.stream().filter(e -> !vo.getGroupId().contains(e.toString())).collect(Collectors.toSet()));
                }

                List<String> delTemplates = channelUpcctemplateRelationMapper.selectList(Wrappers.lambdaQuery(ChannelUpcctemplateRelation.class)
                                .select(ChannelUpcctemplateRelation::getTemplateId)
                                .eq(ChannelUpcctemplateRelation::getCorpId, vo.getCorpId()))
                        .stream()
                        .map(ChannelUpcctemplateRelation::getTemplateId)
                        //找出本次更新被删掉的templateId
                        .filter(e -> !vo.getUpccTemplateIds().contains(e))
                        .collect(Collectors.toList());
                channelPackageVo.setTemplateIds(delTemplates);
                if (!channelPackageVo.getGroupIds().isEmpty() || !delTemplates.isEmpty()) {
                    List<Package> channelPackages = pmsFeignClient.getChannelPackage(channelPackageVo).get();
                    if (!channelPackages.isEmpty()) {
                        throw new BizException("自建套餐已绑定渠道商现有国家卡池关联组或删除的可用速度模板");
                    }
                }
            }
            //判断是否删除自建套餐组
            ChannelPackageRelation channelPackageRelation = channelPackageRelationMapper.selectOne(Wrappers.lambdaQuery(ChannelPackageRelation.class)
                    .eq(ChannelPackageRelation::getCorpId, vo.getCorpId())
                    .eq(ChannelPackageRelation::getIsChannelCreate, IS_SELF_CREATE));
            if (ObjectUtil.isNotNull(channelPackageRelation)) {
                //判断是否删除自建套餐组
                boolean isDelete = false;
                isDelete = vo.getPackageInfos().stream().anyMatch(info -> info.getGroupId().equals(channelPackageRelation.getGroupId()));
                if (!isDelete) {
                    throw new BizException("不允许删除渠道商自建套餐组");
                } else {
                    log.info("新增和修改时移除自建套餐组，避免之后更新渠道商的自建套餐组");
                    Iterator<NewChannelVO.PackageInfo> iterator = vo.getPackageInfos().iterator();
                    while (iterator.hasNext()) {
                        NewChannelVO.PackageInfo str = iterator.next();
                        if (channelPackageRelation.getGroupId().equals(str.getGroupId())) {
                            iterator.remove();
                            break;
                        }
                    }
                }
            }


        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approvalChannel(String corpId, String decision) {
//        final String flag = String.format(PACKAGE_USE_PERCENTAGE_KEY, corpId);

        ChannelDistributorDetail one = channelDistributorDetailMapper.selectOne(new LambdaQueryWrapper<ChannelDistributorDetail>()
                .eq(ChannelDistributorDetail::getWhetherUpdateThreshold, "1")
                .eq(ChannelDistributorDetail::getCorpId, corpId));
        final boolean notifyFlag = Optional.ofNullable(one).isPresent();

//        final boolean notifyFlag = redisUtil.hasKey(flag);
        try {
            CmsChannelAuth cmsChannelAuth = channelAuthMapper.selectOne(Wrappers.lambdaQuery(CmsChannelAuth.class)
                    .select(CmsChannelAuth::getCheckStatus)
                    .eq(CmsChannelAuth::getCorpId, corpId));
            String checkStatus = cmsChannelAuth == null ? "" : cmsChannelAuth.getCheckStatus();
            //兼容原始数据
            if (StringUtils.isEmpty(checkStatus)) {
                Channel channel = this.baseMapper.selectById(corpId);
                if (CheckStatus.PASS.getStatus().equals(decision)) {
                    if (CheckStatus.DELETE_NEED_APPROVAL.getStatus().equals(channel.getCheckStatus())) {
                        deleteChannelInfo(corpId);
                        cmsChannelDirectionalRelationMapper.delete(Wrappers.lambdaUpdate(CmsChannelDirectionalRelation.class)
                                .eq(CmsChannelDirectionalRelation::getCorpId, corpId));
                    } else {
                        updateChannelStatus(corpId, CheckStatus.PASS.getStatus());
                        cmsChannelDirectionalRelationAuthMapper.delete(Wrappers.lambdaUpdate(CmsChannelDirectionalRelationAuth.class)
                                .eq(CmsChannelDirectionalRelationAuth::getCorpId, corpId));
                    }
                } else if (CheckStatus.NOT_PASS.getStatus().equals(decision)) {
                    updateChannelStatus(corpId, CheckStatus.NOT_PASS.getStatus());
                    cmsChannelDirectionalRelationAuthMapper.delete(Wrappers.lambdaUpdate(CmsChannelDirectionalRelationAuth.class)
                            .eq(CmsChannelDirectionalRelationAuth::getCorpId, corpId));
                }
            }

            if (CheckStatus.PASS.getStatus().equals(decision)) {
                if (CheckStatus.NEW_NEED_APPROVAL.getStatus().equals(checkStatus)) {
                    updateAuth2Original(corpId);
                    updateChannelStatus(corpId, Channel.StatusEnum.NORMAL.getValue(), CheckStatus.PASS.getStatus());
                } else if (CheckStatus.DELETE_NEED_APPROVAL.getStatus().equals(checkStatus)) {
//                    updateChannelStatus(corpId, Channel.StatusEnum.DELETED.getValue(), CheckStatus.PASS.getStatus());
                    deleteChannelInfo(corpId);
                    cmsChannelDirectionalRelationMapper.delete(Wrappers.lambdaUpdate(CmsChannelDirectionalRelation.class)
                            .eq(CmsChannelDirectionalRelation::getCorpId, corpId));
                } else if (CheckStatus.ALTER_NEED_APPROVAL.getStatus().equals(checkStatus)) {
                    //将审核表的数据更新到对应表中
                    updateAuth2Original(corpId);

                    if (notifyFlag) {
                        List<Channel> lowerChannel = getLowerChannel(corpId, true);
                        List<String> corpids = lowerChannel.stream().map(Channel::getCorpId).collect(Collectors.toList());
                        channelPackageCardMapper.update(null, Wrappers.lambdaUpdate(ChannelPackageCard.class)
                                .set(ChannelPackageCard::getFlowNoticeLevel, null)
                                .in(ChannelPackageCard::getCorpId, corpids));
//                        redisUtil.del(flag);
                        channelDistributorDetailMapper.update(null, new LambdaUpdateWrapper<ChannelDistributorDetail>()
                                .set(ChannelDistributorDetail::getWhetherUpdateThreshold, null)
                                .eq(ChannelDistributorDetail::getCorpId, corpId));
                    }
                } else {
                    return;
                }
            } else if (CheckStatus.NOT_PASS.getStatus().equals(decision) && CheckStatus.NEW_NEED_APPROVAL.getStatus().equals(checkStatus)) {
                //新建渠道商不通过
                updateChannelStatus(corpId, Channel.StatusEnum.NORMAL.getValue(), CheckStatus.NOT_PASS.getStatus());
            }
            cmsChannelDirectionalRelationAuthMapper.delete(Wrappers.lambdaUpdate(CmsChannelDirectionalRelationAuth.class)
                    .eq(CmsChannelDirectionalRelationAuth::getCorpId, corpId));

            channelAuthMapper.delete(Wrappers.lambdaQuery(CmsChannelAuth.class).eq(CmsChannelAuth::getCorpId, corpId));
            channelDistributorsAuthMapper.delete(Wrappers.lambdaQuery(CmsChannelDistributorsAuth.class).eq(CmsChannelDistributorsAuth::getCorpId, corpId));
            channelUpcctemplateRelationAuthMapper.delete(Wrappers.lambdaQuery(ChannelUpcctemplateRelationAuth.class)
                    .in(ChannelUpcctemplateRelationAuth::getCorpId, corpId));
            cmsChannelSelfpackageCountryRelationAuthMapper.delete(Wrappers.lambdaQuery(CmsChannelSelfpackageCountryRelationAuth.class)
                    .eq(CmsChannelSelfpackageCountryRelationAuth::getCorpId, corpId));
            cmsChannelPackageRelationAuthMapper.delete(Wrappers.lambdaQuery(CmsChannelPackageRelationAuth.class)
                    .in(CmsChannelPackageRelationAuth::getCorpId, corpId));
        } catch (Exception e) {

//            if (notifyFlag && !redisUtil.hasKey(flag)) {
//                redisUtil.set(flag, "1");
            boolean present = Optional.ofNullable(channelDistributorDetailMapper.selectOne(new LambdaQueryWrapper<ChannelDistributorDetail>()
                    .eq(ChannelDistributorDetail::getWhetherUpdateThreshold, "1")
                    .eq(ChannelDistributorDetail::getCorpId, corpId))).isPresent();
            if (notifyFlag && !present) {
                channelDistributorDetailMapper.update(null, new LambdaUpdateWrapper<ChannelDistributorDetail>()
                        .set(ChannelDistributorDetail::getWhetherUpdateThreshold, "1")
                        .eq(ChannelDistributorDetail::getCorpId, corpId));
            }
            log.error("", e);
            throw new BizException("审批失败");
        }
    }

    @Override
    public List<ChannelA2zOperatorDTO> getChannelA2zOperator(String corpId) {
        List<CmsChannelA2zOperator> cmsChannelA2zOperators = channelA2zOperatorMapper.selectList(Wrappers.lambdaQuery(CmsChannelA2zOperator.class)
                .eq(CmsChannelA2zOperator::getCorpId, corpId));

        if (CollectionUtils.isEmpty(cmsChannelA2zOperators)) return Collections.emptyList();

        List<ChannelA2zOperatorDTO> dto = new ArrayList<>();
        for (CmsChannelA2zOperator cmsChannelA2zOperator : cmsChannelA2zOperators) {
            ChannelA2zOperatorDTO channelA2zOperatorDTO = new ChannelA2zOperatorDTO();
            channelA2zOperatorDTO.setA2zOperatorChargeType(cmsChannelA2zOperator.getA2zOperatorChargeType());
            List<MccDTO> mccDTOS = omsFeignClient.getNameByMcc(Collections.singletonList(cmsChannelA2zOperator.getA2zOperatorMcc())).get();
            if (CollectionUtils.isNotEmpty(mccDTOS)) {
                channelA2zOperatorDTO.setCountry(ChannelA2zOperatorDTO.Country.builder()
                        .name(mccDTOS.get(0).getCountryCn())
                        .mcc(cmsChannelA2zOperator.getA2zOperatorMcc()).build());
            }

            List<String> operatorIds = Arrays.stream(cmsChannelA2zOperator.getA2zOperator().split(",")).collect(Collectors.toList());
            List<ChannelA2zOperatorDTO.Operator> collect = omsFeignClient.getOperator().get().stream()
                    .filter(e -> operatorIds.contains(e.getId().toString())).collect(Collectors.toList());
            channelA2zOperatorDTO.setOperators(collect);

            dto.add(channelA2zOperatorDTO);
        }

        return dto;
    }

    @Override
    @Transactional
    public Response updateChannelA2zOperatorByMcc(String mcc, List<Long> operatorIds) {
        List<CmsChannelA2zOperator> cmsChannelA2zOperators = channelA2zOperatorMapper.selectList(Wrappers.lambdaQuery(CmsChannelA2zOperator.class)
                .eq(CmsChannelA2zOperator::getA2zOperatorMcc, mcc));
        cmsChannelA2zOperators.forEach(c -> {
            List<Long> operatorId = Arrays.stream(c.getA2zOperator().split(",")).map(Long::parseLong).collect(Collectors.toList());
            List<Long> updateIds = operatorId.stream().filter(operatorIds::contains).collect(Collectors.toList());
            if (!updateIds.isEmpty()) {
                channelA2zOperatorMapper.delete(Wrappers.lambdaQuery(CmsChannelA2zOperator.class)
                        .eq(CmsChannelA2zOperator::getId, c.getId()));
                operatorId.removeAll(updateIds);
                if (!operatorId.isEmpty()) {
                    String updateString = operatorId.stream().map(String::valueOf).collect(Collectors.joining(","));
                    c.setA2zOperator(updateString);
                    channelA2zOperatorMapper.insert(c);
                }
            }
        });
        return Response.ok();
    }

    @Override
    public void deleteChannelA2zOperatorByMcc(String mcc) {
        channelA2zOperatorMapper.delete(Wrappers.lambdaQuery(CmsChannelA2zOperator.class)
                .eq(CmsChannelA2zOperator::getA2zOperatorMcc, mcc));
    }

    @Override
    public List<String> packageGroupBind(List<String> packageGroupList) {
        try {
            return channelPackageRelationMapper.getBindChannelByPackageGroupId(packageGroupList);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("查询绑定套餐组渠道失败");
        }
    }

    @Override
    public List<ChannelPackageGroupsVO> getChanelPackageGroups(String groupId) {

        return channelPackageRelationMapper.queryChanelPackageGroups(groupId);
    }

    @Override
    public Boolean deleteChannel(String corpId) {
        Integer count = channelRecordDetailMapper.getCountWithinTwoYears(corpId);

        if (count == 0) {
            return false;
        } else {
            channelMapper.deleteById(corpId);
            return true;
        }


    }

    @Override
    public void addTestImsi(String imsi) {
        try {
            if (testImsiMapper.selectCount(Wrappers.lambdaQuery(TestImsi.class).eq(TestImsi::getImsi, imsi)) > 0) {
                throw new BizException("测试imsi已存在");
            }
            testImsiMapper.insert(TestImsi.builder().imsi(imsi).build());
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("新增失败");
        }
    }

    @Override
    public void deleteTestImsi(List<String> ids) {
        try {
            testImsiMapper.deleteBatchIds(ids);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("删除失败");
        }
    }

    @Override
    public IPage<TestImsi> getTestImsi(String imsi, int pageNum, int pageSize) {
        try {
            IPage<TestImsi> page = new Page<>(pageNum, pageSize);
            return testImsiMapper.selectPage(page, Wrappers.lambdaQuery(TestImsi.class)
                    .eq(!StringUtils.isEmpty(imsi), TestImsi::getImsi, imsi)
                    .orderByDesc(TestImsi::getId));
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("查询失败");
        }
    }

    @Override
    public RealRule getCardRealNameInfo(RuleInformation ruleInformation) {
        List<ChannelRealNameInfo> infos = channelRealNameInfoMapper.selectList(Wrappers.lambdaQuery(ChannelRealNameInfo.class)
                .select(ChannelRealNameInfo::getRuleCode, ChannelRealNameInfo::getCertificatesTime, ChannelRealNameInfo::getUpdateTime, ChannelRealNameInfo::getIccid,
                        ChannelRealNameInfo::getAuthStatus, ChannelRealNameInfo::getUseStatus, ChannelRealNameInfo::getCertificatesType)
                .eq(ChannelRealNameInfo::getIccid, ruleInformation.getIccid())
                .eq(ChannelRealNameInfo::getRuleCode, ruleInformation.getRuleID())
                .orderByAsc(ChannelRealNameInfo::getUpdateTime));

        Map<String, ChannelRealNameInfo> map = infos.stream().collect(Collectors.toMap(ChannelRealNameInfo::getUseStatus, Function.identity(), (key1, key2) -> key2));

        ChannelRealNameInfo channelRealNameInfo = map.get(ChannelRealNameInfo.UseStatus.USING.getStatus());

        if (channelRealNameInfo == null && !infos.isEmpty()) {
            channelRealNameInfo = map.get(ChannelRealNameInfo.UseStatus.PROCESSING.getStatus());
            if (channelRealNameInfo == null) {
                channelRealNameInfo = map.get(ChannelRealNameInfo.UseStatus.BACKUP.getStatus());
                if (channelRealNameInfo == null) {
                    channelRealNameInfo = infos.stream()
                            .filter(f -> ChannelRealNameInfo.AuthStatusEnum.NOT_AUTH.getValue().equals(f.getAuthStatus()))
                            .max(Comparator.comparing(ChannelRealNameInfo::getUpdateTime))
                            .orElse(null);
                }
            }
        }

        RealRule realRule = new RealRule();
        BeanUtils.copyProperties(ruleInformation, realRule);

        if (channelRealNameInfo != null) {
            realRule.setAuthStatus(channelRealNameInfo.getAuthStatus());
            realRule.setCertificatesTime(channelRealNameInfo.getCertificatesTime());
            realRule.setCertificatesType(channelRealNameInfo.getCertificatesType());
        }
        return realRule;
    }

    @Override
    public List<ChannelTypeDTO> getChannelType(List<String> corpIds) {
        if (CollectionUtils.isEmpty(corpIds)) {
            return null;
        }
        return channelMapper.selectChannelType(corpIds);
    }

    @Override
    public void channelBillRecord(BillRecordVo billRecordVo) {
        log.debug("记录渠道商账单流水详情开始");
        String corpId = billRecordVo.getCorpId();
        ChannelDistributorDetail channelDistributorDetail = channelDistributorDetailMapper.selectOne(Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                .eq(ChannelDistributorDetail::getCorpId, corpId));
        //基本肯定不会为空，只是个容错，就不做返回码了
        if (channelDistributorDetail == null) {
            throw new BizException("系统数据异常");
        }
        BigDecimal deposit = channelDistributorDetail.getDeposit();
        String currencyCode = channelDistributorDetail.getCurrencyCode();
        BigDecimal amount = billRecordVo.getAmount();
        String billType = billRecordVo.getBillType();
        CmsChannelBillflowRecord.CmsChannelBillflowRecordBuilder billflowRecordBuilder = CmsChannelBillflowRecord.builder()
                .amount("569".contains(billType) ? amount.negate() : amount)
                .corpId(corpId)
                .currencyCode(currencyCode)
                .deposit(deposit)
                .type(billType)
                .cooperationMode(billRecordVo.getCooperationMode());
        List<String> billTypes = Arrays.asList("5", "6", "7", "8", "9", "12");

        if (billTypes.contains(billType)) {
            // 订购/退订写订单id、订购/退订时间
            billflowRecordBuilder.orderId(billRecordVo.getOrderId())
                    .createTime(billRecordVo.getOrderSubOrUnsubDate());
        } else {
            billflowRecordBuilder.createTime(new Date());
        }
        channelBillflowRecordMapper.insert(billflowRecordBuilder.build());
        log.debug("记录渠道商账单流水详情结束");
    }

    @Override
    public void channelChargeRecord(ChannelChargeRecord channelChargeRecord) {
        channelChargeRecordMapper.insert(channelChargeRecord);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void newTopChannel(NewTopChannelVO newTopChannelVO) {
        channelSameNameCheck(newTopChannelVO.getCorpName());

        String corpId = Utils.randomUUID();
        String appKey = Utils.randomUUID();
        String appSecret = Utils.randomUUID();
        CmsTopchannel cmsTopchannel = CmsTopchannel.builder()
                .corpId(corpId)
                .appKey(appKey)
                .authStatus("2")
                .appSecret(appSecret).build();
        BeanUtils.copyProperties(newTopChannelVO, cmsTopchannel);
        EopAccessDetail eopAccessDetail = EopAccessDetail.builder()
                .corpId(corpId)
                .appKey(appKey)
                .appSecret(appSecret)
                .notifyUrl("")
                .build();
        try {
            topchannelMapper.insert(cmsTopchannel);
            eopAccessDetailMapper.insert(eopAccessDetail);
            newTopChannelVO.getRelationChannel().forEach(
                    relationChannel -> {
                        sameRelationChannelCheck(relationChannel.getCorpId());
                        CmsChannelRelation channelRelation = CmsChannelRelation.builder()
                                .corpId(relationChannel.getCorpId())
                                .parentCorpId(corpId)
                                .provinceCode(relationChannel.getProvinceCode()).build();
                        channelRelationMapper.insert(channelRelation);
                    }
            );
        } catch (DuplicateKeyException e) {
            throw new BizException("省份编码重复");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTopChannel(UpdateTopChannelVO updateTopChannelVO) {
        CmsTopchannel topchannel = topchannelMapper.selectById(updateTopChannelVO.getCorpId());
        if (!topchannel.getCorpName().equals(updateTopChannelVO.getCorpName().trim())) {
            channelSameNameCheck(updateTopChannelVO.getCorpName());
        }

        CmsTopchannel cmsTopchannel = new CmsTopchannel();
        BeanUtils.copyProperties(updateTopChannelVO, cmsTopchannel);
        String topChannelCorpId = updateTopChannelVO.getCorpId();
        try {
            topchannelMapper.updateById(cmsTopchannel);
            channelRelationMapper.delete(Wrappers.lambdaQuery(CmsChannelRelation.class).eq(CmsChannelRelation::getParentCorpId, topChannelCorpId));
            if (ObjectUtil.isNull(updateTopChannelVO.getRelationChannel())) {
                return;
            }
            updateTopChannelVO.getRelationChannel().forEach(
                    relationChannel -> {
                        sameRelationChannelCheck(relationChannel.getCorpId());
                        CmsChannelRelation channelRelation = CmsChannelRelation.builder()
                                .corpId(relationChannel.getCorpId())
                                .parentCorpId(topChannelCorpId)
                                .provinceCode(relationChannel.getProvinceCode()).build();
                        channelRelationMapper.insert(channelRelation);
                    }
            );
        } catch (DuplicateKeyException e) {
            throw new BizException("省份编码重复");
        }
    }

    private void channelSameNameCheck(String channelName) {
        int channelSameNameNum = channelMapper.selectCount(Wrappers.lambdaQuery(Channel.class).eq(Channel::getCorpName, channelName.trim()));
        if (channelSameNameNum == 0) {
            int topchannelSamNameNum = topchannelMapper.selectCount(Wrappers.lambdaQuery(CmsTopchannel.class).eq(CmsTopchannel::getCorpName, channelName.trim()));
            if (topchannelSamNameNum > 0) {
                throw new BizException("存在相同名称的0级渠道商");
            }
        } else {
            throw new BizException("存在相同名称的1级渠道商");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTopChannel(String corpId) {
        try {
            LambdaQueryWrapper<CmsChannelRelation> wrapper =
                    Wrappers.lambdaQuery(CmsChannelRelation.class).eq(CmsChannelRelation::getParentCorpId, corpId);
            Integer relationChannelNum = channelRelationMapper.selectCount(wrapper);
            if (relationChannelNum > 0) {
                throw new BizException("存在关联的一级渠道商，不允许删除0级渠道");
            }

            topchannelMapper.deleteById(corpId);
            eopAccessDetailMapper.deleteById(corpId);
            channelRelationMapper.delete(wrapper);
        } catch (BizException ex) {
            throw ex;
        } catch (Exception e) {
            log.error("删除0级渠道商失败：", e);
            throw new BizException("系统异常");
        }
    }

    @Override
    public IPage<UpdateTopChannelVO> getTopChannel(int pageNum, int pageSize) {
        IPage<CmsTopchannel> page = new Page<>(pageNum, pageSize);

        IPage<CmsTopchannel> cmsTopchannels = topchannelMapper.selectPage(page, Wrappers.lambdaQuery(CmsTopchannel.class));
        List<UpdateTopChannelVO> updateTopChannelVOList = new ArrayList<>();
        for (CmsTopchannel topChannel : cmsTopchannels.getRecords()) {
            UpdateTopChannelVO updateTopChannelVO = new UpdateTopChannelVO();
            BeanUtils.copyProperties(topChannel, updateTopChannelVO);
            EopAccessDetail eopAccessDetail = eopAccessDetailMapper.selectById(topChannel.getCorpId());
            updateTopChannelVO.setAppkey(eopAccessDetail.getAppKey());
            updateTopChannelVO.setAppsecret(eopAccessDetail.getAppSecret());

            List<CmsChannelRelation> channelRelations =
                    channelRelationMapper.selectList(Wrappers.lambdaQuery(CmsChannelRelation.class).eq(CmsChannelRelation::getParentCorpId, topChannel.getCorpId()));
            final List<String> relationCorpIds =
                    channelRelations.stream().map(CmsChannelRelation::getCorpId).collect(Collectors.toList());
            if (relationCorpIds.size() == 0) {
                updateTopChannelVOList.add(updateTopChannelVO);
                continue;
            }
            final List<Channel> channels =
                    channelMapper.selectList(Wrappers.lambdaQuery(Channel.class).select(Channel::getCorpId, Channel::getCorpName).in(Channel::getCorpId, relationCorpIds));
            final Map<String, String> relationChannelNameMap = channels.stream().collect(Collectors.toMap(Channel::getCorpId, Channel::getCorpName));
            List<NewTopChannelVO.RelationChannel> relationChannelList = new ArrayList<>();
            channelRelations.forEach(relationChannel -> {
                NewTopChannelVO.RelationChannel channel = NewTopChannelVO.RelationChannel.builder()
                        .corpId(relationChannel.getCorpId())
                        .corpName(relationChannelNameMap.get(relationChannel.getCorpId()))
                        .provinceCode(relationChannel.getProvinceCode()).build();
                relationChannelList.add(channel);
            });

            updateTopChannelVO.setRelationChannel(relationChannelList);
            updateTopChannelVOList.add(updateTopChannelVO);
        }

        IPage<UpdateTopChannelVO> topChannelVOIPage = new Page<>(pageNum, pageSize);
        topChannelVOIPage.setRecords(updateTopChannelVOList);
        topChannelVOIPage.setTotal(cmsTopchannels.getTotal());
        topChannelVOIPage.setSize(cmsTopchannels.getSize());
        return topChannelVOIPage;
    }


    @Override
    public List<String> getTopChannelRelationCorpIds(String corpId) {
        return channelRelationMapper.selectList(Wrappers.lambdaQuery(CmsChannelRelation.class)
                        .eq(CmsChannelRelation::getParentCorpId, corpId))
                .stream()
                .map(CmsChannelRelation::getCorpId)
                .collect(Collectors.toList());
    }

    @Override
    public IPage<SeconedChannelVO> getCorpList(int pageNum, int pageSize, String corpName, String topChannelCorpId) {
        IPage<SeconedChannelVO> page = new Page<>(pageNum, pageSize);
        return channelMapper.getCorpList(page, topChannelCorpId, corpName);
    }

    private void sameRelationChannelCheck(String corpId) {
        Integer count = channelRelationMapper.selectCount(Wrappers.lambdaQuery(CmsChannelRelation.class).eq(CmsChannelRelation::getCorpId, corpId));
        if (count > 0) {
            throw new BizException("存在被其他0级渠道关联的子渠道商");
        }
    }

    private boolean updateChannelBlankCheck(Integer originalVal, Integer updateVal) {
        if (originalVal == null && updateVal == null) {
            return false;
        } else if (originalVal != null && updateVal != null) {
            return !originalVal.equals(updateVal);
        }

        return true;
    }

    //判断deposit、discount、reset_price、direct_ratio、indirect_ratio是否变更。
    public boolean isUpdate(UpdateChannelVO newChannelVO, ChannelDistributorDetail channelDistributorDetail, boolean isUpdateNotpassChannel, boolean appIdChangeFlag) {

        if (updateChannelThreadLoacl.get() != null && updateChannelThreadLoacl.get()) {
            return true;
        }

        if (isUpdateNotpassChannel) {
            return true;
        }
        if (appIdChangeFlag) {
            return true;
        }
        if (!compare(newChannelVO.getDiscount(), channelDistributorDetail.getDiscount(), Integer.class)) {
            return true;
        } else if (!newChannelVO.getEsimNotifySwitch().equals(channelDistributorDetail.getEsimNotification())) {
            return true;
        } else if ("1".equals(newChannelVO.getEsimNotifySwitch()) && !newChannelVO.getEsimNotifyUrl().equals(channelDistributorDetail.getEsimNotificationUrl())) {
            return true;
        } else if (!compare(newChannelVO.getCreditAmount(), channelDistributorDetail.getCreditAmount(), BigDecimal.class)) {
            return true;
        } else if (!compare(newChannelVO.getMarketingAmount(), channelDistributorDetail.getMarketingAmount(), BigDecimal.class)) {
            return true;
        } else if (!compare(newChannelVO.getA2zMarketingAmount(), channelDistributorDetail.getA2zMarketingAmount(), BigDecimal.class)) {
            return true;
        } else if (!newChannelVO.getUnsubscribeRule().equals(channelDistributorDetail.getUnsubscribeRule())) {
            return true;
        } else if (StrUtil.isNotBlank(newChannelVO.getAllowNewPackage()) && !newChannelVO.getAllowNewPackage().equals(channelDistributorDetail.getAllowNewPackage())) {
            return true;
        } else if (!String.join(StringPool.COMMA, newChannelVO.getChannelCooperationMode()).equals(channelDistributorDetail.getChannelCooperationMode())) {
            return true;
        } else if (!String.join(StringPool.COMMA, newChannelVO.getPackageUsePercentage()).equals(channelDistributorDetail.getPackageUsePercentage())) {
//            final String flag = String.format(PACKAGE_USE_PERCENTAGE_KEY, newChannelVO.getCorpId());
//            if (!redisUtil.hasKey(flag)) {
//                redisUtil.set(flag, "1");
//            }
            channelDistributorDetailMapper.update(null, new LambdaUpdateWrapper<ChannelDistributorDetail>()
                    .set(ChannelDistributorDetail::getWhetherUpdateThreshold, "1")
                    .eq(ChannelDistributorDetail::getCorpId, newChannelVO.getCorpId()));
            return true;
        } else if (!newChannelVO.getApprovalPackage().equals(channelDistributorDetail.getApprovalPackage())) {
            return true;
        } else if (channelDistributorDetail.getA2zAccountingPeriodId() != null && !channelDistributorDetail.getA2zAccountingPeriodId().toString().equals(newChannelVO.getA2zAccountingPeriodId())) {
            return true;
        } else if (channelDistributorDetail.getDistributionAccountingPeriodId() != null && !channelDistributorDetail.getDistributionAccountingPeriodId().toString().equals(newChannelVO.getDistributionAccountingPeriodId())) {
            return true;
        } else if (newChannelVO.getResourceAccountingPeriodId() != null && !channelDistributorDetail.getResourceAccountingPeriodId().toString().equals(newChannelVO.getResourceAccountingPeriodId())) {
            return true;
        } else if (StrUtil.isBlank(newChannelVO.getAllowNewPackage()) && !newChannelVO.getAllowNewPackage().equals(channelDistributorDetail.getAllowNewPackage())) {
            return true;
        } else if (newChannelVO.getLimitPacakageNum() != null &&
                channelDistributorDetail.getLimitPackageNum() != null &&
                !newChannelVO.getLimitPacakageNum().equals(channelDistributorDetail.getLimitPackageNum())) {
            return true;
        }

        if (newChannelVO.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.CONSIGNMENT.getType())) {
            if (!newChannelVO.getChannelType().equals(channelDistributorDetail.getChannelType())) {
                return true;
            }
        }

        if (newChannelVO.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.A2Z.getType())) {
            if (!compare(newChannelVO.getProhibitiveBuyRemindThreshold(), channelDistributorDetail.getProhibitiveBuyRemindThreshold(), BigDecimal.class)) {
                return true;
            } else if (!compare(newChannelVO.getRunoutofBalanceRemindThreshold(), channelDistributorDetail.getRunoutofBalanceRemindThreshold(), BigDecimal.class)) {
                return true;
            } else if (!compare(newChannelVO.getStopUseRemindThreshold(), channelDistributorDetail.getStopUseRemindThreshold(), BigDecimal.class)) {
                return true;
            } else if (!newChannelVO.getA2zChannelType().equals(channelDistributorDetail.getA2zChannelType())) {
                return true;
            }

            List<Long> collect = channelSelfpackageCountryRelationMapper.selectList(Wrappers.lambdaQuery(CmsChannelSelfpackageCountryRelation.class)
                            .eq(CmsChannelSelfpackageCountryRelation::getCorpId, newChannelVO.getCorpId())).stream()
                    .map(CmsChannelSelfpackageCountryRelation::getGroupId)
                    .collect(Collectors.toList());
            List<Long> dels = collect.stream().filter(e -> !newChannelVO.getGroupId().contains(e.toString())).collect(Collectors.toList());
            if (!dels.isEmpty()) {
                Integer i = channelBlankcardOrderMapper.selectCount(new LambdaQueryWrapper<CmsChannelBlankcardOrder>()
                        .eq(CmsChannelBlankcardOrder::getOrderUserId, newChannelVO.getCorpId())
                        .eq(CmsChannelBlankcardOrder::getCooperationMode, "2")
                        .in(CmsChannelBlankcardOrder::getGroupId, dels));
                if (i > 0) {
                    throw new BizException("不允许删除卡已绑定的国家卡池关联组");
                }

                Integer j = channelCardMapper.selectCount(new LambdaQueryWrapper<ChannelCard>()
                        .eq(ChannelCard::getCorpId, newChannelVO.getCorpId())
                        .eq(ChannelCard::getCooperationMode, "2")
                        .in(ChannelCard::getGroupId, dels));
                if (j > 0) {
                    throw new BizException("不允许删除卡已绑定的国家卡池关联组");
                }
            }

            if (newChannelVO.getGroupId() != null && newChannelVO.getGroupId().size() != collect.size() ||
                    collect.stream().anyMatch(ruleId -> !newChannelVO.getGroupId().contains(ruleId.toString()))) {
                return true;
            }
        }

        if (newChannelVO.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.RESOURCE_COOPERATION.getType())) {
            if (!compare(newChannelVO.getResourceProhibitiveBuyRemindThreshold(), channelDistributorDetail.getResourceProhibitiveBuyRemindThreshold(), BigDecimal.class)) {
                return true;
            } else if (!compare(newChannelVO.getResourceRunoutofBalanceRemindThreshold(), channelDistributorDetail.getResourceRunoutofBalanceRemindThreshold(), BigDecimal.class)) {
                return true;
            } else if (!compare(newChannelVO.getResourceStopUseRemindThreshold(), channelDistributorDetail.getResourceStopUseRemindThreshold(), BigDecimal.class)) {
                return true;
            } else if (!newChannelVO.getResourceChannelType().equals(channelDistributorDetail.getResourceChannelType())) {
                return true;
            }
        }

        if (ChannelDistributorDetail.AllowNewChannelPackage.YES.getValue().equals(newChannelVO.getAllowNewPackage())) {
            List<String> originTemplateIds = channelUpcctemplateRelationMapper.selectList(Wrappers.lambdaQuery(ChannelUpcctemplateRelation.class)
                            .eq(ChannelUpcctemplateRelation::getCorpId, newChannelVO.getCorpId()))
                    .stream().map(ChannelUpcctemplateRelation::getTemplateId)
                    .collect(Collectors.toList());
            if (originTemplateIds.size() != newChannelVO.getUpccTemplateIds().size()) {
                return true;
            } else return !originTemplateIds.containsAll(newChannelVO.getUpccTemplateIds());
        }

        return false;
    }

    /**
     * @param corpId
     * @param status      渠道商状态
     * @param checkStatus 渠道商审核状态
     */
    private void updateChannelStatus(String corpId, String status, String checkStatus) {
        this.baseMapper.update(null, Wrappers.lambdaUpdate(Channel.class)
                .set(Channel::getCheckStatus, checkStatus)
                .set(Channel::getStatus, status)
                .eq(Channel::getCorpId, corpId));
    }

    private void updateChannelStatus(String corpId, String checkStatus) {
        this.baseMapper.update(null, Wrappers.lambdaUpdate(Channel.class)
                .set(Channel::getCheckStatus, checkStatus)
                .eq(Channel::getCorpId, corpId));
    }

    private void updateAuth2Original(String corpId) {
        channelAuthMapper.updateChannel(channelAuthMapper.selectById(corpId));
        channelDistributorsAuthMapper.updateChannelDistributors(channelDistributorsAuthMapper
                .selectOne(Wrappers.lambdaQuery(CmsChannelDistributorsAuth.class).eq(CmsChannelDistributorsAuth::getCorpId, corpId)));

        List<ChannelUpcctemplateRelationAuth> channelUpcctemplateRelationAuths =
                channelUpcctemplateRelationAuthMapper.selectList(Wrappers.lambdaQuery(ChannelUpcctemplateRelationAuth.class)
                        .eq(ChannelUpcctemplateRelationAuth::getCorpId, corpId));
        if (!channelUpcctemplateRelationAuths.isEmpty()) {
            channelUpcctemplateRelationAuthMapper.delete(Wrappers.lambdaQuery(ChannelUpcctemplateRelationAuth.class)
                    .in(ChannelUpcctemplateRelationAuth::getCorpId, corpId));
            channelUpcctemplateRelationMapper.delete(Wrappers.lambdaQuery(ChannelUpcctemplateRelation.class)
                    .in(ChannelUpcctemplateRelation::getCorpId, corpId));
            final List<ChannelUpcctemplateRelation> collect = channelUpcctemplateRelationAuths.stream()
                    .map(m -> new ChannelUpcctemplateRelation().setCorpId(corpId).setTemplateId(m.getTemplateId()))
                    .collect(Collectors.toList());
            channelUpcctemplateRelationMapper.batchInsert(collect);
        }

        List<CmsChannelPackageRelationAuth> channelPackageRelationAuths =
                cmsChannelPackageRelationAuthMapper.selectList(Wrappers.lambdaQuery(CmsChannelPackageRelationAuth.class)
                        .eq(CmsChannelPackageRelationAuth::getCorpId, corpId));

        if (!channelPackageRelationAuths.isEmpty()) {
            cmsChannelPackageRelationAuthMapper.delete(Wrappers.lambdaQuery(CmsChannelPackageRelationAuth.class)
                    .in(CmsChannelPackageRelationAuth::getCorpId, corpId));
            channelPackageRelationMapper.delete(Wrappers.lambdaQuery(ChannelPackageRelation.class)
                    .eq(ChannelPackageRelation::getIsChannelCreate, NO_SELF_CREATE)
                    .in(ChannelPackageRelation::getCorpId, corpId));

            List<ChannelPackageRelation> channelPackageRelations = new ArrayList<>();
            channelPackageRelationAuths.forEach(e -> {
                final ChannelPackageRelation channelPackageRelation = new ChannelPackageRelation();
                BeanUtils.copyProperties(e, channelPackageRelation);
                channelPackageRelations.add(channelPackageRelation);
            });
            channelPackageRelationMapper.inserBatch(channelPackageRelations);
        }

        List<CmsChannelSelfpackageCountryRelationAuth> cmsChannelSelfpackageCountryRelationAuths = cmsChannelSelfpackageCountryRelationAuthMapper.selectList(Wrappers.lambdaQuery(CmsChannelSelfpackageCountryRelationAuth.class)
                .eq(CmsChannelSelfpackageCountryRelationAuth::getCorpId, corpId));
        if (!cmsChannelSelfpackageCountryRelationAuths.isEmpty()) {
            cmsChannelSelfpackageCountryRelationAuthMapper.delete(Wrappers.lambdaQuery(CmsChannelSelfpackageCountryRelationAuth.class)
                    .in(CmsChannelSelfpackageCountryRelationAuth::getCorpId, corpId));
            channelSelfpackageCountryRelationMapper.delete(Wrappers.lambdaQuery(CmsChannelSelfpackageCountryRelation.class)
                    .in(CmsChannelSelfpackageCountryRelation::getCorpId, corpId));
            cmsChannelSelfpackageCountryRelationAuths.forEach(e -> {
                CmsChannelSelfpackageCountryRelation cmsChannelSelfpackageCountryRelation = new CmsChannelSelfpackageCountryRelation();
                cmsChannelSelfpackageCountryRelation.setCorpId(e.getCorpId());
                cmsChannelSelfpackageCountryRelation.setGroupId(e.getGroupId());
                channelSelfpackageCountryRelationMapper.insert(cmsChannelSelfpackageCountryRelation);
            });
        }

        // 删除基础表数据
        cmsChannelDirectionalRelationMapper.delete(Wrappers.lambdaUpdate(CmsChannelDirectionalRelation.class).eq(CmsChannelDirectionalRelation::getCorpId, corpId));
        List<CmsChannelDirectionalRelation> cmsChannelDirectionalRelations = new ArrayList<>();
        // 查询审核表数据
        cmsChannelDirectionalRelationAuthMapper.selectList(Wrappers.lambdaQuery(CmsChannelDirectionalRelationAuth.class)
                .eq(CmsChannelDirectionalRelationAuth::getCorpId, corpId)).forEach(relationAuth -> {
            cmsChannelDirectionalRelations.add(CmsChannelDirectionalRelation.builder().appId(relationAuth.getAppId()).corpId(corpId).build());
        });
        // 新增基础表数据
        cmsChannelDirectionalRelationService.saveBatch(cmsChannelDirectionalRelations);
        // 删除审核表数据
        cmsChannelDirectionalRelationAuthMapper.delete(Wrappers.lambdaUpdate(CmsChannelDirectionalRelationAuth.class).eq(CmsChannelDirectionalRelationAuth::getCorpId, corpId));
    }

    private void deleteChannelInfo(String corpId) {
        this.baseMapper.delete(Wrappers.lambdaQuery(Channel.class)
                .eq(Channel::getCorpId, corpId));
        channelDistributorDetailMapper.delete(Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                .eq(ChannelDistributorDetail::getCorpId, corpId));
        channelPackageRelationMapper.delete(Wrappers.lambdaQuery(ChannelPackageRelation.class)
                .eq(ChannelPackageRelation::getCorpId, corpId));
        //删除渠道账户
        backFeignClient.deleteChannelAccount(corpId);
        channelUpcctemplateRelationMapper.delete(Wrappers.lambdaQuery(ChannelUpcctemplateRelation.class)
                .eq(ChannelUpcctemplateRelation::getCorpId, corpId));
        channelPackageRelationMapper.delete(Wrappers.lambdaQuery(ChannelPackageRelation.class)
                .in(ChannelPackageRelation::getCorpId, corpId));
        channelImsiAmountRelationMapper.delete(Wrappers.lambdaQuery(CmsChannelImsiAmountRelation.class)
                .eq(CmsChannelImsiAmountRelation::getCorpId, corpId));
        channelImsiAmountRelationMapper.delete(Wrappers.lambdaQuery(CmsChannelImsiAmountRelation.class)
                .eq(CmsChannelImsiAmountRelation::getCorpId, corpId));

        channelA2zruleRelationMapper.delete(Wrappers.lambdaQuery(CmsChannelA2zruleRelation.class)
                .eq(CmsChannelA2zruleRelation::getCorpId, corpId));
        channelResourceruleRelationMapper.delete(Wrappers.lambdaQuery(CmsChannelResourceruleRelation.class)
                .eq(CmsChannelResourceruleRelation::getCorpId, corpId));
        channelSmsTemplateRelationMapper.delete(Wrappers.lambdaQuery(CmsChannelSmsTemplateRelation.class)
                .eq(CmsChannelSmsTemplateRelation::getCorpId, corpId));
        channelSelfpackageCountryRelationMapper.delete(Wrappers.lambdaQuery(CmsChannelSelfpackageCountryRelation.class)
                .eq(CmsChannelSelfpackageCountryRelation::getCorpId, corpId));
    }

    private void writeAuthTable(UpdateChannelVO updateChannelVO, boolean isNewChannel) {
        //写入或更新cms_channel_auth
        if (channelAuthService.count(new LambdaQueryWrapper<CmsChannelAuth>().eq(CmsChannelAuth::getCorpId, updateChannelVO.getCorpId())) > 0) {
            channelAuthService.update(buildChannelAuth(updateChannelVO, isNewChannel),
                    new LambdaQueryWrapper<CmsChannelAuth>().eq(CmsChannelAuth::getCorpId, updateChannelVO.getCorpId()));
        } else {
            channelAuthService.save(buildChannelAuth(updateChannelVO, isNewChannel));
        }

        //写入或更新cms_channel_distributors_auth
        if (channelDistributorsAuthIService.count(Wrappers.lambdaQuery(CmsChannelDistributorsAuth.class)
                .eq(CmsChannelDistributorsAuth::getCorpId, updateChannelVO.getCorpId())) > 0) {
            channelDistributorsAuthIService.update(buildChannelDistributorsAuth(updateChannelVO),
                    Wrappers.lambdaQuery(CmsChannelDistributorsAuth.class)
                            .eq(CmsChannelDistributorsAuth::getCorpId, updateChannelVO.getCorpId()));
        } else {
            channelDistributorsAuthIService.save(buildChannelDistributorsAuth(updateChannelVO));
        }

        // 定向应用集合
        // 删除审核表数据
        cmsChannelDirectionalRelationAuthMapper.delete(Wrappers.lambdaUpdate(CmsChannelDirectionalRelationAuth.class).eq(CmsChannelDirectionalRelationAuth::getCorpId, updateChannelVO.getCorpId()));
        // 新增审核表数据
        List<CmsChannelDirectionalRelationAuth> cmsChannelDirectionalRelations = new ArrayList<>();
        updateChannelVO.getAppids().forEach(id -> {
            cmsChannelDirectionalRelations.add(CmsChannelDirectionalRelationAuth.builder().appId(Long.parseLong(id)).corpId(updateChannelVO.getCorpId()).build());
        });
        cmsChannelDirectionalRelationAuthService.saveBatch(cmsChannelDirectionalRelations);

        //非审核字段直接写入详情表
        channelMapper.updateById(buildChannel(updateChannelVO));
        ChannelDistributorDetail channelDistributorDetail = updateChannelVO.getChannelDistributorDetail();
        channelDistributorDetailMapper.update(null, Wrappers.lambdaUpdate(ChannelDistributorDetail.class)
                        .eq(ChannelDistributorDetail::getCorpId, updateChannelVO.getCorpId())
                        .set(ChannelDistributorDetail::getIsSub, updateChannelVO.getChannelStatus())
//                .set(ChannelDistributorDetail::getChannelCode, updateChannelVO.getChannelCode())
//                .set(ChannelDistributorDetail::getChannelUrl, updateChannelVO.getChannelUrl())
                        .set(ChannelDistributorDetail::getAppKey, updateChannelVO.getAppkey())
                        .set(ChannelDistributorDetail::getAppSecret, updateChannelVO.getAppSecret())
//                .set(ChannelDistributorDetail::getDepositeReset, updateChannelVO.getDepositeReset())
                        .set(ChannelDistributorDetail::getDepositeRemindThreshold, updateChannelVO.getDepositNotify())
                        .set(ChannelDistributorDetail::getCurrencyCode, updateChannelVO.getCurrencyCode())
                        .set(ChannelDistributorDetail::getContractStartTime, updateChannelVO.getContractBeginTime())
                        .set(ChannelDistributorDetail::getContractEndTime, updateChannelVO.getContractEndTime())
                        .set(ChannelDistributorDetail::getDepositAmount, updateChannelVO.getContractSellAmount())
//                .set(ChannelDistributorDetail::getIndirectType, updateChannelVO.getLimitType())
//                .set(ChannelDistributorDetail::getIndirectCount, IndirectCountEnum.BY_COUNT.getType().equals(updateChannelVO.getLimitType()) ? updateChannelVO.getAccountNum() : updateChannelVO.getCreateTime())
                        .set(ChannelDistributorDetail::getEmail, updateChannelVO.getMail())
                        .set(ChannelDistributorDetail::getActivateNotificationUrl, updateChannelVO.getActivateNotificationUrl())
                        .set(ChannelDistributorDetail::getAccountNum, updateChannelVO.getCreateAccountNumber())
                        .set(ChannelDistributorDetail::getPackageUseNotifyUrl, updateChannelVO.getPackageUseNotifyUrl())
                        .set(ChannelDistributorDetail::getOverdueNotifyUrl, updateChannelVO.getOverdueNotifyUrl())
                        .set(ChannelDistributorDetail::getOverdueNotify, updateChannelVO.getOverdueNotify())
                        .set(ChannelDistributorDetail::getDepositeRemindThreshold, updateChannelVO.getDepositNotify())
                        .set(ChannelDistributorDetail::getA2zContractEndTime, updateChannelVO.getA2zContractEndTime())
                        .set(ChannelDistributorDetail::getA2zContractStartTime, updateChannelVO.getA2zContractStartTime())
                        .set(ChannelDistributorDetail::getSalesMail, updateChannelVO.getSalesMail())
                        .set(updateChannelVO.getContractEndTime() != null &&
                                        !updateChannelVO.getContractEndTime().equals(channelDistributorDetail.getContractEndTime()),
                                ChannelDistributorDetail::getContractConsignmentNotify, "1")
                        .set(updateChannelVO.getA2zContractEndTime() != null &&
                                        !updateChannelVO.getA2zContractEndTime().equals(channelDistributorDetail.getA2zContractEndTime()),
                                ChannelDistributorDetail::getA2zConsignmentNotify, "1")
                        .set(StrUtil.isNotBlank(updateChannelVO.getA2zDepositAmount()), ChannelDistributorDetail::getA2zDepositAmount, updateChannelVO.getA2zDepositAmount())
        );
    }

    private CmsChannelDistributorsAuth buildChannelDistributorsAuth(UpdateChannelVO updateChannelVO) {
        CmsChannelDistributorsAuth cmsChannelDistributorsAuth = new CmsChannelDistributorsAuth();
        BeanUtils.copyProperties(buildChannelDistributorDetail(updateChannelVO), cmsChannelDistributorsAuth);
        cmsChannelDistributorsAuth.setApprovalPackage(updateChannelVO.getApprovalPackage());
        cmsChannelDistributorsAuth.setDistributionAccountingPeriodId(updateChannelVO.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.CONSIGNMENT.getType()) ?
                getAccountingPeriodId(updateChannelVO.getDistributionAccountingPeriodId()) : null);
        cmsChannelDistributorsAuth.setA2zAccountingPeriodId(updateChannelVO.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.A2Z.getType()) ?
                getAccountingPeriodId(updateChannelVO.getA2zAccountingPeriodId()) : null);
        cmsChannelDistributorsAuth.setResourceAccountingPeriodId(updateChannelVO.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.RESOURCE_COOPERATION.getType()) ?
                getAccountingPeriodId(updateChannelVO.getResourceAccountingPeriodId()) : null);
        return cmsChannelDistributorsAuth;
    }

    private CmsChannelAuth buildChannelAuth(UpdateChannelVO updateChannelVO, boolean isNewChannel) {
        CmsChannelAuth cmsChannelAuth = new CmsChannelAuth();
        BeanUtils.copyProperties(buildChannel(updateChannelVO), cmsChannelAuth);
        cmsChannelAuth.setCheckStatus(isNewChannel ? CheckStatus.NEW_NEED_APPROVAL.getStatus() : CheckStatus.ALTER_NEED_APPROVAL.getStatus());
        cmsChannelAuth.setEBSCode(updateChannelVO.getEbsCode());
        cmsChannelAuth.setType(Channel.ChannelTypeEnum.CHANNEL.getValue());

        return cmsChannelAuth;
    }

    private Channel buildChannel(UpdateChannelVO updateChannelVO) {
        return Channel.builder()
                .corpId(updateChannelVO.getCorpId())
                .corpName(updateChannelVO.getCorpName().trim())
                .status(CorpStatus.COMMON.getStatus())
                .type(Channel.ChannelTypeEnum.CHANNEL.getValue())
                .ebsCode(updateChannelVO.getEbsCode())
                .currencyCode(updateChannelVO.getCurrencyCode())
                .internalOrder(updateChannelVO.getInternalOrder())
                .companyName(updateChannelVO.getCompanyName())
                .address(updateChannelVO.getAddress()).build();
    }

    private ChannelDistributorDetail buildChannelDistributorDetail(UpdateChannelVO updateChannelVO) {
        ChannelDistributorDetail detail = ChannelDistributorDetail.builder()
                .corpId(updateChannelVO.getCorpId())
                .isSub(updateChannelVO.getChannelStatus())
//                .channelCode(updateChannelVO.getChannelCode())
//                .channelUrl(updateChannelVO.getChannelUrl())
                .appKey(updateChannelVO.getAppkey())
                .appSecret(updateChannelVO.getAppSecret())
                .depositeReset(updateChannelVO.getDepositeReset())
                .resetPrice(updateChannelVO.getResetPrice())
//                .deposit(updateChannelVO.getDeposit())
                .depositeRemindThreshold(updateChannelVO.getDepositNotify())
                .currencyCode(updateChannelVO.getCurrencyCode())
                .discount(updateChannelVO.getDiscount())
                .contractStartTime(updateChannelVO.getContractBeginTime())
                .contractEndTime(updateChannelVO.getContractEndTime())
                .depositAmount(updateChannelVO.getContractSellAmount())
//                .directRatio(updateChannelVO.getDirectEarningsRatio())
//                .indirectRatio(updateChannelVO.getIndirectEarningsRatio())
//                .indirectType(updateChannelVO.getLimitType())
                .email(updateChannelVO.getMail())
                .accountNum(updateChannelVO.getCreateAccountNumber())
//                .indirectCount(IndirectCountEnum.BY_COUNT.getType().equals(updateChannelVO.getLimitType()) ? updateChannelVO.getAccountNum() : updateChannelVO.getCreateTime())
                .activateNotification(updateChannelVO.getActivateNotification())
                .channelType(updateChannelVO.getChannelType())
                .unsubscribeRule(updateChannelVO.getUnsubscribeRule())
                .activateNotificationUrl(updateChannelVO.getActivateNotificationUrl())
//                .totalDeposit(updateChannelVO.getTotalDeposit())
                .allowNewPackage(updateChannelVO.getAllowNewPackage())
                .runoutofBalanceRemindThreshold(updateChannelVO.getRunoutofBalanceRemindThreshold())
                .stopUseRemindThreshold(updateChannelVO.getStopUseRemindThreshold())
                .prohibitiveBuyRemindThreshold(updateChannelVO.getProhibitiveBuyRemindThreshold())
                .channelCooperationMode(String.join(StringPool.COMMA, updateChannelVO.getChannelCooperationMode()))
                .packageUsePercentage(String.join(StringPool.COMMA, updateChannelVO.getPackageUsePercentage()))
                .overdueNotify(updateChannelVO.getOverdueNotify())
                .overdueNotifyUrl(updateChannelVO.getOverdueNotifyUrl())
                .packageUseNotifyUrl(updateChannelVO.getPackageUseNotifyUrl())
                .esimNotification(updateChannelVO.getEsimNotifySwitch())
                .esimNotificationUrl(updateChannelVO.getEsimNotifyUrl())
                .a2zContractStartTime(updateChannelVO.getA2zContractStartTime())
                .a2zContractEndTime(updateChannelVO.getA2zContractEndTime())
                .a2zDepositAmount(StrUtil.isBlank(updateChannelVO.getA2zDepositAmount()) ? null : new BigDecimal(updateChannelVO.getA2zDepositAmount()))
                .a2zChannelType(updateChannelVO.getA2zChannelType())
                .salesMail(updateChannelVO.getSalesMail())
                .resourceStopUseRemindThreshold(updateChannelVO.getResourceStopUseRemindThreshold())
                .resourceProhibitiveBuyRemindThreshold(updateChannelVO.getResourceProhibitiveBuyRemindThreshold())
                .resourceRunoutofBalanceRemindThreshold(updateChannelVO.getResourceRunoutofBalanceRemindThreshold())
                .resourceChannelType(updateChannelVO.getResourceChannelType())
                .a2zAccountingPeriodId(updateChannelVO.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.A2Z.getType()) ?
                        getAccountingPeriodId(updateChannelVO.getA2zAccountingPeriodId()) : null)
                .distributionAccountingPeriodId(updateChannelVO.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.CONSIGNMENT.getType()) ?
                        getAccountingPeriodId(updateChannelVO.getDistributionAccountingPeriodId()) : null)
                .marketingAmount(updateChannelVO.getMarketingAmount())
                .creditAmount(updateChannelVO.getCreditAmount())
                .a2zMarketingAmount(updateChannelVO.getA2zMarketingAmount())
                .build();
        if (ChannelDistributorDetail.AllowNewChannelPackage.YES.getValue().equals(updateChannelVO.getAllowNewPackage())) {
            detail.setLimitPackageNum(updateChannelVO.getLimitPacakageNum());
//            detail.setGroupId(updateChannelVO.getGroupId());

            channelUpcctemplateRelationAuthMapper.delete(Wrappers.lambdaQuery(ChannelUpcctemplateRelationAuth.class)
                    .eq(ChannelUpcctemplateRelationAuth::getCorpId, updateChannelVO.getCorpId()));

            List<ChannelUpcctemplateRelationAuth> upcctemplateRelationAuths = updateChannelVO.getUpccTemplateIds().stream()
                    .map(m -> new ChannelUpcctemplateRelationAuth().setTemplateId(m).setCorpId(updateChannelVO.getCorpId()))
                    .collect(Collectors.toList());
            channelUpcctemplateRelationAuthMapper.batchInsert(upcctemplateRelationAuths);
        }
        return detail;
    }

    private <T> boolean compare(Object var1, Object var2, Class<T> type) {
        if (BigDecimal.class.getName().equals(type.getName())) {
            BigDecimal tmpVar1 = (BigDecimal) var1;
            BigDecimal tmpVar2 = (BigDecimal) var2;
            if (var1 == null) {
                tmpVar1 = new BigDecimal(0);
            }
            if (var2 == null) {
                tmpVar2 = new BigDecimal(0);
            }

            return tmpVar1.compareTo(tmpVar2) == 0;
        } else if (Integer.class.getName().equals(type.getName())) {
            Integer tmpVar1 = (Integer) var1;
            Integer tmpVar2 = (Integer) var2;
            if (var1 == null) {
                tmpVar1 = 0;
            }
            if (var2 == null) {
                tmpVar2 = 0;
            }

            return tmpVar1.compareTo(tmpVar2) == 0;
        } else {
            throw new RuntimeException("8支持的类型");
        }
    }

    @Override
    public IPage<CmsCorpFlowdetail> getCorpFlowDetail(int pageNum, int pageSize, String country, String corpId, String beginDate, String endDate) {
        String ebscode = Optional.ofNullable(channelMapper.selectById(corpId).getEbsCode())
                .orElseThrow(() -> new BizException("没有查询到渠道商ebscode"));
        IPage<CmsCorpFlowdetail> page = new Page<>(pageNum, pageSize);
        IPage<CmsCorpFlowdetail> records = corpFlowdetailMapper.queryDetail(page, ebscode, country, beginDate, endDate);
        for (CmsCorpFlowdetail record : records.getRecords()) {
            BigDecimal amount = record.getAmount();
            record.setAmount(amount.divide(BigDecimal.valueOf(100), 2, RoundingMode.UP));
        }

        return records;
    }

    @Override
    public ExportVO corpFlowDetailExport(String country, String corpId, String beginDate, String endDate) {
        Long taskId = null;

        Channel channel = Optional.ofNullable(channelMapper.selectOne(Wrappers.lambdaQuery(Channel.class)
                        .select(Channel::getCorpName)
                        .eq(Channel::getCorpId, corpId)))
                .orElseThrow(() -> new BizException("channel not exist"));

        String taskName = channel.getCorpName() + "_flow_details";
        String fileName = taskName + ".csv";
        File dir = new File(channelFlowDetailPath);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        try {
            String filePath = channelFlowDetailPath + File.separator + fileName;
            taskId = Response.getAndCheckRemoteData(backFeignClient.addTask(BatchSyncfileTack.builder()
                    .taskStatus(BatchSyncfileTack.TaskStatus.PROCESSING.getStatus())
                    .taskDesc(taskName)
                    .filePath(filePath)
                    .corpId(corpId)
                    .fileName(fileName)
                    .build()));

            CorpFlowDetailExportTask corpFlowDetailExportTask = new CorpFlowDetailExportTask(backFeignClient);

            corpFlowDetailExportTask.doTask(country, corpId, beginDate, endDate, filePath, taskId);

        } catch (Exception e) {
            if (taskId != null) {
                backFeignClient.updateTaskStatus(taskId, BatchSyncfileTack.TaskStatus.FAIL.getStatus(), null);
            }
            log.error("出错原因为：", e);
        }
        return ExportVO.builder().taskId(taskId).taskName(fileName).build();

    }

    @Override
    public List<ExportVO> channelAtzFlowExport(String corpId, String beginDate, String endDate, String type, String billType, String userId, Long id) {
        Long taskId = null;
        ChannelCloseAccounts channelCloseAccounts = Response.getAndCheckRemoteData(statFeignClient.getCloseAccounts(id));

        //判断是否封板
        if (ObjectUtil.isNotNull(channelCloseAccounts) && StrUtil.isNotBlank(channelCloseAccounts.getFlowDetailPath())) {
            List<ExportVO> exportVOS = getExportVOS(userId, channelCloseAccounts.getFlowDetailPath(), id);
            return exportVOS;
        }

        Channel channel = Optional.ofNullable(channelMapper.selectOne(Wrappers.lambdaQuery(Channel.class)
                        .eq(Channel::getCorpId, corpId)))
                .orElseThrow(() -> new BizException("channel not exist"));

        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");

        String nameDate;

        try {
            Date date = inputFormat.parse(beginDate);
            nameDate = outputFormat.format(inputFormat.parse(endDate));
        } catch (Exception e) {
            throw new BizException("时间格式转换失败");
        }

        LocalDate currentDate = LocalDate.parse(nameDate);

        String abbreviatedMonthName = currentDate.getMonth().getDisplayName(TextStyle.SHORT, Locale.ENGLISH);

        String taskName = "CMLink_Global_Data_SIM_Monthly_Settlement_Report_" + channel.getCorpName().replaceAll(",", "").replaceAll("，", "") + "_detail_" + abbreviatedMonthName + currentDate.getYear();

        Integer size;

        if (A2ZModel.equals(type)) {
            size = corpFlowdetailMapper.getChannelAtzFlowCount(channel.getEbsCode(), beginDate, endDate);
        } else if (resourceModel.equals(type)) {
            size = resourceflowdetailMapper.getChannelResourceFlowCount(channel.getEbsCode(), beginDate, endDate);
        } else {
            size = corpFlowdetailMapper.getChannelAtzFlowCount(channel.getEbsCode(), beginDate, endDate);
            size += resourceflowdetailMapper.getChannelResourceFlowCount(channel.getEbsCode(), beginDate, endDate);
        }


        List<ExportVO> exportVOS = getExportTaskMessage.getXlsExportVOS(userId, size, taskName, channelFlowDetailPath, taskName, id);

        channelAtzFlowExportTask.detailTask(channel.getEbsCode(), beginDate, endDate, channelFlowDetailPath, taskId, type, exportVOS, channelCloseAccounts, id);

        return exportVOS;
    }

    private List<ExportVO> getExportVOS(String userId, String filePath, Long id) {
        List<String> filePaths = Arrays.stream(filePath.split(","))
                .map(String::trim)
                .collect(Collectors.toList());
        List<ExportVO> exportVOS = getExportTaskMessage.getExportVOS(userId, filePaths);
        exportVOS.forEach(exportVO -> exportAsyncTask.exportFileAsync(exportVO, exportVO.getOldFilePath()));
        return exportVOS;
    }

    private static String getModel(String type) {
        String model = "";

        switch (type) {
            case "1":
                model = "_A~Z";
                break;
            case "2":
                model = "_Resource";
                break;
        }
        return model;
    }

    private static String getSuffix(String billType) {
        String suffix = "";

        switch (billType) {
            case "0":
                suffix = "Distribution_";
                break;
            case "1":
                suffix = "Distribution_";
                break;
            case "2":
                suffix = "A~Z_";
                break;
            case "3":
                suffix = "A~Z_";
                break;
            case "4":
                suffix = "合并_";
                break;
            case "5":
                suffix = "A~Z_";
                break;
            case "6":
                suffix = "合并_";
                break;
            case "7":
                suffix = "合并_";
                break;
        }
        return suffix;
    }

    @Override
    public ExportVO channelAtzSummaryExport(String corpId, String beginDate, String endDate, String type, String billType, String userId, Long id) {
        Long taskId = null;

        ChannelCloseAccounts channelCloseAccounts = Response.getAndCheckRemoteData(statFeignClient.getCloseAccounts(id));

        //判断是否封板
        if (ObjectUtil.isNotNull(channelCloseAccounts) && StrUtil.isNotBlank(channelCloseAccounts.getFlowSummaryPath())) {
            List<ExportVO> exportVOS = getExportVOS(userId, channelCloseAccounts.getFlowSummaryPath(), id);
            return exportVOS.get(0);
        }

        Channel channel = Optional.ofNullable(channelMapper.selectOne(Wrappers.lambdaQuery(Channel.class)
                        .eq(Channel::getCorpId, corpId)))
                .orElseThrow(() -> new BizException("channel not exist"));

        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");

        String nameDate;

        try {
            Date date = inputFormat.parse(beginDate);
            nameDate = outputFormat.format(inputFormat.parse(endDate));
        } catch (Exception e) {
            throw new BizException("时间格式转换失败");
        }

        LocalDate currentDate = LocalDate.parse(nameDate);

        String abbreviatedMonthName = currentDate.getMonth().getDisplayName(TextStyle.SHORT, Locale.ENGLISH);

        String taskName = "CMLink_Global_Data_SIM_Monthly_Settlement_Report_" + channel.getCorpName().replaceAll(",", "").replaceAll("，", "") + "_Summary_" + abbreviatedMonthName + currentDate.getYear();
        String fileName = taskName + ".xlsx";
        File dir = new File(channelFlowDetailPath + File.separator + id);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        try {
            String filePath = channelFlowDetailPath + File.separator + id + File.separator + fileName;
            taskId = Response.getAndCheckRemoteData(backFeignClient.addTask(BatchSyncfileTack.builder()
                    .taskStatus(BatchSyncfileTack.TaskStatus.PROCESSING.getStatus())
                    .taskDesc(taskName)
                    .filePath(filePath)
                    .corpId(userId)
                    .fileName(fileName)
                    .build()));

            channelAtzFlowExportTask.summaryTask(channel, beginDate, endDate, filePath, taskId, type, id, channelCloseAccounts);

        } catch (Exception e) {
            if (taskId != null) {
                backFeignClient.updateTaskStatus(taskId, BatchSyncfileTack.TaskStatus.FAIL.getStatus(), null);
            }
            log.error("出错原因为：", e);
        }
        return ExportVO.builder().taskId(taskId).taskName(fileName).build();
    }

    @Override
    public BlankcardOrderConfirmVO getInfo4Order2(String corpId, String cooperationMode) {
        BlankcardOrderConfirmVO res = new BlankcardOrderConfirmVO();
        res.setFreeImsiList(pmsFeignClient.getImsiAmountName(
                channelImsiAmountRelationMapper.selectList(Wrappers.lambdaQuery(CmsChannelImsiAmountRelation.class)
                                .eq(CmsChannelImsiAmountRelation::getCorpId, corpId)
                                .eq(CmsChannelImsiAmountRelation::getCooperationMode, cooperationMode))
                        .stream()
                        .map(CmsChannelImsiAmountRelation::getRuleId)
                        .collect(Collectors.toList())).get());
        List<CmsChannelResourceruleRelation> cmsChannelResourceruleRelations = channelResourceruleRelationMapper.selectList(Wrappers.lambdaQuery(CmsChannelResourceruleRelation.class)
                .eq(CmsChannelResourceruleRelation::getCorpId, corpId));
        List<AtzCharging> atzChargings = jmsFeignClient.getA2zRuleNameByChannel(cmsChannelResourceruleRelations.stream().map(CmsChannelResourceruleRelation::getRuleId).collect(Collectors.toList())).get();
        res.setAtzChargings(atzChargings);
        return res;
    }

    @Override
    public Map<String, String> getChannelDetails(Set<String> corpIds) {
        List<ChannelDistributorDetail> details = channelDistributorDetailMapper.selectList(Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                .select(ChannelDistributorDetail::getCorpId, ChannelDistributorDetail::getAllowNewPackage)
                .in(ChannelDistributorDetail::getCorpId, corpIds));


        Map<String, String> map = details.stream()
                .collect(Collectors.toMap(ChannelDistributorDetail::getCorpId, ChannelDistributorDetail::getAllowNewPackage));

        return map;
    }

    @Override
    public List<String> getEmailList() {
        return backFeignClient.emailByFunctionStatus(Arrays.asList("2", "3")).getData();
    }

    @Override
    public List<String> listBySalesMail(String salesMail) {
        List<ChannelDistributorDetail> list = channelMapper.getChannelCardBySalesMail(salesMail);
        return list.stream()
                .map(ChannelDistributorDetail::getSalesMail)
                .collect(Collectors.toList());
    }

    @Override
    public List<Channel> getChannelByEmail(String userName) {

        List<String> eMails = Response.getAndCheckRemoteData(backFeignClient.getUserEmail(userName));

        List<Channel> channels = channelMapper.getChannelByEmail(eMails);

        return channels;
    }

    @Override
    public List<ExportVO> channelExport(ChannelExportDTO dto) {
        List<ExportVO> exportVOS = new ArrayList<>();
        boolean a2zExport = false;
        boolean sellExport = false;


        switch (dto.getBillType()) {
            case "1":
                sellExport = true;
                break;
            case "2":
                a2zExport = true;
                break;
            case "3":
                a2zExport = true;
                break;
            case "4":
                a2zExport = true;
                sellExport = true;
                break;
            case "5":
                a2zExport = true;
                break;
            case "6":
                a2zExport = true;
                sellExport = true;
                break;
            case "7":
                a2zExport = true;
                sellExport = true;
                break;
        }

        //导出Imsi费
        ExportImsiCostDTO exportImsiCostDTO = new ExportImsiCostDTO();
        BeanUtils.copyProperties(dto, exportImsiCostDTO);
        exportImsiCostDTO.setUserId(dto.getUserId());
//        exportVOS.add(channelSelfService.exportImsiCost(exportImsiCostDTO));

        //渠道商收入汇总文件导出
        ChannelIncomeExportVO incomeExportVO = new ChannelIncomeExportVO();
        BeanUtils.copyProperties(dto, incomeExportVO);
        incomeExportVO.setUserId(dto.getUserId());
        incomeExportVO.setId(dto.getId());
        exportVOS.addAll(Response.getAndCheckRemoteData(statFeignClient.createBillTotalTask(incomeExportVO)));

        if (a2zExport) {
            //渠道ATZ/资源合作流量明细导出
            exportVOS.addAll(channelAtzFlowExport(dto.getCorpId(), dto.getDateStart(), dto.getDateEnd(), dto.getType(), dto.getBillType(), dto.getUserId(), dto.getId()));
            //渠道ATZ/资源合作流量汇总导出
            exportVOS.add(channelAtzSummaryExport(dto.getCorpId(), dto.getDateStart(), dto.getDateEnd(), dto.getType(), dto.getBillType(), dto.getUserId(), dto.getId()));
        }

        if (sellExport) {
            //渠道商收入详情文件导出
            exportVOS.addAll(Response.getAndCheckRemoteData(statFeignClient.createBillDetailTask(incomeExportVO)));
        }

        return exportVOS;
    }

    @Override
    public List<StatCorpFlowdetail> channelAtzSummary(String corpId, String beginDate, String endDate) {
        HashMap<String, StatCorpFlowdetail> map = getFlowdetailHashMap(corpId, beginDate, endDate, "2");
        return new ArrayList<>(map.values());
    }

    @Override
    public List<ChannelAccountPeriodDTO> getAccountPeriodId(List<String> corpIdList) {
        corpIdList = channelMapper.selectList(Wrappers.lambdaQuery(Channel.class)
                .eq(Channel::getInternalOrder, "1")
                .in(Channel::getCorpId, corpIdList)).stream().map(Channel::getCorpId).collect(Collectors.toList());
        return channelDistributorDetailMapper.selectA2zAccountId(corpIdList);
    }

    @Override
    public List<ConsignmentDetailsDto> getConsignmentDetails(String corpId) {
        SelectMarketingCampaignDTO selectMarketingCampaignDTO = new SelectMarketingCampaignDTO();

        selectMarketingCampaignDTO.setCorpIds(Collections.singletonList(corpId));

        List<MktCampaignCorpDTO> mktCampaignCorpDTOS = mktFeignClient.getMarketingCampaignCorp(selectMarketingCampaignDTO).ignoreThrow();

        List<Long> mcIds = mktCampaignCorpDTOS.stream().map(MktCampaignCorpDTO::getMcId).collect(Collectors.toList());

        selectMarketingCampaignDTO.setMarketingCampaignIds(mcIds);
        selectMarketingCampaignDTO.setCorpIds(null);
        List<MarketingCampaignDTO> marketingCampaignDTOS = mktFeignClient.getMarketingCampaign(selectMarketingCampaignDTO).ignoreThrow();

        Map<Long, MarketingCampaignDTO> marketingCampaignDTOMap = marketingCampaignDTOS.stream().collect(Collectors.toMap(MarketingCampaignDTO::getId, m -> m));


        List<CmsChannelMarketingRebate> cmsChannelMarketingRebates = cmsChannelMarketingRebateMapper.selectList(new QueryWrapper<CmsChannelMarketingRebate>().lambda()
                .eq(CmsChannelMarketingRebate::getCorpId, corpId)
                .eq(CmsChannelMarketingRebate::getType, mcIds));


        ArrayList<ConsignmentDetailsDto> consignmentDetailsDtos = new ArrayList<>();
        cmsChannelMarketingRebates.forEach(cmsChannelMarketingRebate -> {
            MarketingCampaignDTO marketingCampaignDTO = marketingCampaignDTOMap.get(cmsChannelMarketingRebate.getActivityId());
            ConsignmentDetailsDto consignmentDetailsDto = new ConsignmentDetailsDto();
            consignmentDetailsDto.setCampaignStatus(marketingCampaignDTO.getCampaignStatus());
            consignmentDetailsDto.setCampaignName(marketingCampaignDTO.getCampaignName());
            consignmentDetailsDto.setCreateTime(cmsChannelMarketingRebate.getCreateTime());

            consignmentDetailsDto.setExpireTime(cmsChannelMarketingRebate.getExpiryTime());
            consignmentDetailsDto.setStartTime(marketingCampaignDTO.getStartTime());
            consignmentDetailsDto.setEndTime(marketingCampaignDTO.getEndTime());
            consignmentDetailsDto.setRebateAmount(cmsChannelMarketingRebate.getRebateAmount());
            consignmentDetailsDto.setTotalAmount(cmsChannelMarketingRebate.getRemainAmount());
            consignmentDetailsDto.setUsedAmount(cmsChannelMarketingRebate.getRemainAmount().subtract(cmsChannelMarketingRebate.getRebateAmount()));
        });
        return consignmentDetailsDtos;
    }

    private HashMap<String, StatCorpFlowdetail> getFlowdetailHashMap(String corpId, String beginDate, String endDate, String cooperationMode) {
        Channel channel = channelMapper.selectOne(Wrappers.lambdaQuery(Channel.class)
                .eq(Channel::getCorpId, corpId));
        int pageNum = 1;
        HashMap<String, StatCorpFlowdetail> map = new HashMap<>();
        List<StatCorpFlowdetail> channelSummary2;
        while (true) {
            Page<StatCorpFlowdetail> page = new Page<>(pageNum, batchSize);
            page.setSearchCount(false);
            if ("2".equals(cooperationMode)) {
                channelSummary2 = corpFlowdetailMapper.getChannelSummary3(channel.getEbsCode(), beginDate, endDate, page);
            } else {
                channelSummary2 = resourceflowdetailMapper.getChannelSummary3(channel.getEbsCode(), beginDate, endDate, page);
            }
            for (StatCorpFlowdetail statCorpFlowdetail : channelSummary2) {
                if (statCorpFlowdetail.getRebateAmount().compareTo(BigDecimal.ZERO) != 0) {
                    log.debug("此条话单消耗营销款不为0，判断是否进行话单拆分。营销款： {}", statCorpFlowdetail.getRebateAmount());
                    BigDecimal rebateRate = new BigDecimal(statCorpFlowdetail.getRebateRate()).divide(new BigDecimal(100), 3, RoundingMode.UP);
                    BigDecimal rate = new BigDecimal(1).subtract(rebateRate);
                    BigDecimal discountPrice = statCorpFlowdetail.getUnitPrice().multiply(rate);
                    if (statCorpFlowdetail.getOldAmount().multiply(rebateRate).compareTo(statCorpFlowdetail.getRebateAmount()) != 0) {
                        log.info("此条话单消耗营销款比例不对，准备进行话单拆分。 {}", statCorpFlowdetail);
                        BigDecimal discountUsage = statCorpFlowdetail.getRebateAmount().divide(statCorpFlowdetail.getUnitPrice().subtract(discountPrice), 3, RoundingMode.UP);
                        StatCorpFlowdetail discountCorpFlowdetail = new StatCorpFlowdetail();
                        BeanUtils.copyProperties(statCorpFlowdetail, discountCorpFlowdetail);
                        discountCorpFlowdetail.setUsedTraffic(discountUsage);
                        discountCorpFlowdetail.setUnitPrice(discountPrice);
                        log.info("拆分后的折扣话单：{}", discountCorpFlowdetail);
                        groupByMccPriceAndoperator(map, discountCorpFlowdetail);
                        statCorpFlowdetail.setUsedTraffic(statCorpFlowdetail.getUsedTraffic().subtract(discountUsage));
                    } else {
                        statCorpFlowdetail.setUnitPrice(discountPrice);
                    }
                }
                groupByMccPriceAndoperator(map, statCorpFlowdetail);
            }
            if (channelSummary2.size() < batchSize) {
                break;
            }
            pageNum++;
        }
        for (StatCorpFlowdetail channelSummaryExport : map.values()) {
            channelSummaryExport.setUsedTraffic(channelSummaryExport.getUsedTraffic().setScale(3, RoundingMode.CEILING));
            channelSummaryExport.setAmount((channelSummaryExport.getUnitPrice().multiply(channelSummaryExport.getUsedTraffic())).setScale(3, RoundingMode.CEILING));
        }
        return map;
    }

    private void groupByMccPriceAndoperator(HashMap<String, StatCorpFlowdetail> map, StatCorpFlowdetail statCorpFlowdetail) {
        BigDecimal unitPrice = statCorpFlowdetail.getUnitPrice();
        String key = statCorpFlowdetail.getMcc() + "-" + unitPrice + "-" + statCorpFlowdetail.getOperator();
        if (map.containsKey(key)) {
            StatCorpFlowdetail corpFlowdetail = map.get(key);
            corpFlowdetail.setUsedTraffic(corpFlowdetail.getUsedTraffic().add(statCorpFlowdetail.getUsedTraffic()));
            corpFlowdetail.setOldAmount(corpFlowdetail.getOldAmount().add(statCorpFlowdetail.getOldAmount()));
        } else {
            map.put(key, statCorpFlowdetail);
        }
    }

    @Override
    public List<StatCorpFlowdetail> channelResourceSummary(String corpId, String beginDate, String endDate) {
        HashMap<String, StatCorpFlowdetail> map = getFlowdetailHashMap(corpId, beginDate, endDate, "3");
        return new ArrayList<>(map.values());
    }

    @Override
    public List<ChannelSellDataDTO> getChannelSellData(String userId, List<String> corpId, int pageNum, int pageSize, String batchId) {
        List<String> salesMails = backFeignClient.getMailByUserId(userId).get()
                .stream().map(User::getEmail).collect(Collectors.toList());
        Assert.notEmpty(salesMails, "user not exist");

        List<ChannelDistributorDetail> channels = channelDistributorDetailMapper.selectList(
                Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                        .in(ChannelDistributorDetail::getSalesMail, salesMails)
                        .in(corpId != null && !corpId.isEmpty(), ChannelDistributorDetail::getCorpId, corpId)
                        .orderByDesc(BaseEntity::getCreateTime));
        List<ChannelSellDataDTO> list = new ArrayList<>();
        for (ChannelDistributorDetail channel : channels) {
            ChannelSellsVO vo = ChannelSellsVO.builder()
                    .corpId(channel.getCorpId())
                    .channelDistributorDetail(channel)
                    .build();
            if (pageNum == -1) {
                vo.setUnpaid(true);
                vo.setBatchId(batchId);
            }
            ChannelSellsDTO channelSells = statFeignClient.getChannelSells(vo);
            if (channelSells == null) {
                continue;
            }

            Channel channelbase = channelMapper.selectById(channel.getCorpId());
            for (String mode : channel.getChannelCooperationMode().split(",")) {
                ChannelSellDataDTO dto = ChannelSellDataDTO.builder()
                        .account(channel.getSalesMail())
                        .cooperationMode(mode)
                        .currencyCode(channel.getCurrencyCode())
                        .corpName(channelbase.getCorpName())
                        .build();
                if ("1".equals(mode)) {
                    dto.setCompletedAmount(new BigDecimal(channelSells.getCompletedAmountConsignment()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
                    dto.setContractSellAmount(channel.getDepositAmount() == null ?
                            "0.00" : channel.getDepositAmount().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
                    dto.setArrears(new BigDecimal(channelSells.getArrearsConsignment()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
                    dto.setCurrentPeriodBill(new BigDecimal(channelSells.getCurrentPeriodBillConsignment()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
                } else if ("2".equals(mode)) {
                    dto.setCompletedAmount(new BigDecimal(channelSells.getCompletedAmountA2z()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
                    dto.setContractSellAmount(channel.getA2zDepositAmount() == null ?
                            "0.00" : channel.getA2zDepositAmount().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
                    dto.setArrears(new BigDecimal(channelSells.getArrearsA2z()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
                    dto.setCurrentPeriodBill(new BigDecimal(channelSells.getCurrentPeriodBillA2z()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
                } else if ("3".equals(mode)) {
                    dto.setArrears(new BigDecimal(channelSells.getArrearsResource()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
                    dto.setCompletedAmount("0.00");
                    dto.setContractSellAmount("0.00");
                    dto.setCurrentPeriodBill(new BigDecimal(channelSells.getCurrentPeriodBillResource()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());
                }
                list.add(dto);
            }
        }

        return list;
    }

    @Override
    public IPage<ChannelSellDataDTO> getChannelSellDataForPage(ChannelSellVO channelSellVO) {
        String userId = channelSellVO.getUserId();
        List<String> corpId = channelSellVO.getCorpId();
        int pageNum = channelSellVO.getPageNum();
        int pageSize = channelSellVO.getPageSize();

        List<ChannelSellDataDTO> channelSellData = getChannelSellData(userId, corpId, pageNum, pageSize, "");
        // 计算总记录数
        int total = channelSellData.size();

        // 分页逻辑
        int fromIndex = (pageNum - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, total);
        List<ChannelSellDataDTO> currentPageData = channelSellData.subList(fromIndex, toIndex);

        // 创建并设置IPage对象
        IPage<ChannelSellDataDTO> page = new Page<>(pageNum, pageSize);
        page.setTotal(total); // 设置总记录数
        page.setRecords(currentPageData); // 设置当前页的数据

        // 注意：通常不需要手动设置pages（总页数），因为IPage实现会根据total和pageSize自动计算
        // 但如果你需要，可以添加以下逻辑来计算总页数（可选）
        int totalPages = (total + pageSize - 1) / pageSize;
        page.setPages(totalPages); // 设置总页数（可选）

        return page;
    }

    @Override
    public TodoChannelSellDataDTO getTodoChannelSellData(String userId, String todoNodeId, String procUniqueId, String batchId) {
        TodoChannelSellDataDTO res = new TodoChannelSellDataDTO();
//        res.setPushTodo("2".equals(backFeignClient.getTodoStatus(todoNodeId).get()));
        res.setProcUniqueId(procUniqueId);
        res.setTodoNodeId(todoNodeId);

        List<ChannelSellDataDTO> channelSellData = getChannelSellData(userId, null, -1, -1, batchId);
        res.setList(channelSellData);

        return res;
    }

    @Override
    public void finishTodoChannelSellData(String todoNodeId, String procUniqueId) {
        SubmitProcessReq submitProcessReq = new SubmitProcessReq();
        submitProcessReq.setType("2");
        submitProcessReq.setProcId("6");
        submitProcessReq.setTodoNodeId("1");
        submitProcessReq.setProcUniqueId(procUniqueId);
//        submitProcessReq.setTodoNodeId(todoNodeId);

        backFeignClient.processSubmission(submitProcessReq);
    }

    @Override
    public List<String> getCorpIdByName(String corpName) {
        return channelMapper.getCorpIdByName(corpName);
    }

    @Component
    @RequiredArgsConstructor
    private static class CorpFlowDetailExportTask {

        private final BackFeignClient backFeignClient;

        @Async
        private void doTask(String country, String corpId, String beginDate, String endDate, String filePath, Long taskId) {
            try (FileOutputStream os = new FileOutputStream(new File(filePath))) {

                final ChannelService channelService = SpringContextHolder.getBean(ChannelService.class);
                List<CmsCorpFlowdetail> corpFlowdetails = channelService.getCorpFlowDetail(-1, -1, country, corpId, beginDate, endDate).getRecords();

                // 设置表头
                String titles = "Date,Country or region,Usage (MB),Amount";

                // 设置每列字段
                String keys = "date,countryOrRegion,usedTraffic,amount";

                // 构造导出数据
                List<Map<String, Object>> datas = new ArrayList<>();

                Map<String, Object> map;

                if (corpFlowdetails != null) {
                    for (CmsCorpFlowdetail data : corpFlowdetails) {
                        map = new HashMap<>();
                        map.put("date", DateUtil.format(data.getDate(), "yyyy-MM-dd"));
                        map.put("countryOrRegion", data.getCountryOrRegion());
                        map.put("usedTraffic", String.valueOf(data.getUsedTraffic()));
                        map.put("amount", String.valueOf(data.getAmount()));
                        datas.add(map);
                    }
                }

                CsvExportUtil.doExport(datas, titles, keys, os);
                backFeignClient.updateTaskStatus(taskId, BatchSyncfileTack.TaskStatus.FINISH.getStatus(), null);
            } catch (Exception e) {
                log.error("渠道商流量详情导出失败：", e);
                backFeignClient.updateTaskStatus(taskId, BatchSyncfileTack.TaskStatus.FAIL.getStatus(), null);
            }
        }
    }

    @Override
    public IPage<Resourceflowdetail> getResourceFlowDetail(int pageNum, int pageSize, String country, String corpId,
                                                           String beginDate, String endDate, String month) {
        String ebscode = Optional.ofNullable(channelMapper.selectById(corpId).getEbsCode())
                .orElseThrow(() -> new BizException("没有查询到渠道商ebscode"));
        IPage<Resourceflowdetail> page = new Page<>(pageNum, pageSize);
        IPage<Resourceflowdetail> records = resourceflowdetailMapper.queryDetail(page, ebscode, country, beginDate, endDate, month);

        if (records.getRecords().size() == 0) {
            return page;
        }

        final Map<String, List<OmsCountry>> map = omsFeignClient.countryListByMcc(records.getRecords().stream().map(Resourceflowdetail::getMcc).collect(Collectors.toList())).get()
                .stream().collect(Collectors.groupingBy(OmsCountry::getMcc));

        for (Resourceflowdetail record : records.getRecords()) {
            BigDecimal amount = record.getAmount();
            record.setAmount(amount.divide(BigDecimal.valueOf(100), 2, RoundingMode.UP));
            record.setMccEn(map.getOrDefault(record.getMcc(), Collections.singletonList(new OmsCountry())).get(0).getCountryEn());
            record.setCountryOrRegion(map.getOrDefault(record.getMcc(), Collections.singletonList(new OmsCountry())).get(0).getCountryCn());
            if (StrUtil.isNotBlank(month)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
                record.setDateStr(sdf.format(record.getDate()));
            } else {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                record.setDateStr(sdf.format(record.getDate()));
            }
        }

        return records;
    }

    @Override
    public ExportVO exportResourceFlowDetail(String country, Boolean en, String corpId, String userId,
                                             String beginDate, String endDate, String month) {
        Long taskId = null;
        Channel channel = Optional.ofNullable(channelMapper.selectById(corpId))
                .orElseThrow(() -> new BizException("channel not exist"));

        DateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
        Calendar calendar = Calendar.getInstance();
        String dateName = df.format(calendar.getTime());
        String taskName = en ? channel.getCorpName() + " Invoice Statistics " + dateName : channel.getCorpName() + " 账单统计 " + dateName;
        String fileName = taskName + ".csv";
        File dir = new File(iotConfig.getExport().getResourceExportPath());
        if (!dir.exists()) {
            dir.mkdirs();
        }

        try {
            String filePath = iotConfig.getExport().getResourceExportPath() + File.separator + fileName;
            taskId = Response.getAndCheckRemoteData(backFeignClient.addTask(BatchSyncfileTack.builder()
                    .taskStatus(BatchSyncfileTack.TaskStatus.PROCESSING.getStatus())
                    .taskDesc(taskName)
                    .filePath(filePath)
                    .corpId(userId)
                    .fileName(fileName)
                    .build()));

            exportResourceFlowTask.doTask(country, en, corpId, beginDate, endDate, month, filePath, taskId);
        } catch (Exception e) {
            if (taskId != null) {
                backFeignClient.updateTaskStatus(taskId, BatchSyncfileTack.TaskStatus.FAIL.getStatus(), null);
            }
            log.error("", e);
        }
        return ExportVO.builder().taskId(taskId).taskName(fileName).build();
    }

    @Override
    public void a2zOwnCharging(List<ChannelFlowDetailDTO> data, Boolean isSupplement) throws ParseException {
        Map<String, Map<A2ZDeposit, BigDecimal>> record = new HashMap<>();
        Map<String, ChannelDistributorDetail> channelInfo = new HashMap<>();
        Set<String> nullEbsCode = new HashSet<>();
        Iterator<ChannelFlowDetailDTO> iterator = data.iterator();
        while (iterator.hasNext()) {
            ChannelFlowDetailDTO line = iterator.next();

            line.setUsage(String.valueOf(line.getUsedTraffic()));
            line.setOperator(line.getOperatorName());
            line.setCountryOrRegion(line.getCountryEn());

            if (!isSupplement && line.haveEmpty()) {
                iterator.remove();
                continue;
            }

            if (!getCorpConsumption(line, record, channelInfo, nullEbsCode)) {
                iterator.remove();
            }
        }
        if (data.isEmpty()) {
            log.info("数据为空");
            return;
        }

        writeInfo(data, record, channelInfo, data.get(0).getDateBelongTo(), isSupplement, false, false);

        notifyIfNecessary(record, channelInfo);

        if (!nullEbsCode.isEmpty()) {
            log.error("####################存在非数据库中渠道商的话单， ebsCode: {}", nullEbsCode);
        }
    }

    @Override
    public Boolean isCharging(IsChargingForm form) {
        if ("1".equals(form.getType())) {
            return corpFlowdetailMapper.selectCount(Wrappers.<CmsCorpFlowdetail>lambdaQuery()
                    .eq(CmsCorpFlowdetail::getEbsCode, form.getEbsCode())
                    .eq(CmsCorpFlowdetail::getDateBelongTo, form.getDate())) > 0;
        } else {
            return assignedImsiRecordMapper.selectCount(Wrappers.<CmsAssignedImsiRecord>lambdaQuery()
                    .eq(CmsAssignedImsiRecord::getCorpId, form.getCorpId())
                    .eq(CmsAssignedImsiRecord::getDateBelongTo, form.getDate())) > 0;
        }
    }

    @Override
    public List<CmsCorpFlowdetail> getSupplementCard(IsChargingForm form) {
        //id,date_belong_to,iccid,plmnList，mcc，curreny_code
        if ("1".equals(form.getType())) {
            return corpFlowdetailMapper.selectList(Wrappers.<CmsCorpFlowdetail>lambdaQuery()
                    .eq(CmsCorpFlowdetail::getEbsCode, form.getEbsCode())
                    .eq(CmsCorpFlowdetail::getUnitPrice, 0)
                    .eq(CmsCorpFlowdetail::getAmount, 0)
                    .eq(CmsCorpFlowdetail::getDateBelongTo, form.getDate()));
        } else {
            // TODO: 2024/8/16 预留 gtp proxy
            return new ArrayList<>();
        }
    }

    @Override
    public List<ChannelA2zruleRelationVo> corpChargingRule(Long ruleId, String corpId) {
        return channelA2zruleRelationMapper.corpChargingRule(Wrappers.<CmsChannelA2zruleRelation>lambdaQuery()
                .eq(!ObjectUtils.isEmpty(ruleId), CmsChannelA2zruleRelation::getRuleId, ruleId)
                .eq(StringUtils.hasLength(corpId), CmsChannelA2zruleRelation::getCorpId, corpId));
    }

    @Override
    public List<ChannelA2zruleRelationVo> resCorpChargingRule(Long ruleId, String corpId) {
        return channelA2zruleRelationMapper.resCorpChargingRule(Wrappers.<CmsChannelA2zruleRelation>lambdaQuery()
                .eq(!ObjectUtils.isEmpty(ruleId), CmsChannelA2zruleRelation::getRuleId, ruleId)
                .eq(StringUtils.hasLength(corpId), CmsChannelA2zruleRelation::getCorpId, corpId));
    }

    @Override
    public void recordchannelflowDetailV2(String date, Boolean isGtp) {
        RecordChannelFlowContext context = new RecordChannelFlowContext();
        context.setGtp(isGtp);
        context.setMarketingRateMapper(marketingRateMapper);
        context.setMarketingRebateMapper(marketingRebateMapper);
        context.setChannelMapper(channelMapper);

        date = StringUtils.isEmpty(date) ? DateTimeUtil.getYesterday() : date;
        context.setDate(date);
        File file = getflowDetailFile(date, isGtp, false);
        Boolean postback = postbakIfneed(date, isGtp, context);
        context.setPoskpack(postback);

        try (BufferedReader bomReader = FileUtil.getBOMReader(file)) {
            CsvReadConfig csvReadConfig = CsvReadConfig.defaultConfig();
            csvReadConfig.setContainsHeader(true);
            CsvReader reader = new CsvReader(bomReader, csvReadConfig);
            reader.read(new CsvRowHandler() {
                @Override
                public void handle(CsvRow row) {
                    ChannelFlowDetailDTO dto = row.toBean(ChannelFlowDetailDTO.class);
                    context.setCurrentRecord(dto);
                    //判空
                    if (dto.haveEmpty()) return;
                    //价格*100
                    dto.reSet();
                    //记录话单
                    try {
                        writeInfoV2(dto, context);
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                }
            });
            //写金额备份
            writeAmountBackup(context);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 回滚话单、渠道商a2z已用额度、营销款
     *
     * @param date
     * @param isGtp
     * @return 返回true表示为回滚请求
     */
    private boolean postbakIfneed(String date, Boolean isGtp, RecordChannelFlowContext ctx) {
        Boolean postbak = false;
        if (date.compareTo(iotConfig.getDeleteDate()) >= 0) {
            //删除话单
            if (isGtp) {
                boolean isCompensation1 = true;
                while (isCompensation1) {
                    int successDelSize = resourceflowdetailMapper.delete(Wrappers.lambdaQuery(Resourceflowdetail.class)
                            .eq(Resourceflowdetail::getDateBelongTo, date)
                            .orderByDesc(Resourceflowdetail::getId)
                            .last("limit " + iotConfig.getBatchDeleteSize()));
                    int successDelSize2 = corpFlowdetailMapper.delete(Wrappers.lambdaQuery(CmsCorpFlowdetail.class)
                            .eq(CmsCorpFlowdetail::getDateBelongTo, date)
                            .eq(CmsCorpFlowdetail::getFileType, "2")
                            .orderByDesc(CmsCorpFlowdetail::getId)
                            .last("limit " + iotConfig.getBatchDeleteSize()));
                    if (successDelSize > 0 || successDelSize2 > 0) postbak = true;
                    if ((iotConfig.getBatchDeleteSize() != successDelSize) &&
                            (iotConfig.getBatchDeleteSize() != successDelSize2)) isCompensation1 = false;
                }
            } else {
                boolean isCompensation = true;
                while (isCompensation) {
                    int successDelSize = corpFlowdetailMapper.delete(Wrappers.lambdaQuery(CmsCorpFlowdetail.class)
                            .eq(CmsCorpFlowdetail::getDateBelongTo, date)
                            .eq(CmsCorpFlowdetail::getFileType, "1")
                            .orderByDesc(CmsCorpFlowdetail::getId)
                            .last("limit " + iotConfig.getBatchDeleteSize()));
                    if (successDelSize > 0) postbak = true;
                    if (iotConfig.getBatchDeleteSize() != successDelSize) isCompensation = false;
                }
            }
            if (!postbak) {
                return postbak;
            }

            log.debug("开始退费");
            IotRecord iotRecord = iotRecordMapper.selectOne(Wrappers.lambdaQuery(IotRecord.class).eq(IotRecord::getDate, date));
            if (iotRecord != null) {
                List<IotRecordDetail> iotRecordDetails = iotRecordDetailMapper.selectList(Wrappers.lambdaQuery(IotRecordDetail.class)
                        .eq(IotRecordDetail::getRelationId, iotRecord.getId())
                        .eq(IotRecordDetail::getType, isGtp ? "1" : "2"));
                for (IotRecordDetail e : iotRecordDetails) {
                    //退渠道商钱
                    channelDistributorDetailMapper.bakA2zUsedDeposit(e.getAmount(), e.getCorpId());
                    //退营销活动钱
                    List<CmsIotRebateamountDetail> details = iotRebateamountDetailMapper.selectList(Wrappers.lambdaQuery(CmsIotRebateamountDetail.class)
                            .eq(CmsIotRebateamountDetail::getRelationId, e.getId()));
                    for (CmsIotRebateamountDetail detail : details) {
                        channelMarketingRebateMapper.addRemainAmount(detail.getRebateId(), detail.getRebateAmount());
                        ctx.getMarketingRebateIdList().add(detail.getRebateId());
                        iotRebateamountDetailMapper.deleteById(detail.getId());
                    }
                    //删除流水
                    channelMarketBillflowA2zMapper.delete(Wrappers.lambdaQuery(CmsChannelMarketBillflowA2z.class)
                            .eq(CmsChannelMarketBillflowA2z::getCorpId, e.getCorpId())
                            .eq(CmsChannelMarketBillflowA2z::getRecordType, isGtp ? "2" : "1")
                            .eq(CmsChannelMarketBillflowA2z::getDate, date));
                    iotRecordDetailMapper.delete(Wrappers.lambdaQuery(IotRecordDetail.class)
                            .eq(IotRecordDetail::getRelationId, iotRecord.getId())
                            .eq(IotRecordDetail::getType, isGtp ? "1" : "2"));
                }

                iotRecordMapper.update(null, Wrappers.lambdaUpdate(IotRecord.class)
                        .set(IotRecord::getPostback, "1")
                        .eq(IotRecord::getId, iotRecord.getId()));
            }

            return postbak;
        }

        return postbak;
    }

    private void writeAmountBackup(RecordChannelFlowContext ctx) {
        IotRecord iotRecord = iotRecordMapper.selectOne(Wrappers.lambdaQuery(IotRecord.class)
                .eq(IotRecord::getDate, ctx.getDate()));
        if (iotRecord == null) {
            IotRecord record = IotRecord.builder()
                    .date(ctx.getDate())
                    .build();
            iotRecordMapper.insert(record);
            iotRecord = iotRecordMapper.selectOne(Wrappers.lambdaQuery(IotRecord.class)
                    .eq(IotRecord::getDate, ctx.getDate()));
        }

        Map<String, BigDecimal> map = ctx.getAmount();
        Map<String, Map<Long, BigDecimal>> map1 = ctx.getRebateAmount();
        final Long id = iotRecord.getId();
        map.forEach((corpId, amount) -> {
            iotRecordDetailMapper.insert(IotRecordDetail.builder()
                    .amount(amount)
                    .corpId(corpId)
                    .type(ctx.isGtp() ? "1" : "2")
                    .relationId(id)
                    .build());
            Map<Long, BigDecimal> map2 = map1.get(corpId);
            if (map2 != null) {
                List<IotRecordDetail> iotRecordDetail = iotRecordDetailMapper.selectList(Wrappers.lambdaQuery(IotRecordDetail.class)
                        .eq(IotRecordDetail::getCorpId, corpId)
                        .eq(IotRecordDetail::getRelationId, id)
                        .eq(IotRecordDetail::getType, ctx.isGtp() ? "1" : "2")
                        .orderByAsc(IotRecordDetail::getId));
                map2.forEach((rebateId, rebateAmount) -> {
                    iotRebateamountDetailMapper.insert(
                            CmsIotRebateamountDetail.builder()
                                    .rebateAmount(rebateAmount)
                                    .rebateId(rebateId)
                                    .relationId(iotRecordDetail.get(iotRecordDetail.size() - 1).getId())
                                    .build());
                });
            }
        });

    }


    // 1. 参数控制是否回滚
    // 2. 合并入库
    @Override
    public void recordchannelflowDetail(String date, Boolean isGtp, Boolean postback) throws ParseException {
        boolean compensate = !StringUtils.isEmpty(date);
        date = StringUtils.isEmpty(date) ? DateTimeUtil.getYesterday() : date;
        File file = getflowDetailFile(date, isGtp, postback);
        BufferedReader bomReader = FileUtil.getBOMReader(file);
        List<ChannelFlowDetailDTO> data = CsvUtil.getReader().read(bomReader, ChannelFlowDetailDTO.class);
        if (postback) {
            log.info("执行{}回传话单写入", date);
        }
        log.info("获取文件成功，写入数据库，数量：{} 条", data.size());

        if (date.compareTo(iotConfig.getDeleteDate()) >= 0 && compensate) {
            if (isGtp) {
                boolean isCompensation1 = true;
                while (isCompensation1) {
                    int successDelSize = resourceflowdetailMapper.delete(Wrappers.lambdaQuery(Resourceflowdetail.class)
                            .eq(Resourceflowdetail::getDateBelongTo, date)
                            .orderByDesc(Resourceflowdetail::getId)
                            .last("limit " + iotConfig.getBatchDeleteSize()));

                    if (iotConfig.getBatchDeleteSize() != successDelSize) isCompensation1 = false;
                }
            } else {
                boolean isCompensation = true;
                while (isCompensation) {
                    int successDelSize = corpFlowdetailMapper.delete(Wrappers.lambdaQuery(CmsCorpFlowdetail.class)
                            .eq(CmsCorpFlowdetail::getDateBelongTo, date)
                            .orderByDesc(CmsCorpFlowdetail::getId)
                            .last("limit " + iotConfig.getBatchDeleteSize()));

                    if (iotConfig.getBatchDeleteSize() != successDelSize) isCompensation = false;
                }
            }

            log.debug("开始退费");
            IotRecord iotRecord = iotRecordMapper.selectOne(Wrappers.lambdaQuery(IotRecord.class).eq(IotRecord::getDate, date));
            if (iotRecord != null) {
                List<IotRecordDetail> iotRecordDetails = iotRecordDetailMapper.selectList(Wrappers.lambdaQuery(IotRecordDetail.class)
                        .eq(IotRecordDetail::getRelationId, iotRecord.getId())
                        .eq(IotRecordDetail::getType, isGtp ? "1" : "2"));
                for (IotRecordDetail e : iotRecordDetails) {
                    channelDistributorDetailMapper.bakA2zUsedDeposit(e.getAmount(), e.getCorpId());
                }
                iotRecordDetailMapper.delete(Wrappers.lambdaQuery(IotRecordDetail.class)
                        .eq(IotRecordDetail::getRelationId, iotRecord.getId())
                        .eq(IotRecordDetail::getType, isGtp ? "1" : "2"));
            } else {
                log.error("没有记录的旧话单数据，请手动处理渠道商押金");
            }
        }

        Map<String, Map<A2ZDeposit, BigDecimal>> record = new HashMap<>();
        Map<String, ChannelDistributorDetail> channelInfo = new HashMap<>();
        List<ChannelFlowDetailDTO> buffer = new ArrayList<>(2000);

        Set<String> nullEbsCode = new HashSet<>();
        for (ChannelFlowDetailDTO line : data) {
            if (line.haveEmpty()) continue;

            line.reSet();
            if (!getCorpConsumption(line, record, channelInfo, nullEbsCode)) continue;

            buffer.add(line);
            if (buffer.size() == 2000) {
                writeInfo(buffer, record, channelInfo, date, false, postback, isGtp);
                buffer.clear();
            }
        }

        if (buffer.size() > 0) {
            writeInfo(buffer, record, channelInfo, date, false, postback, isGtp);
        }

        notifyIfNecessary(record, channelInfo);

        if (nullEbsCode.size() > 0) {
            log.error("####################存在非数据库中渠道商的话单，文件名：{}， ebsCode: {}",
                    file.getName(), nullEbsCode);
        }
    }


//    @Override
//    public void recordPostbakcChannelflowDetail() {
//        Set<String> dates = null;
//        try {
//            dates = getPostbackFlowDetailFileDates();
//        } catch (Exception e) {
//            log.error("获取处理文件日期失败", e);
//        }
//
//        if (dates.isEmpty()) {
//            log.info("今日无回传话单处理");
//        } else {
//            dates.forEach(date -> {
//                try {
//                    recordchannelflowDetail(date, true);
//                } catch (ParseException e) {
//                    throw new RuntimeException(e);
//                }
//            });
//        }
//    }

//    public Set<String> getPostbackFlowDetailFileDates() throws SftpException {
//        final IotConfig.Sftp sftp = iotConfig.getSftp();
//
//        Session session = JschUtil.openSession(sftp.getHost(), sftp.getPort(),
//                sftp.getUsername(), sftp.getPassword(), sftp.getSessionTimeout());
//        ChannelSftp channel = (ChannelSftp) JschUtil.openChannel(session, ChannelType.SFTP);
//
//        log.debug("已建立连接");
//        String dirPath = sftp.getRemotePathPostback() + File.separator;
//        Vector<ChannelSftp.LsEntry> ls = channel.ls(dirPath);
//
//        Set<String> doneDate = iotRecordMapper.selectList(Wrappers.lambdaQuery(IotRecord.class)
//                        .select(IotRecord::getDate)
//                        .eq(IotRecord::getPostback, "1"))
//                .stream()
//                .map(IotRecord::getDate)
//                .collect(Collectors.toSet());
//
//        Set<String> dates = ls.stream()
//                .filter(lsEntry -> lsEntry.getFilename().startsWith("billing_result_info"))
//                .map(lsEntry -> lsEntry.getFilename().substring(20, 28))
//                .collect(Collectors.toSet());
//        dates.removeAll(doneDate);
//
//        return dates;
//    }

    private boolean getCorpConsumption(ChannelFlowDetailDTO dto,
                                       Map<String, Map<A2ZDeposit, BigDecimal>> record,
                                       Map<String, ChannelDistributorDetail> channelInfo,
                                       Set<String> nullEbsCode) {
        String ebsCode = dto.getEbsCode();
        if (nullEbsCode.contains(ebsCode)) {
            return false;
        }

        Map<A2ZDeposit, BigDecimal> depositInfo = record.get(ebsCode);
        if (depositInfo == null) {
            Channel channel = channelMapper.selectOne(Wrappers.lambdaQuery(Channel.class).eq(Channel::getEbsCode, ebsCode));
            if (channel == null) {
                nullEbsCode.add(ebsCode);
                return false;
            }

            ChannelDistributorDetail channelDetail = channelDistributorDetailMapper.selectOne(Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                    .eq(ChannelDistributorDetail::getCorpId, channel.getCorpId()));
            channelInfo.put(ebsCode, channelDetail);

            depositInfo = new HashMap<>();
            depositInfo.put(A2ZDeposit.RUNOUTOF_BALANCE_REMIND_THRESHOLD, channelDetail.getRunoutofBalanceRemindThreshold());
            depositInfo.put(A2ZDeposit.PROHIBITIVE_BUY_REMIND_THRESHOLD, channelDetail.getProhibitiveBuyRemindThreshold());
            depositInfo.put(A2ZDeposit.STOP_USE_REMIND_THRESHOLD, channelDetail.getStopUseRemindThreshold());
            depositInfo.put(A2ZDeposit.A2Z_USED_DEPOSIT, channelDetail.getA2zUsedDeposit().add(new BigDecimal(dto.getAmount())));
            depositInfo.put(A2ZDeposit.A2Z_PRE_DEPOSIT, channelDetail.getA2zPreDeposit());
            depositInfo.put(A2ZDeposit.THIS_A2Z_USED_DEPOSIT, new BigDecimal(dto.getAmount()).setScale(6, RoundingMode.HALF_UP));
            record.put(ebsCode, depositInfo);

            return true;
        } else {
            BigDecimal usingDeposit = depositInfo.get(A2ZDeposit.A2Z_USED_DEPOSIT);
            depositInfo.put(A2ZDeposit.A2Z_USED_DEPOSIT, usingDeposit.add(new BigDecimal(dto.getAmount())));

            BigDecimal thisA2zUsedDeposit = depositInfo.get(A2ZDeposit.THIS_A2Z_USED_DEPOSIT);
            depositInfo.put(A2ZDeposit.THIS_A2Z_USED_DEPOSIT, thisA2zUsedDeposit.add(new BigDecimal(dto.getAmount()).setScale(6, RoundingMode.HALF_UP)));

            return true;
        }
    }

    private void getCorpConsumptionV2(RecordChannelFlowContext context) {
        context.getAmount().forEach((corpId, amount) -> {
            ChannelDistributorDetail channelDetail = channelDistributorDetailMapper.selectOne(Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                    .eq(ChannelDistributorDetail::getCorpId, corpId));

            BigDecimal usedDeposit = channelDetail.getA2zUsedDeposit().add(amount);
            BigDecimal preDeposit = channelDetail.getA2zPreDeposit();
            BigDecimal stopUseRemindThreshold = channelDetail.getStopUseRemindThreshold();
            BigDecimal prohibitiveBuyRemindThreshold = channelDetail.getProhibitiveBuyRemindThreshold();
            BigDecimal runoutofBalanceRemindThreshold = channelDetail.getRunoutofBalanceRemindThreshold();

            String runoutofBalanceKey = String.format(RUNOUT_OF_BALANCE, corpId);
            String stopUseKey = String.format(STOP_USE, corpId);
            String prohibitiveBuyKey = String.format(PROHIBITIVE_BUY, corpId);

            //余额用尽 --->禁止购买 ---->停止使用
            try {
                if (usedDeposit.compareTo(stopUseRemindThreshold.add(preDeposit)) > -1) {
                    if (redisUtil.hasKey(stopUseKey)) {
                        return;
                    }
                    log.info("corpId:{},发送停止使用邮件", corpId);
                    emailUtil.sendMailSync(
                            iotConfig.getMailRemind().getStopuse().getTitle(),
                            iotConfig.getMailRemind().getStopuse().getContent(),
                            channelDetail.getEmail());
                    redisUtil.set(stopUseKey, 0L, getExpireTime());
                }
                if (usedDeposit.compareTo(prohibitiveBuyRemindThreshold.add(preDeposit)) > -1) {
                    if (redisUtil.hasKey(prohibitiveBuyKey)) {
                        return;
                    }
                    log.info("corpId:{},发送禁止购买邮件", corpId);
                    emailUtil.sendMailSync(
                            iotConfig.getMailRemind().getProhibitivebuy().getTitle(),
                            iotConfig.getMailRemind().getProhibitivebuy().getContent(),
                            channelDetail.getEmail());
                    redisUtil.set(prohibitiveBuyKey, 0L, getExpireTime());
                }
                if (usedDeposit.compareTo(runoutofBalanceRemindThreshold) > -1) {
                    if (redisUtil.hasKey(runoutofBalanceKey)) {
                        return;
                    }
                    log.info("corpId:{}, 发送余额用尽邮件", corpId);
                    emailUtil.sendMailSync(
                            iotConfig.getMailRemind().getRunoutofbalance().getTitle(),
                            iotConfig.getMailRemind().getRunoutofbalance().getContent(),
                            channelDetail.getEmail());
                    redisUtil.set(runoutofBalanceKey, 0L, getExpireTime());
                }
            } catch (Exception e) {
                log.error("邮件发送异常：", e);
            }
        });
    }

    /**
     * a2z已用信用额度 + amount > 额度用尽提醒阈值
     * a2z已用信用额度 + amount > 禁止购买提醒阈值 + a2z预存款额度
     * a2z已用信用额度 + amount > 停止使用提醒阈值 + a2z预存款额度
     * 发送邮件并设置rediskey，过期时间为当日23：59：59
     */
    private void notifyIfNecessary(Map<String, Map<A2ZDeposit, BigDecimal>> record,
                                   Map<String, ChannelDistributorDetail> channelInfo) {
        Set<String> ebscodes = record.keySet();
        for (String ebscode : ebscodes) {
            Map<A2ZDeposit, BigDecimal> depositInfo = record.get(ebscode);
            BigDecimal usedDeposit = depositInfo.get(A2ZDeposit.A2Z_USED_DEPOSIT);
            BigDecimal preDeposit = depositInfo.get(A2ZDeposit.A2Z_PRE_DEPOSIT);
            BigDecimal stopUseRemindThreshold = depositInfo.get(A2ZDeposit.STOP_USE_REMIND_THRESHOLD);
            BigDecimal prohibitiveBuyRemindThreshold = depositInfo.get(A2ZDeposit.PROHIBITIVE_BUY_REMIND_THRESHOLD);
            BigDecimal runoutofBalanceRemindThreshold = depositInfo.get(A2ZDeposit.RUNOUTOF_BALANCE_REMIND_THRESHOLD);

            String runoutofBalanceKey = String.format(RUNOUT_OF_BALANCE, ebscode);
            String stopUseKey = String.format(STOP_USE, ebscode);
            String prohibitiveBuyKey = String.format(PROHIBITIVE_BUY, ebscode);

            //余额用尽 --->禁止购买 ---->停止使用
            try {
                if (usedDeposit.compareTo(stopUseRemindThreshold.add(preDeposit)) > -1) {
                    if (redisUtil.hasKey(stopUseKey)) {
                        return;
                    }
                    log.info("ebscode:{},发送停止使用邮件", ebscode);
                    emailUtil.sendMailSync(
                            iotConfig.getMailRemind().getStopuse().getTitle(),
                            iotConfig.getMailRemind().getStopuse().getContent(),
                            channelInfo.get(ebscode).getEmail());
                    redisUtil.set(stopUseKey, 0L, getExpireTime());
                } else if (usedDeposit.compareTo(prohibitiveBuyRemindThreshold.add(preDeposit)) > -1) {
                    if (redisUtil.hasKey(prohibitiveBuyKey)) {
                        return;
                    }
                    log.info("ebscode:{},发送禁止购买邮件", ebscode);
                    emailUtil.sendMailSync(
                            iotConfig.getMailRemind().getProhibitivebuy().getTitle(),
                            iotConfig.getMailRemind().getProhibitivebuy().getContent(),
                            channelInfo.get(ebscode).getEmail());
                    redisUtil.set(prohibitiveBuyKey, 0L, getExpireTime());
                } else if (usedDeposit.compareTo(runoutofBalanceRemindThreshold) > -1) {
                    if (redisUtil.hasKey(runoutofBalanceKey)) {
                        return;
                    }
                    log.info("ebscode:{}, 发送余额用尽邮件", ebscode);
                    emailUtil.sendMailSync(
                            iotConfig.getMailRemind().getRunoutofbalance().getTitle(),
                            iotConfig.getMailRemind().getRunoutofbalance().getContent(),
                            channelInfo.get(ebscode).getEmail());
                    redisUtil.set(runoutofBalanceKey, 0L, getExpireTime());
                }
            } catch (Exception e) {
                log.error("邮件发送异常：", e);
            }
        }
    }

    private void writeInfo(List<ChannelFlowDetailDTO> buffer,
                           Map<String, Map<A2ZDeposit, BigDecimal>> record,
                           Map<String, ChannelDistributorDetail> channelInfo,
                           String date, Boolean isSupplement, Boolean postback, Boolean isGtp) throws ParseException {

        Set<String> iccidSet = buffer.stream().map(ChannelFlowDetailDTO::getIccid).collect(Collectors.toSet());
        List<ChannelCard> channelCards = channelCardMapper.selectList(Wrappers.lambdaQuery(ChannelCard.class)
                .select(ChannelCard::getIccid, ChannelCard::getCooperationMode)
                .in(ChannelCard::getIccid, iccidSet));

        List<String> a2zCards = channelCards.stream()
                .filter(e -> ChannelDistributorDetail.CooperationModeEnum.A2Z.getType().equals(e.getCooperationMode()))
                .map(ChannelCard::getIccid)
                .collect(Collectors.toList());
        channelCards = null;

        List<CmsCorpFlowdetail> corpFlow = new ArrayList<>();
        List<Resourceflowdetail> resourceFlow = new ArrayList<>();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        for (ChannelFlowDetailDTO channelFlowDetailDTO : buffer) {
            String ebsCode = channelFlowDetailDTO.getEbsCode();
            if (!isGtp) {
                if ((a2zCards.contains(channelFlowDetailDTO.getIccid()) || "1".equals(channelFlowDetailDTO.getType()))) {
                    CmsCorpFlowdetail cmsCorpFlowdetail = new CmsCorpFlowdetail()
                            .setId(channelFlowDetailDTO.getId())
                            .setEbsCode(channelFlowDetailDTO.getEbsCode())
                            .setDate(format.parse(channelFlowDetailDTO.getDate()))
                            .setIccid(channelFlowDetailDTO.getIccid())
                            .setMcc(channelFlowDetailDTO.getMcc())
                            .setCountryOrRegion(channelFlowDetailDTO.getCountryOrRegion())
                            .setUsedTraffic(new BigDecimal(channelFlowDetailDTO.getUsage()))
                            .setCurrency(channelFlowDetailDTO.getCurrency())
                            .setOperator(channelFlowDetailDTO.getOperator())
                            .setUnitPrice(new BigDecimal(channelFlowDetailDTO.getUnitPrice()))
                            .setAmount(new BigDecimal(channelFlowDetailDTO.getAmount()))
                            .setPlmnlist(channelFlowDetailDTO.getPlmnlist())
                            .setHimsi(channelFlowDetailDTO.getHimsi());

                    corpFlow.add(cmsCorpFlowdetail);
                } else {
                    record.get(ebsCode).put(A2ZDeposit.THIS_A2Z_USED_DEPOSIT,
                            record.get(ebsCode).get(A2ZDeposit.THIS_A2Z_USED_DEPOSIT).subtract(new BigDecimal(channelFlowDetailDTO.getAmount())));
                }

            } else if (isGtp) {
                if (!a2zCards.contains(channelFlowDetailDTO.getIccid())) {
                    Resourceflowdetail resourceflowdetail = new Resourceflowdetail()
                            .setEbsCode(channelFlowDetailDTO.getEbsCode())
                            .setDate(format.parse(channelFlowDetailDTO.getDate()))
                            .setIccid(channelFlowDetailDTO.getIccid())
                            .setImsi(channelFlowDetailDTO.getImsi())
                            .setMcc(channelFlowDetailDTO.getMcc())
                            .setCountryOrRegion(channelFlowDetailDTO.getCountryOrRegion())
                            .setOperator(channelFlowDetailDTO.getOperator())
                            .setUsedTraffic(new BigDecimal(channelFlowDetailDTO.getUsage()))
                            .setCurrency(channelFlowDetailDTO.getCurrency())
                            .setUnitPrice(new BigDecimal(channelFlowDetailDTO.getUnitPrice()))
                            .setAmount(new BigDecimal(channelFlowDetailDTO.getAmount()))
                            .setPlmnlist(channelFlowDetailDTO.getPlmnlist());
                    resourceFlow.add(resourceflowdetail);
                } else {
                    record.get(ebsCode).put(A2ZDeposit.THIS_A2Z_USED_DEPOSIT,
                            record.get(ebsCode).get(A2ZDeposit.THIS_A2Z_USED_DEPOSIT).subtract(new BigDecimal(channelFlowDetailDTO.getAmount())));
                }
            }
        }

        if (!corpFlow.isEmpty()) {
            if (isSupplement) {
                Set<CmsCorpFlowdetail> resultSet = corpFlow.stream()
                        .filter(f -> !ObjectUtils.isEmpty(f.getId()))
                        .map(m -> new CmsCorpFlowdetail()
                                .setId(m.getId())
                                .setAmount(m.getAmount())
                                .setUnitPrice(m.getUnitPrice())
                                .setUpdateTime(new Date()))
                        .collect(Collectors.toSet());
                if (resultSet.isEmpty()) {
                    corpFlowdetailMapper.batchInsert(corpFlow, date);
                } else {
                    corpFlowdetailService.updateBatchById(resultSet);
                }
            } else {
                corpFlowdetailMapper.batchInsert(corpFlow, date);
            }
        }
        if (!resourceFlow.isEmpty()) {
            resourceflowdetailMapper.batchInsert(resourceFlow, date);
        }

        Integer count = iotRecordMapper.selectCount(Wrappers.lambdaQuery(IotRecord.class).eq(IotRecord::getDate, date));
        if (!isSupplement) {
            if (!postback) {
                if (count == 0) {
                    iotRecordMapper.insert(IotRecord.builder().date(date).build());
                }
            } else {
                iotRecordMapper.update(null, Wrappers.lambdaUpdate(IotRecord.class)
                        .set(IotRecord::getPostback, "1")
                        .eq(IotRecord::getDate, date));
            }
        }

        IotRecord iotRecord = iotRecordMapper.selectOne(Wrappers.lambdaQuery(IotRecord.class).eq(IotRecord::getDate, date));

        for (String ebsCode : record.keySet()) {
            BigDecimal amount = record.get(ebsCode).get(A2ZDeposit.THIS_A2Z_USED_DEPOSIT);
            log.info("本次渠道商 {} 累计已用额度：{}", ebsCode, amount);
            channelDistributorDetailMapper.update(null,
                    Wrappers.lambdaUpdate(ChannelDistributorDetail.class)
                            .setSql("a2z_used_deposit = a2z_used_deposit + " + amount)
//                            .set(ChannelDistributorDetail::getA2zUsedDeposit, record.get(ebsCode).get(A2ZDeposit.A2Z_USED_DEPOSIT))
                            .eq(ChannelDistributorDetail::getCorpId, channelInfo.get(ebsCode).getCorpId()));

            if (!isSupplement) {
                iotRecordDetailMapper.insert(IotRecordDetail.builder()
                        .amount(amount)
                        .corpId(channelInfo.get(ebsCode).getCorpId())
                        .type(isGtp ? "1" : "2")
                        .relationId(iotRecord.getId())
                        .build());
            }

            if (isSupplement) {
                channelChargeRecordMapper.insert(ChannelChargeRecord.builder()
                        .corpId(channelInfo.get(ebsCode).getCorpId())
                        .amount(amount)
                        .currencyCode(channelInfo.get(ebsCode).getCurrencyCode())
                        .chargeTime(LocalDateTime.now())
                        .chargeType("13")
                        .ebsCode(ebsCode)
                        .build());
            }
            record.get(ebsCode).put(A2ZDeposit.THIS_A2Z_USED_DEPOSIT, BigDecimal.ZERO);
            log.info("本批渠道商累计已用额度清0：{}", record.get(ebsCode).get(A2ZDeposit.THIS_A2Z_USED_DEPOSIT));
        }
    }

    private void writeInfoV2(ChannelFlowDetailDTO dto,
                             RecordChannelFlowContext context) throws ParseException {

        ChannelCard channelCard = channelCardMapper.selectOne(Wrappers.lambdaQuery(ChannelCard.class)
                .select(ChannelCard::getIccid, ChannelCard::getCooperationMode, ChannelCard::getCorpId)
                .eq(ChannelCard::getIccid, dto.getIccid()));

        String corpId;
        if (ObjectUtils.isEmpty(channelCard)) {
            corpId = context.getCorpId(dto.getEbsCode());
        } else {
            corpId = channelCard.getCorpId();
        }

        if (StrUtil.isEmpty(corpId)) {
            log.debug("垃圾话单");
            return;
        }

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Integer rate = context.getRate(corpId, dto.getMcc());
        BigDecimal totalCost = new BigDecimal(dto.getAmount());
        BigDecimal rebateAmount = totalCost
                .multiply(new BigDecimal(rate))
                .divide(new BigDecimal(100), 4, RoundingMode.HALF_UP);
        BigDecimal a2zDepostCost = totalCost.subtract(rebateAmount);
        context.setRetabateAmount(new BigDecimal(rebateAmount.toString()));

        Long id = null;
        TransactionStatus status = transactionManager.getTransaction(new DefaultTransactionDefinition());
        try {
            if (channelCard != null && ChannelDistributorDetail.CooperationModeEnum.A2Z.getType().equals(channelCard.getCooperationMode())) {
                CmsCorpFlowdetail cmsCorpFlowdetail = new CmsCorpFlowdetail()
                        .setEbsCode(dto.getEbsCode())
                        .setDate(format.parse(dto.getDate()))
                        .setIccid(dto.getIccid())
                        .setMcc(dto.getMcc())
                        .setCountryOrRegion(dto.getCountryOrRegion())
                        .setUsedTraffic(new BigDecimal(dto.getUsage()))
                        .setCurrency(dto.getCurrency())
                        .setOperator(dto.getOperator())
                        .setUnitPrice(new BigDecimal(dto.getUnitPrice()))
                        .setAmount(totalCost)
                        .setPlmnlist(dto.getPlmnlist())
                        .setHimsi(dto.getHimsi())
                        .setFileType(context.isGtp() ? "2" : "1")
                        .setRebateAmount(rebateAmount)
                        .setDateBelongTo(context.getDate())
                        .setRebateRate(rate);
                corpFlowdetailMapper.insert(cmsCorpFlowdetail);
                id = cmsCorpFlowdetail.getId();
            } else if (context.isGtp()) {
                Resourceflowdetail resourceflowdetail = new Resourceflowdetail()
                        .setEbsCode(dto.getEbsCode())
                        .setDate(format.parse(dto.getDate()))
                        .setIccid(dto.getIccid())
                        .setImsi(dto.getImsi())
                        .setMcc(dto.getMcc())
                        .setCountryOrRegion(dto.getCountryOrRegion())
                        .setOperator(dto.getOperator())
                        .setUsedTraffic(new BigDecimal(dto.getUsage()))
                        .setCurrency(dto.getCurrency())
                        .setUnitPrice(new BigDecimal(dto.getUnitPrice()))
                        .setAmount(totalCost)
                        .setPlmnlist(dto.getPlmnlist())
                        .setRebateAmount(rebateAmount)
                        .setDateBelongTo(context.getDate())
                        .setRebateRate(rate);
                resourceflowdetailMapper.insert(resourceflowdetail);
                id = resourceflowdetail.getId();
            }

            if (id == null) {
                transactionManager.commit(status);
                return;
            }
            BigDecimal stillNeedDeducte = useRebateAmount(corpId, rebateAmount, id, context);
            useDeposit(corpId,
                    stillNeedDeducte.compareTo(BigDecimal.ZERO) == 0 ?
                            a2zDepostCost :
                            a2zDepostCost.add(stillNeedDeducte));
            if (stillNeedDeducte.compareTo(BigDecimal.ZERO) > 0) {
                if (context.getRetabateAmount().compareTo(stillNeedDeducte) == 0) {
                    if (channelCard != null && ChannelDistributorDetail.CooperationModeEnum.A2Z.getType().equals(channelCard.getCooperationMode())) {
                        corpFlowdetailMapper.updateById(new CmsCorpFlowdetail()
                                .setId(id)
                                .setRebateAmount(BigDecimal.ZERO));
                    } else {
                        resourceflowdetailMapper.updateById(new Resourceflowdetail()
                                .setId(id)
                                .setRebateAmount(BigDecimal.ZERO));
                    }
                } else {
                    if (channelCard != null && ChannelDistributorDetail.CooperationModeEnum.A2Z.getType().equals(channelCard.getCooperationMode())) {
                        corpFlowdetailMapper.updateById(new CmsCorpFlowdetail()
                                .setId(id)
                                .setRebateAmount(context.getRetabateAmount().subtract(stillNeedDeducte)));
                    } else {
                        resourceflowdetailMapper.updateById(new Resourceflowdetail()
                                .setId(id)
                                .setRebateAmount(context.getRetabateAmount().subtract(stillNeedDeducte)));
                    }
                }
            }
            context.increaseAmount(corpId, a2zDepostCost.add(stillNeedDeducte));
            transactionManager.commit(status);
        } catch (Exception e) {
            log.error("入库失败{}", dto, e);
            transactionManager.rollback(status);
        }
    }

    private BigDecimal useRebateAmount(String corpId, BigDecimal amount, Long id, RecordChannelFlowContext context) {
        if (amount.compareTo(BigDecimal.ZERO) == 0) return BigDecimal.ZERO;
        return deducte(context.getMarketingRebate(corpId).iterator(), amount, id, corpId, context);
    }

    private BigDecimal deducte(Iterator<CmsChannelMarketingRebate> iterator, BigDecimal amount,
                               Long id, String corpId,
                               RecordChannelFlowContext context) {
        if (iterator.hasNext()) {
            CmsChannelMarketingRebate marketingRebate = iterator.next();
            BigDecimal remainAmount = marketingRebateMapper.selectForUpdate(marketingRebate.getId()).getRemainAmount();
            if (remainAmount.compareTo(BigDecimal.ZERO) == 0) {
                log.debug("表id:{}营销款为0, 下一个", marketingRebate.getId());
                return deducte(iterator, amount, id, corpId, context);
            }

            AtomicReference<BigDecimal> totalAmount = new AtomicReference<>(BigDecimal.ZERO);
            List<CmsChannelMarketingRebate> list = new ArrayList<>();
            iterator.forEachRemaining(e -> {
                totalAmount.set(totalAmount.get().add(e.getRemainAmount()));
                list.add(e);
            });

            iterator = list.iterator();
            StringBuilder stringBuilder = new StringBuilder(context.getDate());
            stringBuilder.insert(6, "-");
            stringBuilder.insert(4, "-");

            BigDecimal usedTraffic = new BigDecimal(context.getCurrentRecord().getUsage());
            BigDecimal flow = usedTraffic.multiply(
                    (remainAmount.min(amount)).divide(new BigDecimal(context.getCurrentRecord().getAmount()), RoundingMode.HALF_UP)
            ).multiply(new BigDecimal("1048576"));
            if (remainAmount.compareTo(amount) >= 0) {
                marketingRebateMapper.reduceA2zMarketingRebate(marketingRebate.getId(), amount);

                BigDecimal remain = remainAmount.subtract(amount);
                totalAmount.set(totalAmount.get().add(remain));
                writeBillflowA2z(amount, remain,
                        totalAmount.get(), marketingRebate.getActivityId(),
                        flow, corpId, id,
                        DateUtil.parseDate(stringBuilder.toString()), context);
                log.debug("表id:{}营销款余额充足, 扣款：{}, 抵扣流量:{}byte", marketingRebate.getId(), amount, flow);
                context.increaseRebateAmount(corpId, marketingRebate.getId(), amount);
                return BigDecimal.ZERO;
            } else {
                marketingRebateMapper.reduceA2zMarketingRebate(marketingRebate.getId(), remainAmount);
                writeBillflowA2z(remainAmount, BigDecimal.ZERO,
                        totalAmount.get(), marketingRebate.getActivityId(),
                        flow, corpId, id,
                        DateUtil.parseDate(stringBuilder.toString()), context);
                context.increaseRebateAmount(corpId, marketingRebate.getId(), remainAmount);
                amount = amount.subtract(remainAmount);
                log.debug("表id:{}营销款余额不足, 剩余扣款额:{}, 抵扣流量:{}byte", marketingRebate.getId(), amount, flow);
                amount = deducte(iterator, amount, id, corpId, context);
            }
        }

        return amount;
    }

    private void useDeposit(String corpId, BigDecimal amount) {
        channelDistributorDetailMapper.updateA2zUsedDeposit1(amount, corpId);
    }

    private void writeBillflowA2z(BigDecimal amount, BigDecimal deposit,
                                  BigDecimal totalAmount, Long activityId,
                                  BigDecimal usedFlow, String corpId,
                                  Long id, Date approveDate,
                                  RecordChannelFlowContext ctx) {

        String currencyCode;
        switch (ctx.getCurrentRecord().getCurrency().toUpperCase()) {
            case "CNY":
                currencyCode = CurrencyCodeEnum.CNY.getCurrencyCode();
                break;
            case "USD":
                currencyCode = CurrencyCodeEnum.USD.getCurrencyCode();
                break;
            case "HKD":
                currencyCode = CurrencyCodeEnum.HKD.getCurrencyCode();
                break;
            default:
                currencyCode = "unknown";
        }
        channelMarketBillflowA2zMapper.insert(CmsChannelMarketBillflowA2z.builder()
                .corpId(corpId)
                .orderId(id.toString())
                .type("2")
                .currencyCode(currencyCode)
                .amount(amount)
                .deposit(deposit)
                .totalAmount(totalAmount)
                .activityId(activityId)
                .usedFlow(usedFlow)
                .approveDate(approveDate)
                .date(ctx.getDate())
                .mcc(ctx.getCurrentRecord().getMcc())
                .recordType(ctx.isGtp() ? "2" : "1")
                .build());
    }


    private long getExpireTime() {
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.HOUR_OF_DAY, 23);
        instance.set(Calendar.MINUTE, 59);
        instance.set(Calendar.MILLISECOND, 59);
        return instance.getTimeInMillis() - System.currentTimeMillis();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDeposit(List<ChargeVO> chargeVOS) {
        for (ChargeVO chargeVO : chargeVOS) {
            LocalDateTime now = LocalDateTime.now();
            java.util.Date date = java.sql.Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
            BigDecimal accountAdjustment = chargeVO.getAmount();
            String corpId = chargeVO.getCorpId();
            String chargeType = chargeVO.getType();
            ChannelDistributorDetail channel = Optional.ofNullable(channelDistributorDetailMapper.selectOne(Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                    .eq(ChannelDistributorDetail::getCorpId, corpId))).orElseThrow(() -> new BizException("渠道商不存在"));
            if ("9".equals(chargeVO.getType())) {
                chargeType = "5";
                channelDistributorDetailMapper.updateDeposit(accountAdjustment, channel.getId());
                channelBillRecord(BillRecordVo.builder()
                        .amount(accountAdjustment)
                        .orderSubOrUnsubDate(date)
                        .billType("9")
                        .corpId(corpId)
                        .build());
            } else {
                channelDistributorDetailMapper.updateA2zUsedDeposit(accountAdjustment, channel.getId());
            }
            accountAdjustment = accountAdjustment.negate();
            channelChargeRecord(ChannelChargeRecord.builder()
                    .chargeType(chargeType)
                    .chargeTime(now)
                    .corpId(corpId)
                    .currencyCode(channel.getCurrencyCode())
                    .amount(accountAdjustment)
                    .build());
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void indemnityUpdateDeposit(ChargeVO chargeVO) {
        LocalDateTime now = LocalDateTime.now();
        java.util.Date date = java.sql.Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
        BigDecimal accountAdjustment = chargeVO.getAmount();
        String corpId = chargeVO.getCorpId();
        ChannelDistributorDetail channel = Optional.ofNullable(channelDistributorDetailMapper.selectOne(Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                .eq(ChannelDistributorDetail::getCorpId, corpId))).orElseThrow(() -> new BizException("渠道商不存在"));
        if ("14".equals(chargeVO.getType())) {
            channelDistributorDetailMapper.updateDeposit(accountAdjustment, channel.getId());

            channelBillRecord(BillRecordVo.builder()
                    .amount(accountAdjustment.negate())
                    .orderSubOrUnsubDate(date)
                    .billType("12")
                    .corpId(corpId)
                    .build());
        } else {
            channelDistributorDetailMapper.updateA2zUsedDeposit(accountAdjustment, channel.getId());
        }
        channelChargeRecord(ChannelChargeRecord.builder()
                .chargeType(chargeVO.getType())
                .chargeTime(now)
                .corpId(corpId)
                .currencyCode(channel.getCurrencyCode())
                .amount(accountAdjustment.negate())
                .build());
    }

    public File getflowDetailFile(String date, boolean isGtp, boolean postback) {
        final IotConfig.Sftp sftp = iotConfig.getSftp();

        Session session = JschUtil.openSession(sftp.getHost(), sftp.getPort(),
                sftp.getUsername(), sftp.getPassword(), sftp.getSessionTimeout());
        ChannelSftp channel = (ChannelSftp) JschUtil.openChannel(session, ChannelType.SFTP);

        String fileName = isGtp ? IOT_FILE_GTP.replace("#", date) : IOT_FILE_A2Z.replace("#", date);
        String filePath = (isGtp ? sftp.getRemotePathExtra() : sftp.getRemotePath()) + File.separator + fileName;
        File file = new File(sftp.getLocalstoragePath() + File.separator + fileName);
        log.info("已建立连接，开始获取文件：{}", fileName);
        try (InputStream fileInputStream = channel.get(filePath)) {
            FileUtil.writeFromStream(fileInputStream, file);
        } catch (Exception e) {
            log.error(" 获取物联网平台文件失败", e);
            throw new BizException("获取物联网平台文件失败");
        } finally {
            session.disconnect();
            channel.disconnect();
        }

        return file;
    }

    @Override
    public List<Channel> getChannelList() {
        return channelMapper.selectList(Wrappers.lambdaQuery(Channel.class)
                .select(Channel::getCorpId, Channel::getCorpName, Channel::getCompanyName)
                .eq(Channel::getType, Channel.ChannelTypeEnum.CHANNEL.getValue()));
    }

    @Override
    public List<ChannelUpcctemplateRelation> getChannelUpcctemplateRelation(String templateId) {
        return channelUpcctemplateRelationMapper.getBindInfo(templateId);
    }

    @Override
    public List<String> getChannelCooperationMode(String corpId) {
        if (getIsSubChannel(corpId)) {
            return Collections.singletonList(ChannelDistributorDetail.CooperationModeEnum.CONSIGNMENT.getType());
        }
        ChannelDistributorDetail detail = channelDistributorDetailMapper
                .selectOne(Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                        .select(ChannelDistributorDetail::getChannelCooperationMode)
                        .eq(ChannelDistributorDetail::getCorpId, corpId));

        List<String> cooperationModes = Arrays.stream(detail.getChannelCooperationMode().split(",")).collect(Collectors.toList());
        return cooperationModes;
    }

    @Override
    public List<Channel> getLowerChannel(String corpId, boolean selfContian) {

        return channelMapper.selectList(Wrappers.lambdaQuery(Channel.class)
                        .eq(StrUtil.isNotEmpty(corpId), Channel::getParentCorpId, corpId)
                        .or(selfContian, e -> e.eq(Channel::getCorpId, corpId)))
                .stream()
                .sorted(Comparator.comparing(Channel::getCreateTime))
                .collect(Collectors.toList());
    }

    @Override
    public String getSubChannelCorpId(String corpId, String cooperationMode) {
        if (ChannelDistributorDetail.CooperationModeEnum.CONSIGNMENT.getType().equals(cooperationMode)) {
            List<String> corpIds = getLowerChannel(corpId, true)
                    .stream()
                    .map(Channel::getCorpId)
                    .collect(Collectors.toList());
            corpId = String.join("','", corpIds);
        }

        return "('" + corpId + "')";
    }

    @Override
    public Boolean getIsSubChannel(String corpId) {
        Channel channel = channelMapper.selectOne(Wrappers.lambdaQuery(Channel.class)
                .select(Channel::getCorpId, Channel::getParentCorpId)
                .eq(Channel::getCorpId, corpId));
        return StrUtil.isNotBlank(channel.getParentCorpId());
    }


    @Override
    @Transactional
    public void addSubOne(SubChannelVO vo) {
        // 参数校验
        subCnlParamsCheck(vo, "add");
        // 文件数据读取
        List<SubCnlPackageFileVO> fileInfo = getSubCnlPackageFileInfo(vo.getFile());
        // 可购买套餐校验
        checkUploadPackage(fileInfo, vo);
        // 子渠道商数据保存
        saveSubChannelInfo(fileInfo, vo);
    }

    @Override
    @Transactional
    public void editSubOne(SubChannelVO vo) {
        // 参数校验
        subCnlParamsCheck(vo, "edit");
        // 文件数据读取
        List<SubCnlPackageFileVO> fileInfo = new ArrayList<>();
        if (vo.getFile() != null) {
            fileInfo = getSubCnlPackageFileInfo(vo.getFile());
            // 可购买套餐校验
            checkUploadPackage(fileInfo, vo);
        }
        // 子渠道商数据更新
        editSubChannelInfo(fileInfo, vo);
    }

    @Override
    public ChannelContext getAllKindsOfChannel(String corpId) {
        ChannelContext channelContext = new ChannelContext();

        final List<String> channelRelationCorpIds = getTopChannelRelationCorpIds(corpId);
        if (channelRelationCorpIds.size() == 0) {
            Channel channel = channelMapper.selectOne(Wrappers.lambdaQuery(Channel.class)
                    .eq(Channel::getCorpId, corpId));

            if (StrUtil.isNotBlank(channel.getParentCorpId())) {
                channelContext.setChannelType(ChannelContext.ChannelType.CHILD_CHANNEL);
                channelContext.setCorpId(channel.getParentCorpId());
                channelContext.setChildCorpIds(Collections.singletonList(corpId));

                ChannelDistributorDetail detail = channelDistributorDetailMapper.selectOne(Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                        .eq(ChannelDistributorDetail::getCorpId, corpId));
                channelContext.setPackageDiscount(detail.getDiscount());
                channelContext.setRefuleDiscount(detail.getRefuelProfitMargin());
                channelContext.setCooperatiomMode(detail.getChannelCooperationMode());

                ChannelDistributorDetail parentDetail = channelDistributorDetailMapper.selectOne(Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                        .eq(ChannelDistributorDetail::getCorpId, channel.getParentCorpId()));
                channelContext.setCurrencyCode(parentDetail.getCurrencyCode());

                final List<CmsChannelPackageDetail> thirdPrice = channelPackageDetailMapper.selectList(Wrappers.lambdaQuery(CmsChannelPackageDetail.class)
                        .eq(CmsChannelPackageDetail::getCorpId, corpId));

                channelContext.setPackagePrice(
                        thirdPrice.size() > 0 ?
                                thirdPrice.stream()
                                        .collect(HashMap::new,
                                                (map, e) -> map.put(e.getPackageId(),
                                                        e.getPackagePrice() == null ? null : e.getPackagePrice().divide(new BigDecimal(100), 6, RoundingMode.DOWN)),
                                                HashMap::putAll) :
                                new HashMap<>()
                );
            } else {
                channelContext.setChannelType(ChannelContext.ChannelType.PARENT_CHANNEL);
                channelContext.setCorpId(corpId);
            }

        } else {
            channelContext.setChannelType(ChannelContext.ChannelType.TOP_CHANNEL);
            channelContext.setRelationCorpIds(channelRelationCorpIds);
        }

        return channelContext;
    }

    @Override
    public void newSelfPackageGroup(NewSelfPackageGroupVO newSelfPackageGroupVO) {
        UpdateChannelVO updateChannelVO = new UpdateChannelVO();
        BeanUtils.copyProperties(newSelfPackageGroupVO, updateChannelVO);
        insertChannelPackageRelation(updateChannelVO, newSelfPackageGroupVO.getCorpId(), true, true);
    }

    @Async
    @Override
    public void writeDetailRecord(String date, String platform) {
        log.info("开始话单入库:{}", platform);

        boolean isCompensation = StrUtil.isNotBlank(date);
        String dayNeedToGet = StrUtil.isBlank(date) ? DateTimeUtil.getYesterday() : date;


        List<CmsChannelCdrConfig> cmsChannelCdrConfigs = cmsChannelCdrConfigMapper.selectList(Wrappers.lambdaQuery(CmsChannelCdrConfig.class));

        Integer defConfig = cmsChannelCdrConfigs.stream()
                .filter(config -> "-9999".equals(config.getCorpId()))
                .findFirst().map(CmsChannelCdrConfig::getAddress)
                .orElse(2);

        Set<String> notDefConfigCorp = cmsChannelCdrConfigs.stream()
                .filter(e -> !defConfig.equals(e.getAddress()))
                .map(CmsChannelCdrConfig::getCorpId).collect(Collectors.toSet());

        while (isCompensation) {
            log.info("补录数据,删除旧数据");
            if (CollectionUtils.isEmpty(notDefConfigCorp) && !CdrConfigEnum.getDesc(String.valueOf(defConfig)).equals(platform)) {
                break;
            }

            int successDelSize = assignedImsiRecordMapper.delete(Wrappers.lambdaQuery(CmsAssignedImsiRecord.class)
                    .eq(CmsAssignedImsiRecord::getDateBelongTo, dayNeedToGet)
                    .notIn(CollectionUtils.isNotEmpty(notDefConfigCorp) && CdrConfigEnum.getDesc(String.valueOf(defConfig)).equals(platform), CmsAssignedImsiRecord::getCorpId, notDefConfigCorp)
                    .in(!CdrConfigEnum.getDesc(String.valueOf(defConfig)).equals(platform), CmsAssignedImsiRecord::getCorpId, notDefConfigCorp)
                    .orderByDesc(CmsAssignedImsiRecord::getId)
                    .last("limit " + recordDetailConfig.getBatchDeleteSize()));

            if (recordDetailConfig.getBatchDeleteSize() != successDelSize) isCompensation = false;
        }


        Map<String, List<File>> recordFiles = getDetailRecordFiles(dayNeedToGet, platform);
        recordFiles.forEach((key, files) -> log.info("Key: {}, Files: {}", key, files.stream()
                .map(File::getName)
                .collect(Collectors.joining(", "))));

        if (CollectionUtils.isEmpty(recordFiles)) {
            log.info("没有需要入库的话单文件");
            return;
        }

        Map<String, Integer> cdrConfig = cmsChannelCdrConfigs.stream()
                .collect(Collectors.toMap(CmsChannelCdrConfig::getCorpId, CmsChannelCdrConfig::getAddress));

        recordFiles.forEach((p, recordFile) -> {
            List<File> decompressPathFile = decompressDetailRecordFile(recordFile, dayNeedToGet);
            log.info("decompressFile: {}", decompressPathFile.stream()
                    .map(File::getName)
                    .collect(Collectors.joining(", ")));
            doWriteDetailRecord(p, decompressPathFile, dayNeedToGet, cdrConfig);
        });


        List<File> iotFiles = generateIotFile(dayNeedToGet, platform, cdrConfig);
        if (StrUtil.isBlank(date)) {
            //补录数据不上传，由运维手动上传
            sendFileToIot(iotFiles);
        }


    }

    private Map<String, List<File>> getDetailRecordFiles(String dayNeedToGet, String platform) {
        File dir = new File(recordDetailConfig.getLocalstoragePath());
        if (!dir.exists() && !dir.mkdirs()) {
            log.error("话单入库创建文件夹失败");
            throw new BizException("话单入库创建文件夹失败");
        }

        Map<String, List<File>> files = new HashMap<>();
        int j = 0;
        String month = dayNeedToGet.substring(0, 6);
        for (RecordDetailConfig.Sftp sftp : recordDetailConfig.getSftp()) {
            if (!sftp.getSftp().getName().equals(platform)) {
                continue;
            }

            log.info("开始连接服务器 {}", sftp.getSftp().getHost());
            final Session session = SftpUtils.getSession(sftp);
            final ChannelSftp channel = SftpUtils.getChannel(session);
            long localSerialNumber = Long.parseLong(dayNeedToGet.concat("00001"));

            String pattern = "cdr-#(\\d{5}).cdr.gz".replace("#", dayNeedToGet);
            List<File> platformFile = new ArrayList<>();
            for (String remotePath : sftp.getRemotePath()) {
                if (remotePath.endsWith(File.separator)) {
                    remotePath += month;
                } else {
                    remotePath = remotePath.concat(File.separator).concat(month);
                }
                Vector<ChannelSftp.LsEntry> ls;
                try {
                    ls = channel.ls(remotePath);
                } catch (Exception e) {
                    log.error("进入目标服务器文件夹 {} 失败: ", remotePath, e);
                    continue;
                }
                for (ChannelSftp.LsEntry f : ls) {
                    if (f.getFilename().matches(pattern)) {
                        File file = new File(recordDetailConfig.getLocalstoragePath().concat(File.separator)
                                .concat(RECORD_DETAIL_FILE.replace("#", localSerialNumber + j + "-" + sftp.getSftp().getName())));
                        String filePath = remotePath.concat(File.separator).concat(f.getFilename());
                        try (InputStream fileInputStream = channel.get(filePath)) {
                            log.info("正在获取文件：{}", filePath);
                            FileUtil.writeFromStream(fileInputStream, file);
                        } catch (Exception e) {
                            log.error("获取平台文件: {} 失败", filePath, e);
                            continue;
                        }

                        platformFile.add(file);
                        j++;
                    }
                }
            }
            if (CollectionUtil.isNotEmpty(platformFile)) {
                files.put(sftp.getSftp().getName(), platformFile);
            }
            session.disconnect();
            channel.disconnect();
        }
        return files;
    }

    private void sendFileToIot(List<File> files) {
        final Session session = SftpUtils.getSession(recordDetailConfig.getUploadPlatformSftp());
        final ChannelSftp channel = SftpUtils.getChannel(session);
        File nowFile = null;
        try {
            for (File file : files) {
                nowFile = file;
                log.debug("正在传输文件：{}", file.getAbsolutePath());
                channel.put(
                        file.getAbsolutePath(),
                        recordDetailConfig.getUploadRemotePath().concat(File.separator).concat(file.getName())
                );
            }
        } catch (Exception e) {
            log.error("{} 文件上传至物联网平台失败：", nowFile.getName(), e);
        }
    }

    private List<File> decompressDetailRecordFile(List<File> files, String dayNeedToGet) {
        File storageDir = new File(recordDetailConfig.getDecompressPath().concat(File.separator).concat("detail").concat(dayNeedToGet));
        if (!storageDir.exists() && !storageDir.mkdirs()) {
            throw new BizException("创建话单存储文件夹失败");
        }

        int i = 1;
        List<File> decompressFiles = new ArrayList<>();
        for (File file : files) {
            // 文本文件输出流
            String fileName = file.getName();
            log.info("解压文件：{}", file.getName());
            File storageFile = new File(storageDir.getAbsolutePath().concat(File.separator)
                    .concat(fileName.substring(0, fileName.lastIndexOf('.')))
                    .concat(String.valueOf(i)));
            try (FileOutputStream fileOutput = new FileOutputStream(storageFile);
                 GZIPInputStream gzFileInput = new GZIPInputStream(Files.newInputStream(file.toPath()))) {

                byte[] buf = new byte[1024];
                int len;
                while ((len = gzFileInput.read(buf)) > 0) {
                    fileOutput.write(buf, 0, len);
                }
            } catch (Exception e) {
                log.error("解压文件 {}失败：", fileName, e);
                throw new BizException("解压文件失败");
            }
            decompressFiles.add(storageFile);
            log.info("解压文件成功：{} ", storageFile.getName());

            i++;
        }

        return decompressFiles;
    }

    private void doWriteDetailRecord(String platform, List<File> decompressPathFile, String dayNeedToGet, Map<String, Integer> cdrConfig) {
        final Map<String, String> plmnlistMap = omsFeignClient.getPlmnlistMccMap().get();

        List<File> splitFile = splitFile(decompressPathFile, recordDetailConfig.getMaxLineNum());

        CompletableFuture[] futures = new CompletableFuture[splitFile.size()];

        for (int i = 0; i < splitFile.size(); i++) {
            File file = splitFile.get(i);
            futures[i] = CompletableFuture.runAsync(() -> {

                log.info("读取文件{}入库", file.getName());
                long startTime = System.currentTimeMillis(), batch = 0;
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
                try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
                    String line;
                    ArrayList<CmsAssignedImsiRecord> list = new ArrayList<>();
                    while ((line = reader.readLine()) != null) {
                        String[] datas = line.split(",");
//                    if (datas.length != 17) {
//                        log.error("{} 舍弃残缺数据：{}", file.getName(), line);
//                        continue;
//                    }

                        CmsAssignedImsiRecord record = null;
                        try {
                            if (Long.parseLong(datas[9]) == 0) {
                                log.debug("{} 舍弃残缺数据：{}", file.getName(), line);
                                continue;
                            }
                            String plmnlist = plmnlistConvert(datas[3]);
                            String mcc = plmnlistMap.getOrDefault(plmnlist, "-1");
                            record = CmsAssignedImsiRecord.builder()
                                    .imsi(datas[2])
                                    .msisdn(datas[11])
                                    .flowCount(Long.parseLong(datas[9]))
                                    .flowUplink(Long.parseLong(datas[7]))
                                    .flowDownlink(Long.parseLong(datas[8]))
                                    .plmnlist("-1".equals(mcc) ? "-1" : plmnlist)
                                    .ratType(datas[13])
                                    .dateBelongTo(dayNeedToGet)
                                    .mcc(mcc)
                                    .startTime(sdf.parse(datas[1]))
                                    .endTime(sdf.parse(datas[0]))
                                    .build();
                            list.add(record);
                        } catch (NullPointerException e) {
                            log.debug("{} 舍弃残缺数据：{}", file.getName(), line);
                            continue;
                        } catch (Exception e) {
                            log.error("{} 系统错误:{}，舍弃：{}", file.getName(), e.getMessage(), line);
                            continue;
                        }

                        if (list.size() == recordDetailConfig.getBatchInsertSize()) {
                            buildAndInsert(platform, list, cdrConfig, file.getName() + "批次" + (++batch));
                            list.clear();
                        }
                    }

                    if (!list.isEmpty()) {
                        buildAndInsert(platform, list, cdrConfig, file.getName() + "批次" + (++batch));
                    }
                } catch (Exception e) {
                    log.error("处理文件[{}]失败：", file.getName(), e);
                }
                log.info("文件{}处理完成,耗时：{}ms", file.getName(), System.currentTimeMillis() - startTime);
            }, recordExecutorService);

            try {
                TimeUnit.SECONDS.sleep(15);
            } catch (Exception ignored) {
            }

        }

        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures);
        try {
            allOf.get(24, TimeUnit.HOURS);
        } catch (Exception e) {
            log.error("等待子线程话单入库失败", e);
            throw new BizException("Waiting for sub-thread to write failed");
        }

    }

    private List<File> generateIotFile(String dayNeedToGet, String platform, Map<String, Integer> cdrConfig) {
        Map<String, List<CmsRecordDetailConfig>> map = recordDetailConfigMapper.selectList(Wrappers.lambdaQuery(CmsRecordDetailConfig.class))
                .stream().collect(Collectors.groupingBy(CmsRecordDetailConfig::getCorpId));

        List<File> files = new ArrayList<>();
        String time = DateTimeUtil.getNowTime().substring(0, 12);

        for (String corpId : map.keySet()) {

            String platformConfig = CdrConfigEnum.getDesc(Optional.ofNullable(cdrConfig.get(corpId)).orElseGet(() -> cdrConfig.get("-9999")).toString());
            if (!platform.equals(platformConfig)) {
                continue;
            }

            try {
                long id = 0;
                int batchSize = recordDetailConfig.getBatchQuerySize();
                boolean running = true;
                int fileSerialNumber = 1;
                long writelineNum = 0;
                List<File> fileNeedPackage = new ArrayList<>();
                StringBuilder builder = new StringBuilder();
                builder.append("himsi,IMSI_with_usage,start_time,data_vol_total_kb,msisdn,mcc,mnc,tapcode,sgsn_address,data_vol_uplink_kb,data_vol_downlink_kb,resource_supplier\n");
                CmsRecordDetailConfig config = map.get(corpId).get(0);

                while (running) {
                    List<CmsAssignedImsiRecord> list = assignedImsiRecordMapper.getlist(corpId, id, dayNeedToGet, batchSize);

                    if (list != null && list.size() > 0) {
                        for (CmsAssignedImsiRecord record : list) {
                            writeIotFile(record, builder);
                            ++writelineNum;

                            if (config.getFileLineLimit() == writelineNum) {
                                fileNeedPackage.add(generateFileAndWriteline(fileSerialNumber, builder, time, config));
                                fileSerialNumber++;
                                writelineNum = 0;
                                builder = new StringBuilder();
                                builder.append("himsi,IMSI_with_usage,start_time,data_vol_total_kb,msisdn,mcc,mnc,tapcode,sgsn_address,data_vol_uplink_kb,data_vol_downlink_kb,resource_supplier\n");

//                            if (fileNeedPackage.size() == recordDetailConfig.getDoPackage()) {
//                                String file2ZipPath = Utils.compressFile2ZipWithPasswd(
//                                        fileNeedPackage.toArray(new File[0]),
//                                        recordDetailConfig.getLocalstoragePath(),
//                                        "CDR_GDS_".concat(config.getPartnerEn()).concat("_").concat(time)
//                                        , config.getZipPass());
//
//                                files.add(new File(file2ZipPath));
//                                fileNeedPackage = new ArrayList<>();
//                            }
                            }
                        }

                        if (list.size() != batchSize) {
                            running = false;
                        }

                        id = list.get(list.size() - 1).getId();
                    } else {
                        running = false;
                    }
                }

                if (writelineNum > 0 || (id == 0 && writelineNum == 0)) {
                    fileNeedPackage.add(generateFileAndWriteline(fileSerialNumber, builder, time, config));
                }

                if (fileNeedPackage.size() > 0) {
                    String file2ZipPath = Utils.compressFile2ZipWithPasswd(
                            fileNeedPackage.toArray(new File[0]),
                            recordDetailConfig.getLocalstoragePath(),
                            config.getPartnerEn().concat("_").concat(time)
                            , config.getZipPass());

                    files.add(new File(file2ZipPath));
                }
            } catch (Exception e) {
                log.error("生成物联网账单失败，渠道商ID：{}", corpId);
            }
        }

        return files;
    }

    private void buildAndInsert(String platform, ArrayList<CmsAssignedImsiRecord> list, Map<String, Integer> cdrConfig, String logInfo) {
        log.info("开始处理{}平台话单,{}: {}条", platform, logInfo, list.size());
        long startTime = System.currentTimeMillis();

        Set<String> imsiRecord = list.stream().map(CmsAssignedImsiRecord::getImsi).collect(Collectors.toSet());
        Map<String, List<RmsAssignedImsi>> supplierInfo = rmsFeignClient.getImsiSupplierInfo(imsiRecord).get();
        log.info("获取{},imsi供应商信息成功[{}],耗时：{}ms", logInfo, imsiRecord.size(), System.currentTimeMillis() - startTime);

        Iterator<CmsAssignedImsiRecord> iterator = list.iterator();
        while (iterator.hasNext()) {
            CmsAssignedImsiRecord e = iterator.next();
            String imsi = e.getImsi();
            List<RmsAssignedImsi> rmsAssignedImsis = supplierInfo.get(imsi);
            if (rmsAssignedImsis == null || rmsAssignedImsis.isEmpty()) {
                iterator.remove();
                continue;
            }
            RmsAssignedImsi rmsAssignedImsi = rmsAssignedImsis.get(0);
            e.setCorpId(rmsAssignedImsi.getCorpId());
            e.setSupplierId(rmsAssignedImsi.getSupplierId().intValue());
            e.setSupplierName(rmsAssignedImsi.getSupplierName());

            Integer config = Optional.ofNullable(cdrConfig.get(e.getCorpId())).orElseGet(() -> cdrConfig.get("-9999"));
            if (!CdrConfigEnum.getDesc(String.valueOf(config)).equals((platform))) {
                iterator.remove();
                log.debug("{}渠道商{}平台账单与配置[{}]不匹配,丢弃", e.getCorpId(), platform, CdrConfigEnum.getDesc(String.valueOf(config)));
            }
        }

        if (!list.isEmpty()) {
            log.info("入库：与账单配置匹配话单: {}条", list.size());
            assignedImsiRecordMapper.batchInsert(list);
        }
        log.info("本批次{}平台话单,{}: {}条,耗时：{}ms", platform, logInfo, list.size(), System.currentTimeMillis() - startTime);
    }

    private static List<File> splitFile(List<File> files, int maxLine) {
        List<File> result = new ArrayList<>();
        for (File originalFile : files) {
            int currentline = 0, partNumber = 0;
            String line, partFileName = originalFile.getParent() + "/part_%s_" + originalFile.getName();
            List<File> partList = new ArrayList<>();

            try (Stream<String> lines = Files.lines(originalFile.toPath());
                 BufferedReader br = new BufferedReader(new InputStreamReader(Files.newInputStream(originalFile.toPath())))) {

                if (lines.count() > maxLine) {
                    BufferedWriter writer = null;
                    while ((line = br.readLine()) != null) {
                        if ((currentline++) % maxLine == 0) {
                            if (currentline != 1) {
                                writer.close();
                            }
                            File partFile = new File(String.format(partFileName, ++partNumber));
                            writer = new BufferedWriter(new OutputStreamWriter(Files.newOutputStream(partFile.toPath())));

                            partList.add(partFile);
                        }
                        writer.write(line);
                        writer.newLine();
                    }
                    if (writer != null) {
                        writer.close();
                    }
                    log.info("文件{}共{}行,已分为{}个文件{}", originalFile.getName(), currentline, partList.size(), partList.stream().map(File::getName).collect(Collectors.toList()));
                } else {
                    log.info("文件{}行数 <= {}行,无需分片", originalFile.getName(), maxLine);
                    result.add(originalFile);
                }
                result.addAll(partList);
            } catch (IOException e) {
                result.add(originalFile);
                log.error("分割文件失败,使用原文件：", e);
            }
        }
        return result;
    }

    private String plmnlistConvert(String plmnlist) {
        String mcc = plmnlist.substring(0, 3);
        String mnc = plmnlist.substring(3);
        if (mcc.equals("313")) {
            mcc = "310";
        }
        if (mcc.equals("405")) {
            mcc = "404";
        }
        return mcc + mnc;
    }

    /**
     * himsi,IMSI_with_usage,start_time,data_vol_total_kb,
     * msisdn,mcc,mnc,tapcode,sgsn_address,
     * data_vol_uplink_kb,data_vol_downlink_kb
     *
     * @param record
     * @param stringBuilder
     * @return
     */
    private StringBuilder writeIotFile(CmsAssignedImsiRecord record, StringBuilder stringBuilder) {
        BigDecimal flowUplink = BigDecimal.valueOf(record.getFlowUplink()).divide(BigDecimal.valueOf(1024)).setScale(6, RoundingMode.HALF_UP);
        BigDecimal flowDownlink = BigDecimal.valueOf(record.getFlowDownlink()).divide(BigDecimal.valueOf(1024)).setScale(6, RoundingMode.HALF_UP);

        String[] line = {
                record.getImsi(),
                record.getImsi(),
                DateTimeUtil.formatTime(record.getEndTime()),
                flowDownlink.add(flowUplink).toString(),
                StrUtil.isBlank(record.getMsisdn()) ? "" : record.getMsisdn(),
                record.getMcc().replace("$", ""),
                "-1".equals(record.getPlmnlist()) ? "-1" : record.getPlmnlist().substring(3),
                "",
                "",
                flowUplink.toString(),
                flowDownlink.toString(),
                record.getSupplierName()
        };

        stringBuilder.append(String.join(",", line)).append("\n");
        return stringBuilder;
    }

    private File generateFileAndWriteline(int serialNumber, StringBuilder builder, String dayNeedToGet, CmsRecordDetailConfig config) {
        String ser = String.valueOf(serialNumber);
        if (serialNumber < 10) {
            ser = "0" + ser;
        }
        File file = new File(recordDetailConfig.getLocalstoragePath().concat(File.separator)
                .concat("GDS_CDR_")
                .concat(config.getPartnerEn())
                .concat("_")
                .concat(dayNeedToGet)
                .concat("_").concat(ser).concat(".csv"));

        log.debug("生成物联网文件: {}", file.getName());
        try (FileOutputStream os = new FileOutputStream(file)) {
            os.write(builder.toString().getBytes());
        } catch (Exception e) {
            log.error("生成物联网文件失败： ", e);
            throw new BizException("生成物联网文件失败");
        }

        return file;
    }

    @Override
    @Transactional
    public void delSubOne(String corpId) {
        Channel channel = channelMapper.selectById(corpId);
        if (channel == null) {
            throw new BizException("Sub-channel does not exist");
        }
        if (StrUtil.isBlank(channel.getParentCorpId())) {
            throw new BizException("Non-subchannels cannot be deleted");
        }
        // 卡检验
        if (channelCardMapper.selectCount(Wrappers.lambdaQuery(ChannelCard.class).eq(ChannelCard::getCorpId, corpId)) > 0) {
            throw new BizException("Unable to delete with existing card information");
        }
        // 信息删除
        // cms_channel
        this.baseMapper.deleteById(corpId);
        // cms_channel_distributors_detail
        channelDistributorDetailMapper.delete(Wrappers.lambdaUpdate(ChannelDistributorDetail.class).eq(ChannelDistributorDetail::getCorpId, corpId));
        // cms_channel_package_detail
        channelPackageDetailMapper.delete(Wrappers.lambdaUpdate(CmsChannelPackageDetail.class).eq(CmsChannelPackageDetail::getCorpId, corpId));
        // 删除账户信息
        backFeignClient.deleteChannelAccount(corpId);
    }

    @Override
    public Response queryPage(SubCnlSearchVO searchVO) {
        IPage<SubCnlDTO> pages = channelMapper.querySubCnlInfoList(new Page<>(searchVO.getPageNum(), searchVO.getPageSize()),
                searchVO.getParentCorpId(), searchVO.getSubChannelName());
        if (CollectionUtils.isEmpty(pages.getRecords())) {
            return Response.ok();
        }
        // 获取权限集合
        List<String> cCorpIdList = pages.getRecords().stream().map(SubCnlDTO::getCCorpId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(cCorpIdList)) {
            Map<String, String> resMap = Response.getAndCheckRemoteData(backFeignClient.getSubCnlCorpRoleIdList(cCorpIdList));
            for (SubCnlDTO dto : pages.getRecords()) {
                dto.setAccountPermissions(resMap.get(dto.getCCorpId()));
            }
        }
        return Response.ok(pages.getRecords(), pages.getTotal());
    }

    /**
     * 子渠道商数据更新
     *
     * @param fileInfo 套餐集合信息
     * @param vo       请求头信息
     */
    private void editSubChannelInfo(List<SubCnlPackageFileVO> fileInfo, SubChannelVO vo) {
        // cms_channel
        this.baseMapper.updateById(Channel.builder()
                .corpId(vo.getCCorpId())
                .corpName(vo.getSubChannelName()).build());
        // cms_channel_distributors_detail
        ChannelDistributorDetail pDetail = channelDistributorDetailMapper.selectOne(Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                .eq(ChannelDistributorDetail::getCorpId, vo.getCCorpId()));
        pDetail.setEmail(vo.getEmail());
        if (pDetail.getTotalDeposit().compareTo(vo.getDeposit()) != 0) {
            BigDecimal desposit = pDetail.getDeposit().add(vo.getDeposit().subtract(pDetail.getTotalDeposit()));
            if (BigDecimal.ZERO.compareTo(desposit) > 0) {
                throw new BizException("Total quota is wrong, the available quota can't be negative");
            }
            pDetail.setDeposit(desposit);
            pDetail.setTotalDeposit(vo.getDeposit());
        }
        pDetail.setDiscount(vo.getProfitMargin());
        pDetail.setRefuelProfitMargin(vo.getRefuelProfitMargin());

        if (CollectionUtils.isEmpty(fileInfo) && vo.getProfitMargin() == null) {
            List<CmsChannelPackageDetail> channelPackageDetail = channelPackageDetailMapper.selectList(Wrappers.lambdaQuery(CmsChannelPackageDetail.class)
                    .eq(CmsChannelPackageDetail::getCorpId, vo.getCCorpId()));
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(channelPackageDetail)) {
                channelPackageDetail.forEach(packageInfo -> {
                    if (packageInfo.getPackagePrice() == null) {
                        throw new BizException("There is no secondary pricing package for the sub-channel, the profit margin cannot be deleted");
                    }
                });
            }
        }

        channelDistributorDetailMapper.update(pDetail, Wrappers.lambdaUpdate(ChannelDistributorDetail.class)
                .set(vo.getProfitMargin() == null, ChannelDistributorDetail::getDiscount, vo.getProfitMargin())
                .eq(ChannelDistributorDetail::getCorpId, pDetail.getCorpId()));
        // cms_channel_package_detail
        if (CollectionUtils.isNotEmpty(fileInfo)) {
            channelPackageDetailMapper.delete(Wrappers.lambdaUpdate(CmsChannelPackageDetail.class)
                    .eq(CmsChannelPackageDetail::getCorpId, vo.getCCorpId()));
            for (SubCnlPackageFileVO info : fileInfo) {
                channelPackageDetailMapper.insert(CmsChannelPackageDetail.builder()
                        .corpId(vo.getCCorpId())
                        .packageId(info.getPackageId())
                        .packagePrice(info.getPrice() != null ? new BigDecimal(info.getPrice()).multiply(BigDecimal.valueOf(100)) : null).build());
            }
        }
        // 修改角色信息
        backFeignClient.updateSubCnlRoleId(vo.getCCorpId(), vo.getEmail(), vo.getAccountPermissions());
    }


    /**
     * 子渠道商数据保存
     *
     * @param fileInfo 套餐集合信息
     * @param vo       请求头信息
     */
    private void saveSubChannelInfo(List<SubCnlPackageFileVO> fileInfo, SubChannelVO vo) {
        // cms_channel
        String corpId = Utils.randomUUID();
        ChannelDistributorDetail pDetail = channelDistributorDetailMapper.selectOne(Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                .eq(ChannelDistributorDetail::getCorpId, vo.getPCorpId()));
        this.baseMapper.insert(Channel.builder()
                .corpId(corpId)
                .corpName(vo.getSubChannelName())
                .status(CorpStatus.COMMON.getStatus())
                .type(Channel.ChannelTypeEnum.SUBCHANNEL.getValue())
                .parentCorpId(vo.getPCorpId())
                .currencyCode(pDetail.getCurrencyCode())
                .checkStatus(CheckStatus.PASS.getStatus()).build());
        // cms_channel_distributors_detail
        channelDistributorDetailMapper.insert(ChannelDistributorDetail.builder()
                .corpId(corpId)
                .appKey(Utils.randomUUID())
                .appSecret(Utils.randomUUID())
                .depositeReset("2")
                .email(vo.getEmail())
                .channelType(pDetail.getChannelType())
                .deposit(vo.getDeposit())
                .totalDeposit(vo.getDeposit())
                .activateNotification(pDetail.getActivateNotification())
                .unsubscribeRule(pDetail.getUnsubscribeRule())
                .channelCooperationMode(ChannelDistributorDetail.CooperationModeEnum.CONSIGNMENT.getType())
                .currencyCode(pDetail.getCurrencyCode())
                .discount(vo.getProfitMargin())
                .refuelProfitMargin(vo.getRefuelProfitMargin())
                .build());
        // cms_channel_package_detail
        for (SubCnlPackageFileVO info : fileInfo) {
            channelPackageDetailMapper.insert(CmsChannelPackageDetail.builder()
                    .corpId(corpId)
                    .packageId(info.getPackageId())
                    .packagePrice(info.getPrice() != null ? new BigDecimal(info.getPrice()).multiply(BigDecimal.valueOf(100)) : null).build());
        }
        // 添加账户信息
        backFeignClient.newAccount4SubCnl(corpId, vo.getEmail(), vo.getSubChannelName(), vo.getAccountPermissions());
    }

    /**
     * 子渠道商参数校验
     *
     * @param vo   请求vo
     * @param type 请求类型
     */
    private void subCnlParamsCheck(SubChannelVO vo, String type) {
        vo.setSubChannelName(vo.getSubChannelName().trim());
        vo.setDeposit(vo.getDeposit().multiply(BigDecimal.valueOf(100)));
        if ("edit".equals(type)) {
            if (StrUtil.isBlank(vo.getCCorpId())) {
                throw new BizException("cCorpId can't be null");
            }
            Channel subChannel = channelMapper.selectById(vo.getCCorpId());
            if (subChannel != null && !subChannel.getCorpName().equals(vo.getSubChannelName()) &&
                    channelMapper.selectCount(Wrappers.lambdaQuery(Channel.class).eq(Channel::getCorpName, vo.getSubChannelName())) > 0) {
                throw new BizException("Sub-channel name can't be duplicated");
            }
        } else {
            // 上传文件校验
            if (vo.getFile() == null || vo.getFile().isEmpty()) {
                throw new BizException("file can't be null");
            }
            // 渠道商名称校验
            if (channelMapper.selectCount(Wrappers.lambdaQuery(Channel.class).eq(Channel::getCorpName, vo.getSubChannelName())) > 0) {
                throw new BizException("Sub-channel name can't be duplicated");
            }
        }
        // 父渠道商0级渠道商校验
        if (topchannelMapper.selectCount(Wrappers.lambdaQuery(CmsTopchannel.class).eq(CmsTopchannel::getCorpId, vo.getPCorpId())) > 0) {
            throw new BizException("Parent-channel can't be level 0 channel");
        }
        // 父渠道商合作模式校验
        ChannelDistributorDetail pDetail = channelDistributorDetailMapper.selectOne(Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                .select(ChannelDistributorDetail::getChannelCooperationMode).eq(ChannelDistributorDetail::getCorpId, vo.getPCorpId()));
        if (pDetail == null) {
            throw new BizException("Parent-channel does not exist");
        }
        if (ChannelDistributorDetail.CooperationModeEnum.A2Z.getType().equals(pDetail.getChannelCooperationMode())) {
            throw new BizException("Unable to create under A~Z model");
        }
    }

    /**
     * 获取文件信息
     *
     * @param file 可购买套餐文件
     * @return 解析数据
     */
    public List<SubCnlPackageFileVO> getSubCnlPackageFileInfo(MultipartFile file) {
        try {
            if (file == null || file.isEmpty()) {
                log.debug("文件不存在，不进行套餐处理");
                return new ArrayList<>();
            }
            // 上传文件校验
            String filename = file.getOriginalFilename();
            if (!StringUtils.hasText(filename)) {
                throw new BizException("文件没有文件名");
            }
            if (!"csv".equals(filename.substring(filename.lastIndexOf(".") + 1))) {
                throw new BizException("文件格式错误");
            }
            // 各行数据处理
            List<SubCnlPackageFileVO> readList = CsvFileUtil.csvFileToBean(file, new String[]{"packageId", "price"}, SubCnlPackageFileVO.class);
            if (CollectionUtils.isEmpty(readList)) {
                throw new BizException("没有读取到正确的数据");
            }
            log.debug("文件中读取到的数据：{}", JSONObject.toJSONString(readList));
            return readList;
        } catch (Exception ex) {
            log.error("上传文件错误", ex);
            throw new BizException("Error uploading file");
        }
    }

    /**
     * 可购买套餐校验
     *
     * @param fileInfo 可购买套餐集合
     */
    public void checkUploadPackage(List<SubCnlPackageFileVO> fileInfo, SubChannelVO vo) {
        if (CollectionUtils.isEmpty(fileInfo)) {
            return;
        }
        String corpId = vo.getPCorpId();
        // 父渠道商套餐组获取
        List<String> groupIdList = channelPackageRelationMapper.selectList(Wrappers.lambdaQuery(ChannelPackageRelation.class)
                .select(ChannelPackageRelation::getGroupId)
                .eq(ChannelPackageRelation::getCooperationMode, ChannelDistributorDetail.CooperationModeEnum.CONSIGNMENT.getType())
                .eq(ChannelPackageRelation::getCorpId, corpId)).stream().map(ChannelPackageRelation::getGroupId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(groupIdList)) {
            log.debug("父渠道商无满足条件的套餐组信息，上传套餐均不符合绑定规则");
            throw new BizException("Uploaded packages don't comply with the rules");
        }
        // 套餐id获取
        List<String> packageIdList = Response.getAndCheckRemoteData(pmsFeignClient.queryPackageId(PackageGroupIdVO.builder()
                .groupIdList(groupIdList).cooperationMode(ChannelDistributorDetail.CooperationModeEnum.CONSIGNMENT.getType()).build()));
        if (CollectionUtils.isEmpty(packageIdList)) {
            log.debug("父渠道商无满足条件的套餐组信息，上传套餐均不符合绑定规则");
            throw new BizException("Uploaded packages don't comply with the rules");
        }
        Iterator<SubCnlPackageFileVO> it = fileInfo.iterator();
        while (it.hasNext()) {
            SubCnlPackageFileVO item = it.next();
            if (StrUtil.isBlank(item.getPrice()) && vo.getProfitMargin() == null) {
                throw new BizException("The profit margin of the sub-channel is empty, the upload package must be secondary priced，packageId:" + item.getPackageId());
            }

            if (!packageIdList.contains(item.getPackageId())) {
                log.warn("上传套餐存在不符合绑定规则套餐，packageId: {}", item.getPackageId());
                it.remove();
                continue;
            }
            if (StrUtil.isNotBlank(item.getPrice())) {
                try {
                    BigDecimal price = new BigDecimal(item.getPrice());
                    if (price.compareTo(BigDecimal.ZERO) < 0) {
                        log.warn("上传套餐价格不能小于0，packageId: {}", item.getPackageId());
                        it.remove();
                    }
                    item.setPrice(String.valueOf(price.setScale(2, RoundingMode.HALF_UP)));
                } catch (NumberFormatException e) {
                    log.warn("上传套餐价格不符合规范，packageId: {}", item.getPackageId());
                    it.remove();
                }
            }
        }
        if (fileInfo.size() == 0) {
            throw new BizException("Uploaded packages don't comply with the rules");
        }
    }

    private ChannelSftp getChannel(SftpConfig config) {
        SftpConfig.Sftp sftp = config.getSftp();

        Session session = JschUtil.openSession(sftp.getHost(), sftp.getPort(), sftp.getUsername(), sftp.getPassword(), sftp.getSessionTimeout());
        return (ChannelSftp) JschUtil.openChannel(session, ChannelType.SFTP);
    }

    @Component
    @RequiredArgsConstructor
    private static class ExportResourceFlowTask {


        private final BackFeignClient backFeignClient;


        @Async
        public void doTask(String country, Boolean en, String corpId, String beginDate, String endDate, String month,
                           String filePath, Long taskId) {
            try (FileOutputStream os = new FileOutputStream(new File(filePath))) {
                final ChannelService channelService = SpringContextHolder.getBean(ChannelService.class);
                List<Resourceflowdetail> resourceflowdetails = channelService.getResourceFlowDetail(-1, -1, country,
                        corpId, beginDate, endDate, month).getRecords();

                // 设置表头
                String titlesCn = "国家/地区,日期/月份,用量(MB),费用";
                String titlesEn = "Country/Region,Date/Month,Usage (MB),Fee";
                String titles = en ? titlesEn : titlesCn;

                // 设置每列字段
                String keys = "countryOrRegion,date,usedTraffic,amount";

                // 构造导出数据
                List<Map<String, Object>> datas = new ArrayList<>();

                Map<String, Object> map;

                if (resourceflowdetails != null) {
                    for (Resourceflowdetail data : resourceflowdetails) {
                        map = new HashMap<>();
                        map.put("countryOrRegion", en ? data.getMccEn() : data.getCountryOrRegion());
                        map.put("date", data.getDateStr());
                        map.put("usedTraffic", data.getUsedTraffic().toString());
                        map.put("amount", data.getAmount().toString());
                        datas.add(map);
                    }
                }


                CsvExportUtil.doExport(datas, titles, keys, os);
                backFeignClient.updateTaskStatus(taskId, BatchSyncfileTack.TaskStatus.FINISH.getStatus(), null);
            } catch (Exception e) {
                log.error("导出资源流量详情失败：", e);
                backFeignClient.updateTaskStatus(taskId, BatchSyncfileTack.TaskStatus.FAIL.getStatus(), null);
            }
        }
    }

    @Override
    public List<ChannelVO> getPeriod() {
        return channelDistributorDetailMapper.getChannelVO(new QueryWrapper<>()).stream()
                .filter(f -> StringUtils.hasLength(f.getA2zAccountingPeriodId())
                        || StringUtils.hasLength(f.getDistributionAccountingPeriodId())
                        || StringUtils.hasLength(f.getResourceAccountingPeriodId()))
                .collect(Collectors.toList());

    }

    public List<ChannelVO> getPeriodAuth() {
        return channelDistributorDetailMapper.getChannelAuthVO(new QueryWrapper<>()).stream()
                .filter(f -> StringUtils.hasLength(f.getA2zAccountingPeriodId())
                        || StringUtils.hasLength(f.getDistributionAccountingPeriodId())
                        || StringUtils.hasLength(f.getResourceAccountingPeriodId()))
                .collect(Collectors.toList());

    }

    @Override
    public ChargingInfo getChargingInfo(ChargingInfoForm form) {
        List<ChannelOrderDetail> channelOrderDetails = new ArrayList<>();

        List<ChannelVO> channelVO = new ArrayList<>();

        if (!form.getPackageUniqueIds().isEmpty()) {
            channelOrderDetails = channelOrderDetailMapper.selectList(Wrappers.<ChannelOrderDetail>lambdaQuery()
                    .select(ChannelOrderDetail::getPackageUniqueId, ChannelOrderDetail::getCooperationMode).in(ChannelOrderDetail::getPackageUniqueId, form.getPackageUniqueIds()));
        }

        if (!form.getCorpIds().isEmpty()) {
            channelVO = channelDistributorDetailMapper.getChannelVO(new QueryWrapper<ChannelVO>().in("d.corp_id", form.getCorpIds()));
        }

        return new ChargingInfo()
                .setCooperationModes(channelOrderDetails.stream()
                        .collect(Collectors.toMap(ChannelOrderDetail::getPackageUniqueId, v -> Optional.ofNullable(v.getCooperationMode()).orElse(""))))
                .setCurrencyCodes(channelVO.stream().collect(Collectors.toMap(ChannelVO::getCorpId, Function.identity())));
    }

    @Override
    public List<CmsAssignedImsiRecord> getAssignedImsiRecord(CmsAssignedImsiRecord form) {
        return assignedImsiRecordMapper.selectList(Wrappers.<CmsAssignedImsiRecord>lambdaQuery()
                .select(CmsAssignedImsiRecord::getId, CmsAssignedImsiRecord::getImsi, CmsAssignedImsiRecord::getMsisdn,
                        CmsAssignedImsiRecord::getCorpId, CmsAssignedImsiRecord::getMcc, CmsAssignedImsiRecord::getPlmnlist,
                        CmsAssignedImsiRecord::getFlowCount, CmsAssignedImsiRecord::getDateBelongTo, CmsAssignedImsiRecord::getCreateTime)
                .eq(CmsAssignedImsiRecord::getDateBelongTo, form.getDateBelongTo())
                .eq(StringUtils.hasLength(form.getCorpId()), CmsAssignedImsiRecord::getCorpId, form.getCorpId())
                .orderByDesc(CmsAssignedImsiRecord::getImsi, CmsAssignedImsiRecord::getId));
    }

    @Override
    public Map<String, List<String>> getCorpNamesByImsiAmountId(Set<String> ids) {
        List<CmsChannelImsiAmountRelation> channelImsiAmountRelations = channelImsiAmountRelationMapper.selectList(Wrappers.lambdaQuery(CmsChannelImsiAmountRelation.class)
                .in(CmsChannelImsiAmountRelation::getRuleId, ids));
        if (channelImsiAmountRelations.isEmpty()) {
            return Collections.emptyMap();
        }

        Set<String> corpIds = channelImsiAmountRelations.stream().map(CmsChannelImsiAmountRelation::getCorpId).collect(Collectors.toSet());
        Map<String, String> corpNameMap = channelMapper.selectList(Wrappers.lambdaQuery(Channel.class)
                        .select(Channel::getCorpId, Channel::getCorpName)
                        .in(Channel::getCorpId, corpIds))
                .stream()
                .collect(Collectors.toMap(Channel::getCorpId, Channel::getCorpName));

        Map<String, List<String>> map = new HashMap<>();
        channelImsiAmountRelations.forEach(e -> {
            List<String> list = map.getOrDefault(e.getRuleId().toString(), new ArrayList<>());

            list.add(corpNameMap.get(e.getCorpId()));

            map.put(e.getRuleId().toString(), list);
        });


        return map;
    }

    @Override
    public void contractExpire() {
        List<Channel> channels = channelMapper.selectList(Wrappers.lambdaQuery(Channel.class)
                .notIn(Channel::getType, Arrays.asList("4", "5", "6", "12")));
        List<String> corpIds = channels.stream().map(Channel::getCorpId).collect(Collectors.toList());
        Map<String, List<Channel>> map = channels.stream().collect(Collectors.groupingBy(Channel::getCorpId));
        List<ChannelDistributorDetail> channelDistributorDetails =
                channelDistributorDetailMapper.selectList(Wrappers.<ChannelDistributorDetail>lambdaQuery()
                        .in(ChannelDistributorDetail::getCorpId, corpIds)
                        .and(wp -> {
                            wp.eq(ChannelDistributorDetail::getIsSub, "1");
                            wp.or();
                            wp.eq(ChannelDistributorDetail::getIsSubA2z, "1");
                        })
                        .orderByDesc(BaseEntity::getCreateTime)
                );

        channelDistributorDetails.forEach(channelDistributorDetail -> {
            Boolean hasConsignment = false;
            Boolean hasA2z = false;
            Boolean freezeConsignment = false;
            Boolean freezeA2z = false;

            if (channelDistributorDetail.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.CONSIGNMENT.getType()) &&
                    channelDistributorDetail.getContractEndTime() != null) {
                hasConsignment = true;
                Calendar calendar1 = Calendar.getInstance();
                calendar1.add(Calendar.DATE, channelContractConfig.getBeforeExpire());
                if (new Date(calendar1.getTime().getTime()).after(channelDistributorDetail.getContractEndTime())) {
                    if ("1".equals(channelDistributorDetail.getContractConsignmentNotify())) {
                        List<String> to = new ArrayList<>(Arrays.asList(channelDistributorDetail.getEmail(),
                                channelDistributorDetail.getSalesMail()));
                        to.addAll(channelContractConfig.getCarbonCopyMail());
                        Object[] array = channelContractConfig.getCarbonCopyMail().toArray();
                        String[] cc = new String[array.length];
                        for (int i = 0; i < cc.length; i++) {
                            cc[i] = (String) array[i];
                        }
                        backFeignClient.sendMailBatch(MailSendParam.builder()
                                .title(channelContractConfig.getMail().getConsignment().getTitle())
                                .content(channelContractConfig.getMail().getConsignment().getContent()
                                        .replace("{#corpName}", map.get(channelDistributorDetail.getCorpId()).get(0).getCorpName())
                                        .replace("{#date}", DateUtil.format(channelDistributorDetail.getContractEndTime(), "yyyy-MM-dd"))
                                )
                                .recipientEmailAddress(to)
                                .cc(cc)
                                .build());
                        channelDistributorDetailMapper.update(null,
                                Wrappers.lambdaUpdate(ChannelDistributorDetail.class)
                                        .eq(ChannelDistributorDetail::getCorpId, channelDistributorDetail.getCorpId())
                                        .set(ChannelDistributorDetail::getContractConsignmentNotify, "2"));
                    }
                }

                Calendar calendar2 = Calendar.getInstance();
                calendar2.add(Calendar.DATE, -channelContractConfig.getAfterExpire());
                if (new Date(calendar2.getTime().getTime()).after(channelDistributorDetail.getContractEndTime())) {
                    freezeConsignment = true;
                    channelDistributorDetailMapper.update(null, Wrappers.lambdaUpdate(ChannelDistributorDetail.class)
                            .set(ChannelDistributorDetail::getIsSub, "2")
                            .eq(ChannelDistributorDetail::getCorpId, channelDistributorDetail.getCorpId()));
                }
            }

            if (channelDistributorDetail.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.A2Z.getType()) &&
                    channelDistributorDetail.getA2zContractEndTime() != null) {
                hasA2z = true;
                Calendar calendar1 = Calendar.getInstance();
                calendar1.add(Calendar.DATE, channelContractConfig.getA2zBeforeExpire());

                if (new Date(calendar1.getTime().getTime()).after(channelDistributorDetail.getA2zContractEndTime())) {
                    if ("1".equals(channelDistributorDetail.getA2zConsignmentNotify())) {
                        List<String> to = new ArrayList<>(Arrays.asList(channelDistributorDetail.getEmail(),
                                channelDistributorDetail.getSalesMail()));
                        to.addAll(channelContractConfig.getCarbonCopyMail());
                        Object[] array = channelContractConfig.getCarbonCopyMail().toArray();
                        String[] cc = new String[array.length];
                        for (int i = 0; i < cc.length; i++) {
                            cc[i] = (String) array[i];
                        }
                        backFeignClient.sendMailBatch(MailSendParam.builder()
                                .title(channelContractConfig.getMail().getA2z().getTitle())
                                .content(channelContractConfig.getMail().getA2z().getContent()
                                        .replace("{#corpName}", map.get(channelDistributorDetail.getCorpId()).get(0).getCorpName())
                                        .replace("{#date}", DateUtil.format(channelDistributorDetail.getA2zContractEndTime(), "yyyy-MM-dd"))
                                )
                                .recipientEmailAddress(to)
                                .cc(cc)
                                .build());

                        channelDistributorDetailMapper.update(null,
                                Wrappers.lambdaUpdate(ChannelDistributorDetail.class)
                                        .eq(ChannelDistributorDetail::getCorpId, channelDistributorDetail.getCorpId())
                                        .set(ChannelDistributorDetail::getA2zConsignmentNotify, "2"));
                    }
                }

                Calendar calendar2 = Calendar.getInstance();
                calendar2.add(Calendar.DATE, -channelContractConfig.getA2zAfterExpire());
                if (new Date(calendar2.getTime().getTime()).after(channelDistributorDetail.getA2zContractEndTime())) {
                    freezeA2z = true;
                    channelDistributorDetailMapper.update(null, Wrappers.lambdaUpdate(ChannelDistributorDetail.class)
                            .set(ChannelDistributorDetail::getIsSubA2z, "2")
                            .eq(ChannelDistributorDetail::getCorpId, channelDistributorDetail.getCorpId()));
                }
            }

            if (hasConsignment && hasA2z) {
                if (freezeConsignment && freezeA2z) {
                    freezeChannelAccount(channelDistributorDetail.getCorpId(), "1");
                } else if (freezeConsignment) {
                    freezeChannelAccount(channelDistributorDetail.getCorpId(), "3");
                }
                return;
            }

            if (hasConsignment && freezeConsignment) {
                freezeChannelAccount(channelDistributorDetail.getCorpId(), "1");
                return;
            }

            if (hasA2z && freezeA2z) {
                freezeChannelAccount(channelDistributorDetail.getCorpId(), "2");
            }
        });
    }

    @Override
    public List<String> getChannelCooperationModeForLogin(String corpId) {
        Channel channel = channelMapper.selectById(corpId);
        if (channel.getType().equals(Channel.ChannelTypeEnum.SUBCHANNEL.getValue())) {
            return Collections.singletonList(ChannelDistributorDetail.CooperationModeEnum.CONSIGNMENT.getType());
        }

        ArrayList<String> cooperationModes = new ArrayList<>();
        ChannelDistributorDetail channelDistributorDetail = channelDistributorDetailMapper.selectOne(Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                .eq(ChannelDistributorDetail::getCorpId, corpId));
        if (channelDistributorDetail.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.CONSIGNMENT.getType())) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE, -channelContractConfig.getAfterExpire());
            if (channelDistributorDetail.getContractEndTime() == null ||
                    !new Date(calendar.getTime().getTime()).after(channelDistributorDetail.getContractEndTime())) {
                cooperationModes.add(ChannelDistributorDetail.CooperationModeEnum.CONSIGNMENT.getType());
            }
        }

        if (channelDistributorDetail.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.A2Z.getType())) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE, -channelContractConfig.getA2zAfterExpire());
            if (channelDistributorDetail.getA2zContractEndTime() == null ||
                    !new Date(calendar.getTime().getTime()).after(channelDistributorDetail.getA2zContractEndTime())) {
                cooperationModes.add(ChannelDistributorDetail.CooperationModeEnum.A2Z.getType());
            }
        }

        if (channelDistributorDetail.getChannelCooperationMode().contains(ChannelDistributorDetail.CooperationModeEnum.RESOURCE_COOPERATION.getType())) {
            cooperationModes.add(ChannelDistributorDetail.CooperationModeEnum.RESOURCE_COOPERATION.getType());
        }
        return cooperationModes;
    }

    @Override
    public BlankcardOrderConfirmVO getInfo4Order(String corpId, String cooperationMode) {
        BlankcardOrderConfirmVO res = new BlankcardOrderConfirmVO();
        res.setFreeImsiList(pmsFeignClient.getImsiAmountName(
                channelImsiAmountRelationMapper.selectList(Wrappers.lambdaQuery(CmsChannelImsiAmountRelation.class)
                                .eq(CmsChannelImsiAmountRelation::getCorpId, corpId)
                                .eq(CmsChannelImsiAmountRelation::getCooperationMode, cooperationMode))
                        .stream()
                        .map(CmsChannelImsiAmountRelation::getRuleId)
                        .collect(Collectors.toList())).get());

        res.setChargings(jmsFeignClient.getChargingByChannel(corpId, "", "", cooperationMode).get().stream()
                .collect(Collectors.toMap(e -> Long.toString(e.getId()), ChannelChargingVO::getName)));

        res.setGroupIds(channelCardService.getChannelCardPoolMcc(corpId));

        return res;
    }

    private List<String> getDiff(List<String> origin, List<String> input) {
        if (input == null) {
            return new ArrayList<>();
        }
        return origin.stream().filter(e -> !input.contains(e)).collect(Collectors.toList());
    }

    @Override
    public IPage<DepositRecordVO> depositRecord(GetDepositVo form, User currentUser) {
        Long userId = Optional.ofNullable(currentUser).map(User::getId).orElse(form.getUserId());
        List<User> users = backFeignClient.getMailByUserId(String.valueOf(userId)).get();

        User user = users.stream()
                .filter(u -> u.getId().equals(userId))
                .findFirst()
                .orElseThrow(() -> new BizException("没有找到用户"));
        log.info("{}[{}] [CorpId:{},FunctionStatus:{}]", user.getUsername(), userId, user.getCorpId(), user.getFunctionStatus());

        List<String> corpIds;
        if (StringUtils.hasLength(user.getCorpId()) && "1".equals(user.getFunctionStatus())) {
            log.info("普通渠道商账户");
            corpIds = Collections.singletonList(user.getCorpId());

        } else if (!StringUtils.hasLength(user.getCorpId())) {

            if ("1".equals(user.getFunctionStatus())) {
                log.info("管理员账户");
                corpIds = new ArrayList<>();
            } else if ("2".equals(user.getFunctionStatus()) || "3".equals(user.getFunctionStatus())) {
                log.info("2".equals(user.getFunctionStatus()) ? "销售账户" : "大区账户");
                Set<String> emails = users.stream().map(User::getEmail).collect(Collectors.toSet());

                corpIds = channelDistributorDetailMapper.selectList(new LambdaQueryWrapper<ChannelDistributorDetail>().in(ChannelDistributorDetail::getSalesMail, emails))
                        .stream().distinct().map(ChannelDistributorDetail::getCorpId).collect(Collectors.toList());
                if (corpIds.isEmpty()) {
                    return new Page<>(form.getPageNum(), form.getPageSize(), 0);
                }

            } else {
                log.error("FunctionStatus枚举值[1,2,3]错误：{} ", user.getFunctionStatus());
                throw new BizException("数据错误");
            }

        } else {
            log.error("不应存在销售账户或大区账户CorpId不为空的数据");
            throw new BizException("数据错误");

        }

        log.info("Username: {} [{}], corpIds:{}", user.getUsername(), userId, corpIds);
        return channelSelfService.depositRecord(new GetDepositVo()
                .setIsDisplay("1")
                .setCorpIds(corpIds)
                .setPageNum(form.getPageNum())
                .setPageSize(form.getPageSize())
                .setCorpName(form.getCorpName()));
    }

    @Override
    public void depositRecordExport(GetDepositVo form, User currentUser, HttpServletResponse response) {
        form.setPageSize(1000);
        ExcelWriter excelWriter = null;
        try {
            String name = "渠道商销售数据_充值记录_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xlsx";
            response.setCharacterEncoding("UTF-8");
            //设置响应类型
            response.setContentType("application/ms-txt.numberformat:@");
            //设置响应头
            response.setHeader("Pragma", "public");

            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(name, "UTF-8"));

            excelWriter = EasyExcel.write(response.getOutputStream(), DepositRecordVO.class).build();

            WriteSheet writeSheet = EasyExcel.writerSheet("渠道商充值记录").build();

            List<DepositRecordVO> data;
            int i = 1;
            do {
                data = depositRecord(form.setPageNum(i++), currentUser).getRecords();
                for (DepositRecordVO item : data) {
                    switch (item.getCurrencyCode()) {
                        case "156":
                            item.setCurrencyCode("人民币");
                            break;
                        case "840":
                            item.setCurrencyCode("美元");
                            break;
                        case "978":
                            item.setCurrencyCode("欧元");
                            break;
                        case "344":
                            item.setCurrencyCode("港币");
                            break;
                        default:
                            item.setCurrencyCode("");
                    }

                    item.setChannelType(StringUtils.hasLength(item.getChannelType()) ? item.getChannelType() : "");
                    switch (item.getChannelType()) {
                        case "1":
                            item.setChannelType("押金");
                            break;
                        case "2":
                            item.setChannelType("预存");
                            break;
                        default:
                            item.setChannelType("");
                    }

                    item.setChargeStatus(StringUtils.hasLength(item.getChargeStatus()) ? item.getChargeStatus() : "");
                    switch (item.getChargeStatus()) {
                        case "1":
                            item.setChargeStatus("未缴费");
                            break;
                        case "2":
                            item.setChargeStatus("发票审批中");
                            break;
                        case "3":
                            item.setChargeStatus("发票审批拒绝");
                            break;
                        case "4":
                            item.setChargeStatus("可缴费");
                            break;
                        case "5":
                            item.setChargeStatus("缴费审批中");
                            break;
                        case "6":
                            item.setChargeStatus("缴费审批拒绝");
                            break;
                        case "7":
                            item.setChargeStatus("已缴费");
                            break;
                        case "8":
                            item.setChargeStatus("已取消");
                            break;
                        case "9":
                            item.setChargeStatus("线上缴费中");
                            break;
                        default:
                            item.setChargeStatus("");
                    }

                }

                excelWriter.write(data, writeSheet);
            } while (!data.isEmpty());


        } catch (Exception ex) {
            log.error("导出失败", ex);
            throw new BizException("导出失败");
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }


    @Override
    public boolean getNotNeedVerifyCodeCropId(String corpId) {
        List<String> ids = notNeedVerifyConfig.getIds();
        if (ids.contains(corpId)) {
            return false;
        }
        Channel channel = channelMapper.selectOne(Wrappers.lambdaQuery(Channel.class)
                .eq(Channel::getCorpId, corpId));
        if (channel == null) {
            return true;
        }

        if (Channel.ChannelTypeEnum.SUBCHANNEL.getValue().equals(channel.getType())) {
            return !ids.contains(channel.getParentCorpId());
        }
        return true;
    }

    @Override
    public List<ChannelDistributorDto> getAccountManagement(String corpId, String cooperationMode) {
        if (StrUtil.isEmpty(corpId) || StrUtil.isEmpty(cooperationMode)) {
            throw new BizException("Partner ID and cooperation model must be transmitted");
        }
        List<ChannelDistributorDetail> channelDistributorDetails = channelDistributorDetailMapper.selectList(
                new QueryWrapper<ChannelDistributorDetail>()
                        .lambda()
                        .eq(ChannelDistributorDetail::getCorpId, corpId)
        );
        if (CollectionUtil.isEmpty(channelDistributorDetails)) {
            return new ArrayList<>();
        }
        //ChannelDistributorDetail表与cmsChannelMarketingRebate表的渠道商类型不一致，需要做状态改变才能查询
        String code = "";
        switch (cooperationMode) {
            case "1":
                code = "2";
                break;
            case "2":
                code = "1";
                break;
            case "3":
                code = "1";
                break;
        }
        BigDecimal marketingAmount = cmsChannelMarketingRebateMapper.getTotalAmount(corpId, code);

        List<ChannelDistributorDto> returnList = new ArrayList<>();
        channelDistributorDetails.forEach(channelDistributorDetail -> {
                    if (channelDistributorDetail.getChannelCooperationMode().contains(cooperationMode)) {
                        ChannelDistributorDto channelDistributorDto = new ChannelDistributorDto();
                        channelDistributorDto.setCurrencyCode(channelDistributorDetail.getCurrencyCode());
                        channelDistributorDto.setCreditAmount(channelDistributorDetail.getCreditAmount().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
                        channelDistributorDto.setMarketingAmount(marketingAmount.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
                        if (cooperationMode.equals(ChannelDistributorDetail.CooperationModeEnum.CONSIGNMENT.getType())) {
                            channelDistributorDto.setTotalAmount(channelDistributorDetail.getTotalDeposit().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
                            BigDecimal balance = channelDistributorDetail.getDeposit().add(channelDistributorDetail.getCreditAmount()).add(channelDistributorDetail.getMarketingAmount());
                            channelDistributorDto.setBalance(balance.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
                            BigDecimal usedAmount = channelDistributorDetail.getTotalDeposit().subtract(channelDistributorDetail.getDeposit()).add(channelDistributorDetail.getMarketingAmount());
                            channelDistributorDto.setUsedAmount(usedAmount.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));

                            if ("1".equals(channelDistributorDetail.getChannelType())) {
                                channelDistributorDto.setDeposit(channelDistributorDetail.getTotalDeposit().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
                            } else if ("2".equals(channelDistributorDetail.getChannelType())) {
                                BigDecimal depositAmount = channelDistributorDetail.getDeposit();
                                //depositAmount字段为空，展示0
                                if (!Objects.isNull(depositAmount)) {
                                    //分转换为元
                                    depositAmount = depositAmount.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                                } else {
                                    depositAmount = new BigDecimal(0);
                                }
                                channelDistributorDto.setDeposit(depositAmount);
                            }
                            channelDistributorDto.setChannelType(channelDistributorDetail.getChannelType());
                        } else if (cooperationMode.equals(ChannelDistributorDetail.CooperationModeEnum.A2Z.getType())) {
                            channelDistributorDto.setChannelType(channelDistributorDetail.getA2zChannelType());
                            channelDistributorDto.setDeposit(channelDistributorDetail.getA2zPreDeposit().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
                        } else if (cooperationMode.equals(ChannelDistributorDetail.CooperationModeEnum.RESOURCE_COOPERATION.getType())) {
                            channelDistributorDto.setChannelType(channelDistributorDetail.getResourceChannelType());
                            channelDistributorDto.setDeposit(channelDistributorDetail.getA2zPreDeposit().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
                        }

                        returnList.add(channelDistributorDto);
                    }
                }
        );
        return returnList;
    }

    @Override
    public PageResult<ChannelCorpPageVO> getPage(MktChannelPageDTO mktChannelPageDTO) {
        Page<ChannelCorpPageVO> page = channelDistributorDetailMapper.getPage(new Page<>(mktChannelPageDTO.getPageNum(), mktChannelPageDTO.getPageSize()), mktChannelPageDTO);
        return PageResult.of(page);
    }

    @Override
    public List<ChannelCorpPageVO> getRepeatList(MktChannelPageDTO mktChannelPageDTO) {
        return channelDistributorDetailMapper.getRepeatList(mktChannelPageDTO);
    }
}
