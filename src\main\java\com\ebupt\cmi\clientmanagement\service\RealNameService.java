package com.ebupt.cmi.clientmanagement.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.domain.entity.*;
import com.ebupt.cmi.clientmanagement.domain.entity.realname.ChannelRealNameInfo;
import com.ebupt.cmi.clientmanagement.domain.enums.lu.CardTypeEnum;
import com.ebupt.cmi.clientmanagement.domain.enums.lu.PackageStatusEnum;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.feign.pms.Package;
import com.ebupt.cmi.clientmanagement.feign.pms.PmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.HcardInfo;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.PackageVO;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.RealName;
import com.ebupt.cmi.clientmanagement.feign.rms.RmsFeignClient;
import com.ebupt.cmi.clientmanagement.mapper.ChannelOrderMapper;
import com.ebupt.cmi.clientmanagement.mapper.ChannelSurfMapper;
import com.ebupt.cmi.clientmanagement.mapper.CmsFlowpoolInfoCycleMapper;
import com.ebupt.cmi.clientmanagement.mapper.CmsPackageRealnameInfoMapper;
import com.ebupt.cmi.clientmanagement.mapper.realname.ChannelRealNameInfoMapper;
import com.ebupt.cmi.clientmanagement.service.impl.SmsLuService;
import com.ebupt.cmi.clientmanagement.utils.BizConstants;
import com.google.common.collect.ImmutableMap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;

/**
 * 套餐激活实名制处理逻辑
 *
 * <AUTHOR>
 * @date 2021年11月29日 17:56:22
 */
@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class RealNameService {

    private final ChannelSurfMapper channelSurfMapper;
    private final ChannelOrderMapper channelOrderMapper;
    private final ChannelRealNameInfoMapper channelRealNameInfoMapper;

    private final SmsLuService smsLuService;

    private final CmsPackageRealnameInfoMapper packageRealNameInfoMapper;

    private final PmsFeignClient pmsFeignClient;

    private final Executor taskExecutor;

    private final RedisTemplate<String, String> redisTemplate;

    private final RmsFeignClient rmsFeignClient;

    private static final String PATTREN = "yyyy-MM-dd HH:mm:ss";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    private static final SimpleDateFormat DATE_TIME_FORMAT = new SimpleDateFormat(PATTREN);

    @Value("${realName.startTime}")
    @DateTimeFormat(pattern = PATTREN)
    private Date startTime;

    @Resource
    private CmsFlowpoolInfoCycleMapper flowpoolInfoCycleMapper;

    /**
     * 实名制检查及处理
     *
     * @param mcc             MCC
     * @param cardSendLang    主卡短信下发语言
     * @param templateId      短信模板ID
     * @param realNameGroupId 实名制组ID
     * @param packageCard     待上网套餐
     * @return 是否需要激活套餐
     */
    public boolean checkAndSendRealNameSms(String mcc, String cardSendLang, Long templateId, Long realNameGroupId,
                                           ChannelPackageCard packageCard, String supplierId, boolean vCcrMessage) {
        String imsi = packageCard.getImsi();
        log.debug("实名制检查imsi: {}, mcc: {}", imsi, mcc);
        // mcc、短信模板id、imsi、实名制id、短信发送语言
        if ("1".equals(packageCard.getWhitelistPackage())) {
            //  cmi的胆子大
            log.debug("白名单套餐不用进行实名制");
            return true;
        }
        //实名制组ID为空，不走实名制流程
        if (realNameGroupId == null) {
            log.debug("主卡对应实名制id为空，不需要实名制");
            return true;
        }
        log.debug("查询此规则组id此国家对应得实名制规则id");
        Long realNameId = pmsFeignClient.getRealNameIdByGroupId(realNameGroupId, mcc).get();
        if (realNameId == null) {
            log.info("此规则组此国家不需要实名认证");
            return true;
        }
        String ruleCode = getRealNameRuleCode(realNameId);
        if (StringUtils.isBlank(ruleCode)) {
            log.warn("未找到实名制ID: {}对应规则信息", realNameId);
            return true;
        }
        log.debug("判断当前地区是否需要实名制,[<imsi={}>,<[realNameId={}>,<mcc={}>]", imsi, realNameId, mcc);
        Boolean necessary = isRealNameNecessary(realNameId, mcc, imsi);
        if (!necessary) {
            log.debug("imsi: {}在地区mcc: {}不需要实名制", imsi, mcc);
            return true;
        }
        final String packageStatus = packageCard.getPackageStatus();
        final boolean usingPackage = PackageStatusEnum.ACTIVATING.getStatus().equals(packageStatus)
                || PackageStatusEnum.ACTIVATED.getStatus().equals(packageStatus);
        final String packageUniqueId = packageCard.getPackageUniqueId();
        Boolean realNameNecessary = isRealNameNecessary(mcc, supplierId);
        if (realNameNecessary) {
            log.debug("套餐资源供应商免实名，认证通过");
            taskExecutor.execute(() -> handleExemptedPackage(packageCard, mcc, supplierId, ruleCode));
            return true;
        }
        boolean hasRealNameRecord = packageRealNameInfoMapper.selectOne(Wrappers.lambdaQuery(CmsPackageRealnameInfo.class)
                .eq(CmsPackageRealnameInfo::getPackageUniqueId, packageUniqueId)
                .eq(CmsPackageRealnameInfo::getMcc, mcc)
                .eq(CmsPackageRealnameInfo::getSupplierId, supplierId)) != null;
        if (usingPackage && !hasRealNameRecord) {
            // 激活中/已激活套餐
            if (PackageStatusEnum.ACTIVATED.getStatus().equals(packageStatus)) {
                final Date activeTime = packageCard.getActiveTime();
                // 判断套餐激活时间是否在指定时间之前
                if (activeTime.before(startTime)) {
                    log.debug("套餐packageUniqueId: {}激活时间 {} 在实名制生效时间 {} 之前，正常使用套餐", packageUniqueId,
                            DATE_TIME_FORMAT.format(activeTime), DATE_TIME_FORMAT.format(startTime));
                    return true;
                }
            }

            log.debug("[激活中/已激活套餐]查询是否存在上网信息");
            final boolean hasSurfInfo = channelSurfMapper.selectCount(Wrappers.lambdaQuery(ChannelSurf.class)
                    .eq(ChannelSurf::getImsi, imsi)
                    .eq(ChannelSurf::getMcc, mcc)
                    .eq(ChannelSurf::getPackageUniqueId, packageUniqueId)) > 0;
            if (hasSurfInfo) {
                log.debug("[激活中/已激活套餐]packageUniqueId: {}, imsi：{}, mcc: {}存在上网记录，正常使用套餐", packageUniqueId,
                        imsi, mcc);
                return true;
            }
        }
        final String iccid = packageCard.getIccid();
        if (getUsingRealNameInfo(ruleCode, iccid)) {
            return true;
        }
        if (!vCcrMessage) {
            taskExecutor.execute(() -> handleRealNameNotAuth(realNameId, packageCard, cardSendLang, templateId));
        }
        return false;
    }

    private boolean getUsingRealNameInfo(String ruleCode, String iccid) {
        log.debug("需要实名制，判断是否完成实名认证 iccid: {}", iccid);
        String key = String.format(BizConstants.USING_REALNAME_INFO_KEY, iccid);
        log.debug("从redis缓存中查询在用实名认证记录 key: {}, ruleCode: {}", key, ruleCode);
        Boolean exists = redisTemplate.opsForHash().hasKey(key, ruleCode);
        if (exists) {
            log.debug("存在在用实名认证记录且证件未过期，正常进行套餐激活");
            return true;
        }
        log.debug("redis缓存未查到，查数据库");
        Integer usingRealNameInfoCount = channelRealNameInfoMapper.selectCount(Wrappers.lambdaQuery
                        (ChannelRealNameInfo.class)
                .eq(ChannelRealNameInfo::getUseStatus, ChannelRealNameInfo.UseStatusEnum.USING.getValue())
                .eq(ChannelRealNameInfo::getIccid, iccid)
                .eq(ChannelRealNameInfo::getRuleCode, ruleCode)
                .ge(ChannelRealNameInfo::getCertificatesTime, DATE_FORMAT.format(new Date())));
        if (usingRealNameInfoCount > 0) {
            log.debug("存在在用实名认证记录且证件未过期，正常进行套餐激活");
            return true;
        }
        return false;
    }

    private void handleExemptedPackage(ChannelPackageCard channelPackageCard, String mcc, String supplierId, String ruleCode) {
        log.debug("异步检查是否需要记录免实名认证关系");
        String packageUniqueId = channelPackageCard.getPackageUniqueId();
        CmsPackageRealnameInfo cmsPackageRealnameInfo = packageRealNameInfoMapper.selectOne(Wrappers.lambdaQuery(CmsPackageRealnameInfo.class)
                .eq(CmsPackageRealnameInfo::getPackageUniqueId, packageUniqueId)
                .eq(CmsPackageRealnameInfo::getMcc, mcc)
                .eq(CmsPackageRealnameInfo::getSupplierId, supplierId));

        if (cmsPackageRealnameInfo != null) {
            log.debug("已有免实名认证关系");
            return;
        }

        if (getUsingRealNameInfo(ruleCode, channelPackageCard.getIccid())) {
            log.debug("主卡有可用实名制信息，不记录免实名认证关系");
            return;
        }

        packageRealNameInfoMapper.insert(CmsPackageRealnameInfo.builder()
                .packageUniqueId(packageUniqueId)
                .mcc(mcc)
                .supplierId(supplierId)
                .build());

    }

    private Boolean isRealNameNecessary(Long realNameId, String mcc, String imsi) {
        String key = String.format(BizConstants.REALNAME_ATTR_KEY, realNameId);
        log.debug("从redis缓存中判断实名制规则是否覆盖当前国家 key: {}, mcc: {}", key, mcc);
        Boolean exists = redisTemplate.opsForHash().hasKey(key, mcc);
        if (exists) {
            return true;
        }
        log.debug("redis缓存未查到，查数据库");
        return pmsFeignClient.isRealNameNecessary(realNameId, mcc, imsi).get();
    }

    public Boolean isRealNameNecessary(String mcc, String supplierId) {
        String key = String.format(BizConstants.REALNAME_ATTR_KEY, supplierId);
        log.debug("从redis缓存中判断供应商免实名是否覆盖当前国家 key: {}, mcc: {}", key, mcc);
        Boolean exists = redisTemplate.opsForHash().hasKey(key, mcc);
        if (exists) {
            return true;
        }
        log.debug("redis缓存未查到，查数据库");
        return Response.getAndCheckRemoteData(rmsFeignClient.getSupplierIsNeedRealName(mcc, supplierId));
    }

    private String getRealNameRuleCode(Long realNameId) {
        String key = String.format(BizConstants.REALNAME_KEY, realNameId);
        log.debug("从redis缓存中查询实名制规则ruleCode, key: {}", key);
        String ruleCode = redisTemplate.<String, String>opsForHash().get(key, "ruleCode");
        if (StringUtils.isNotBlank(ruleCode)) {
            return ruleCode;
        }
        log.debug("redis缓存未查到，查数据库");
        RealName realName = pmsFeignClient.getRealNameById(realNameId).get();
        if (realName != null) {
            return realName.getRuleCode();
        }
        return null;
    }

    /**
     * 实名认证处理
     */
    private void handleRealNameNotAuth(Long realNameId, ChannelPackageCard packageCard, String cardSendLang, Long templateId) {
        RealName realName = pmsFeignClient.getRealNameById(realNameId).get();
        log.debug("不存在在用实名认证记录或证件已过期，进入实名认证处理逻辑");
        String imsi = packageCard.getImsi();
        String iccid = packageCard.getIccid();
        final List<ChannelRealNameInfo> realNameInfos = channelRealNameInfoMapper.selectList(Wrappers.lambdaQuery(ChannelRealNameInfo.class)
                .in(ChannelRealNameInfo::getAuthStatus, ChannelRealNameInfo.AuthStatusEnum.NOT_AUTH.getValue(),
                        ChannelRealNameInfo.AuthStatusEnum.AUTHING.getValue())
                .eq(ChannelRealNameInfo::getRuleCode, realName.getRuleCode())
                .eq(ChannelRealNameInfo::getIccid, iccid));
        final boolean hasAuthing = realNameInfos.stream().anyMatch(realNameInfo -> ChannelRealNameInfo
                .AuthStatusEnum.AUTHING.matches(realNameInfo.getAuthStatus()));
        if (!hasAuthing) {
            log.debug("不存在认证中实名认证记录，下发实名制引导短信");
            try {
                sendRealNameSms(cardSendLang, templateId, realName, packageCard);
            } catch (Exception e) {
                log.warn("下发实名制引导短信失败: {}", e);
            }
            log.debug("判断是否存在待认证实名认证记录");
            final boolean hasNotAuth = realNameInfos.stream().anyMatch(realNameInfo -> ChannelRealNameInfo
                    .AuthStatusEnum.NOT_AUTH.matches(realNameInfo.getAuthStatus()));
            if (!hasNotAuth) {
                log.debug("不存在待认证的实名认证记录，新增一条");
                channelRealNameInfoMapper.insert(ChannelRealNameInfo.builder()
                        .imsi(imsi)
                        .msisdn(packageCard.getMsisdn())
                        .iccid(iccid)
                        .ruleCode(realName.getRuleCode())
                        .ruleName(realName.getName())
                        .orderUniqueId(packageCard.getOrderUniqueId())
                        .authStatus(ChannelRealNameInfo.AuthStatusEnum.NOT_AUTH.getValue())
                        .useStatus(ChannelRealNameInfo.UseStatusEnum.PROCESSING.getValue())
                        .authObj(ChannelRealNameInfo.AuthObjEnum.CARD.getValue())
                        .build());
            }
        }
    }

    private void sendRealNameSms(String cardSendLang, Long templateId, RealName realName,
                                 ChannelPackageCard packageCard) {
        log.debug("下发实名制引导短信");
        String sendLang = cardSendLang;
        final String orderUniqueId = packageCard.getOrderUniqueId();
        if (StringUtils.isNotBlank(orderUniqueId)) {
            ChannelOrder order = channelOrderMapper.selectOne(Wrappers.lambdaQuery(ChannelOrder.class)
                    .eq(ChannelOrder::getOrderUniqueId, orderUniqueId));
            if (order != null) {
                final String orderSendLang = order.getSendLang();
                sendLang = StringUtils.isNotBlank(orderSendLang) ? orderSendLang : cardSendLang;
            }
        }
        String packageId = packageCard.getPackageId();
        if ("3".equals(packageCard.getPackageType())) {
            Date now = new Date();
            CmsFlowpoolInfoCycle flowpoolInfoCycle = flowpoolInfoCycleMapper.selectOne(Wrappers.lambdaQuery(CmsFlowpoolInfoCycle.class)
                    .eq(CmsFlowpoolInfoCycle::getFlowPoolId, packageId)
                    .eq(CmsFlowpoolInfoCycle::getStatus, CmsFlowpoolInfoCycle.Status.IN_APPLY.getStatus())
                    .le(CmsFlowpoolInfoCycle::getStartTime, now)
                    .gt(CmsFlowpoolInfoCycle::getEndTime, now));
            if (flowpoolInfoCycle == null) {
                log.debug("状态为应用且在有效期内的流量池周期数据不存在 flowPoolId: {}", packageId);
                throw new BizException("无可用套餐");
            }
            packageCard.setPackageName(flowpoolInfoCycle.getFlowPoolName());
            packageCard.setNameEn(flowpoolInfoCycle.getNameEn());
            packageCard.setNameTw(flowpoolInfoCycle.getNameTw());
        }
        if (packageCard.getPackageName() == null) {
            PackageVO packageVO = Response.getAndCheckRemoteData(pmsFeignClient.queryPackage(packageId));
            packageCard.setPackageName(packageVO.getNameCn());
            packageCard.setNameEn(packageVO.getNameEn());
            packageCard.setNameTw(packageVO.getNameTw());
        }
        String msisdn = packageCard.getMsisdn();
        String packageName = getPackageName(packageCard, sendLang);
        final String url = realName.getCardUrl() + "?iccid=" + packageCard.getIccid() + "&change=0";
        Map<String, List<String>> params = ImmutableMap.of(BizConstants.SmsPlaceHolder.PACKAGE_NAME, Collections.singletonList(packageName),
                BizConstants.SmsPlaceHolder.URL, Collections.singletonList(url));
        // 下发短信
        smsLuService.sendNoticeSms(msisdn, templateId, BizConstants.REAL_NAME_SCENE_ID, sendLang,
                null, null, params);
    }

    private String getPackageName(ChannelPackageCard packageCard, String sendLang) {
        String packageName;
        if (HcardInfo.SendLangEnum.ZH_HK.getValue().equals(sendLang)) {
            packageName = packageCard.getNameTw();
        } else if (HcardInfo.SendLangEnum.EN_US.getValue().equals(sendLang)) {
            packageName = packageCard.getNameEn();
        } else {
            packageName = packageCard.getPackageName();
        }
        return packageName;
    }


}
