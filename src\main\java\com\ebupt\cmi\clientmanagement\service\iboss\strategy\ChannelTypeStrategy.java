package com.ebupt.cmi.clientmanagement.service.iboss.strategy;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelChargeRecord;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelDistributorDetail;
import com.ebupt.cmi.clientmanagement.domain.entity.redis.RedissonLock;
import com.ebupt.cmi.clientmanagement.domain.req.IBossRechargeReq;
import com.ebupt.cmi.clientmanagement.domain.response.ApiResponseEnum;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.BillRecordVo;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.exception.IBossException;
import com.ebupt.cmi.clientmanagement.feign.mkt.MktFeignClient;
import com.ebupt.cmi.clientmanagement.feign.mkt.domain.SettlementForm;
import com.ebupt.cmi.clientmanagement.mapper.ChannelChargeRecordMapper;
import com.ebupt.cmi.clientmanagement.mapper.ChannelDistributorDetailMapper;
import com.ebupt.cmi.clientmanagement.service.ChannelService;
import com.ebupt.cmi.clientmanagement.service.channel.ChannelDistributorsService;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Date;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.concurrent.CompletableFuture;

@Service
@Slf4j
public abstract class ChannelTypeStrategy {

    @Autowired
    private ChannelChargeRecordMapper channelChargeRecordMapper;

    @Resource
    private ChannelService channelService;

    @Resource
    private RedissonLock redissonLock;

    @Resource
    private MktFeignClient mktFeignClient;


    @Resource
    private ChannelDistributorsService channelDistributorsService;

    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional(rollbackFor = Exception.class, timeoutMills = 30000)
    public Response<Void> proceed(IBossRechargeReq iBossRechargeReq, ChannelDistributorDetail channelDistributorDetail) {
        String type = iBossRechargeReq.getType();
        log.debug("IBoss充值类型确认：{}", type);
        String code = iBossRechargeReq.getCode();
        boolean lock = redissonLock.tryLock(code);
        if (!lock) {
            return Response.error(ApiResponseEnum.CODE_REPEAT);
        }
        try {
            ChannelChargeRecord channelChargeRecord = channelChargeRecordMapper.selectOne(Wrappers.lambdaQuery(ChannelChargeRecord.class)
                    .eq(ChannelChargeRecord::getCode, code));
            if (channelChargeRecord != null) {
                return Response.error(ApiResponseEnum.CODE_REPEAT);
            }
            String chargeType = type;
            String billType = type;
            BigDecimal amount = new BigDecimal(iBossRechargeReq.getNewAmount());

            switch (type) {
                case "1":
                    payBill(channelDistributorDetail, amount);
                    break;
                case "2":
                    addDeposit(channelDistributorDetail, amount);
                    break;
                case "3":
                    increasePreDeposit(channelDistributorDetail, amount);
                    break;
                case "4":
                    returnRemuneration(channelDistributorDetail, amount);
                    break;
                case "5":
                    addDepositForA2z(channelDistributorDetail, amount);
                    chargeType = "6";
                    break;
                case "6":
                    payBillForA2z(channelDistributorDetail, amount);
                    chargeType = "7";
                    break;
                //甲方给钱不要白不要，其实都不用加代码
                case "8":
                    channelDistributorsService.changeMarketingAmount(channelDistributorDetail.getId(), amount, "1");
                    break;
                case "9":
                    channelDistributorsService.changeMarketingAmount(channelDistributorDetail.getId(), amount, "2");
                    billType = "-1";
                    break;
                case "10":
                    addPreDepositForA2z(channelDistributorDetail, amount);
                    billType = "-1";
                    break;
                default:
                    return Response.error(ApiResponseEnum.DEPOSIT_TYPE_ERROR);
            }

            log.debug("充值完成，开始写充值记录,渠道商流水记录");
            String cooperationMode;
            if ("56".contains(type)) {
                cooperationMode = channelDistributorDetail.getChannelCooperationMode()
                        .contains(ChannelDistributorDetail.CooperationModeEnum.A2Z.getType()) ?
                        ChannelDistributorDetail.CooperationModeEnum.A2Z.getType() :
                        ChannelDistributorDetail.CooperationModeEnum.RESOURCE_COOPERATION.getType();

            } else {
                cooperationMode = ChannelDistributorDetail.CooperationModeEnum.CONSIGNMENT.getType();
            }

            LocalDateTime now = LocalDateTime.now();
            java.util.Date date = Date.from(now.atZone(ZoneId.systemDefault()).toInstant());

            ChannelChargeRecord chargeRecord = ChannelChargeRecord.builder()
                    .amount(amount)
                    .chargeType(chargeType)
                    .chargeTime(now)
                    .code(iBossRechargeReq.getCode())
                    .corpId(channelDistributorDetail.getCorpId())
                    .currencyCode(iBossRechargeReq.getCurrencyCode())
                    .ebsCode(iBossRechargeReq.getEbsCode())
                    .build();
            BillRecordVo billRecordVo = BillRecordVo.builder()
                    .amount(amount)
                    .orderSubOrUnsubDate(date)
                    .cooperationMode(cooperationMode)
                    .billType(billType)
                    .corpId(channelDistributorDetail.getCorpId())
                    .build();

            insertChargeRecord(chargeRecord, billRecordVo);

            return Response.okForApi("0");


        } finally {
            try {
                if (redissonLock.isHeldByCurrentThread(code)) {
                    redissonLock.unlock(code);
                }
            } catch (Exception e) {
                log.warn("redis解锁失败");
            }
        }
    }

    private void addDepositForSell(ChannelDistributorDetail channelDistributorDetail, BigDecimal amount) {
        channelDistributorsService.changeDepositAndTotal(channelDistributorDetail.getId(), amount, 1);
    }

    private void payBillForA2z(ChannelDistributorDetail channelDistributorDetail, BigDecimal amount) {
        // BigDecimal a2zUsedDeposit = channelDistributorDetail.getA2zUsedDeposit().subtract(amount);
        channelDistributorsService.changeA2zDepositOrA2zUsedDeposit(channelDistributorDetail.getId(), amount, -1);
        //channelDistributorDetailMapper.update(null, Wrappers.lambdaUpdate(ChannelDistributorDetail.class)
        //        .set(ChannelDistributorDetail::getA2zUsedDeposit, a2zUsedDeposit)
        //        .eq(ChannelDistributorDetail::getId, channelDistributorDetail.getId()));
    }

    private void addPreDepositForA2z(ChannelDistributorDetail channelDistributorDetail, BigDecimal amount) {
        //BigDecimal a2zPreDeposit = channelDistributorDetail.getA2zPreDeposit().add(amount);
        channelDistributorsService.changeA2zDepositOrA2zUsedDeposit(channelDistributorDetail.getId(), amount, 1);
        //channelDistributorDetailMapper.update(null, Wrappers.lambdaUpdate(ChannelDistributorDetail.class)
        //        .set(ChannelDistributorDetail::getA2zPreDeposit, a2zPreDeposit)
        //        .eq(ChannelDistributorDetail::getId, channelDistributorDetail.getId()));
    }

    private void addDepositForA2z(ChannelDistributorDetail channelDistributorDetail, BigDecimal amount) {

        channelDistributorsService.changeA2zDepositOrA2zUsedDeposit(channelDistributorDetail.getId(), amount, 1);

    }

    abstract void addDeposit(ChannelDistributorDetail channelDistributorDetail, BigDecimal amount);

    abstract void increasePreDeposit(ChannelDistributorDetail channelDistributorDetail, BigDecimal amount);

    abstract void payBill(ChannelDistributorDetail channelDistributorDetail, BigDecimal amount);

    abstract void returnRemuneration(ChannelDistributorDetail channelDistributorDetail, BigDecimal amount);

    public void checkDeposit(BigDecimal deposit, BigDecimal used) {
        if (deposit.compareTo(BigDecimal.ZERO) < 0) {
            log.error("充值金额错误,计算后可用额度: {}小于0", deposit);
            throw new IBossException(ApiResponseEnum.DEPOSIT_ERROR.getCode());
        }
        if (used.compareTo(BigDecimal.ZERO) < 0) {
            log.error("充值金额错误,计算后已用额度: {}小于0", used);
            throw new IBossException(ApiResponseEnum.USED_DEPOSIT_ERROR.getCode());
        }
    }

    public void checkTotalDeposit(BigDecimal deposit, BigDecimal totalDeposit) {
        if (totalDeposit.compareTo(BigDecimal.ZERO) < 0) {
            log.error("充值金额错误,计算后总额度: {}小于0", totalDeposit);
            throw new IBossException(ApiResponseEnum.TOTAL_DEPOSIT_ERROR.getCode());
        }
        if (deposit.compareTo(BigDecimal.ZERO) < 0) {
            log.error("充值金额错误,计算后可用额度: {}小于0", deposit);
            throw new IBossException(ApiResponseEnum.DEPOSIT_ERROR.getCode());
        }
    }

    public void insertChargeRecord(ChannelChargeRecord channelChargeRecord, BillRecordVo billRecordVo) {
        channelService.channelChargeRecord(channelChargeRecord);
        if ("123410".contains(billRecordVo.getBillType())) {
            channelService.channelBillRecord(billRecordVo);
        }
    }
}
