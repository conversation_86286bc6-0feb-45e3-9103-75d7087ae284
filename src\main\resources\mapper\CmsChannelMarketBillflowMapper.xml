<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ebupt.cmi.clientmanagement.mapper.CmsChannelMarketBillFlowMapper">

    <select id="countYesterdayMarketAmount" resultType="java.math.BigDecimal">
        SELECT IFNULL(-sum(amount), 0)
        FROM cms_channel_market_billflow
        WHERE corp_id = #{corpId}
          AND amount IS NOT NULL
        AND type in ('2', '3', '4', '5')
        AND create_time <![CDATA[ >= ]]> #{startTime}
          AND create_time <![CDATA[ < ]]> #{endTime}
    </select>


    <select id="getCountBySelectParam" resultType="java.lang.Long">
        select count(*) from cms_channel_market_billflow ccmb
        WHERE
        1=1
        <if test="corpId != null and corpId != ''">
            AND ccmb.corp_id =#{corpId}
        </if>
        <if test="beginDate != null">
            AND ccmb.create_time >=#{beginDate}
        </if>
        <if test="endDate != null">
            AND ccmb.create_Time &lt;=#{endDate}
        </if>
        <if test="activityId != null and activityId != ''">
            AND ccmb.activity_id=#{activityId}
        </if>
    </select>


    <select id="getBillflowBySelectParam"
            resultType="com.ebupt.cmi.clientmanagement.domain.entity.CmsChannelMarketBillflow">

        select
            *
        from cms_channel_market_billflow ccmb

        WHERE
        1=1
        <if test="corpId != null and corpId != ''">
            AND ccmb.corp_id =#{corpId}
        </if>
        <if test="beginDate != null ">
            AND ccmb.create_time >=#{beginDate}
        </if>
        <if test="endDate != null ">
            AND ccmb.create_time &lt;=#{endDate}
        </if>
        <if test="activityId != null and activityId != ''">
            AND ccmb.activity_id=#{activityId}
        </if>
        limit #{beginNum},#{pageSize}

    </select>

</mapper>