package com.ebupt.cmi.clientmanagement.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.config.NoticeConfigProperties;
import com.ebupt.cmi.clientmanagement.config.PurchaseSmsConfigProperties;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.enums.QueueEnum;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.vo.SingleDayDelayVO;
import com.ebupt.cmi.clientmanagement.consumer.uitils.SendMessageWrapper;
import com.ebupt.cmi.clientmanagement.domain.dto.Context.CreateOrderContext;
import com.ebupt.cmi.clientmanagement.domain.dto.HimsiStatusAndLocationDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.*;
import com.ebupt.cmi.clientmanagement.domain.entity.realname.ChannelRealNameInfo;
import com.ebupt.cmi.clientmanagement.domain.entity.realname.ChannelRealNameOrder;
import com.ebupt.cmi.clientmanagement.domain.entity.redis.RedissonLock;
import com.ebupt.cmi.clientmanagement.domain.enums.*;
import com.ebupt.cmi.clientmanagement.domain.enums.cooperation.ChannelCheckStatus;
import com.ebupt.cmi.clientmanagement.domain.enums.lu.*;
import com.ebupt.cmi.clientmanagement.domain.req.CompensationReq;
import com.ebupt.cmi.clientmanagement.domain.response.ApiResponseEnum;
import com.ebupt.cmi.clientmanagement.domain.response.CreateOrderResp;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.response.ResponseResult;
import com.ebupt.cmi.clientmanagement.domain.vo.*;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.feign.back.BackFeignClient;
import com.ebupt.cmi.clientmanagement.feign.control.ControlFeignClient;
import com.ebupt.cmi.clientmanagement.feign.pms.PmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.*;
import com.ebupt.cmi.clientmanagement.mapper.*;
import com.ebupt.cmi.clientmanagement.mapper.realname.ChannelRealNameInfoMapper;
import com.ebupt.cmi.clientmanagement.mapper.realname.ChannelRealNameOrderMapper;
import com.ebupt.cmi.clientmanagement.service.ccrcommon.CCRCommonService;
import com.ebupt.cmi.clientmanagement.service.channel.ChannelDistributorsService;
import com.ebupt.cmi.clientmanagement.service.channelself.impl.Common;
import com.ebupt.cmi.clientmanagement.service.impl.FlowPoolServiceImpl;
import com.ebupt.cmi.clientmanagement.service.lu.h.delegate.AbstractInquirePackageTemplate;
import com.ebupt.cmi.clientmanagement.utils.ChannelRebateUtil;
import com.ebupt.cmi.clientmanagement.utils.SpringContextHolder;
import com.ebupt.cmi.clientmanagement.utils.Utils;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.ebupt.cmi.clientmanagement.service.PersonalOrderService.getSimplifiedExceptionMessage;

/**
 * 外部API订单相关逻辑
 *
 * <AUTHOR>
 * @date 2021-5-24 17:40:01
 */
@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class OrderService {

    private static final double ONE_PERCENT = 0.01;
    private static final int KB_COEFFICIENT = 1024;

    private static final ThreadLocal<CreateOrderContext> createOrderContext = new ThreadLocal<>();

    private final PmsFeignClient pmsFeignClient;

    private final BackFeignClient backFeignClient;

    private final CmsBigOrderMapper cmsBigOrderMapper;
    private final ChannelMapper channelMapper;
    private final ChannelCardMapper channelCardMapper;
    private final ChannelPackageCardMapper channelPackageCardMapper;
    private final ChannelOrderMapper channelOrderMapper;
    private final ChannelOrderDetailMapper channelOrderDetailMapper;
    private final ChannelDistributorDetailMapper channelDistributorDetailMapper;

    private final ChannelRealNameInfoMapper channelRealNameInfoMapper;
    private final ChannelRealNameOrderMapper channelRealNameOrderMapper;

    private final SmsService smsService;
    private final PackageService packageService;
    private final PackageEndService packageEndService;

    private final NoticeConfigProperties noticeConfigProperties;
    private final PurchaseSmsConfigProperties purchaseSmsConfigProperties;

    private final ChannelPackageRelationMapper channelPackageRelationMapper;

    private final ChannelSurfInfoMapper channelSurfInfoMapper;

    private final RedisTemplate<String, String> redisTemplate;
    private final SendMessageWrapper sendMessageWrapper;

    private final ChannelDistributorsService channelDistributorsService;

    private final RedissonLock redissonLock;

    private final ChannelService channelService;

    private final CmsTopchannelMapper cmsTopchannelMapper;

    private final ICCIDNumberRelationMapper iccidNumberRelationMapper;

    private final CmsChannelRelationMapper cmsChannelRelationMapper;

    private final CmsPackageCardUpccRelationMapper cmsPackageCardUpccRelationMapper;

    private final CmsFlowpoolCountryRelationMapper flowpoolCountryRelationMapper;

    private final Common common;

    private final CCRCommonService ccrCommonService;

    private final FlowPoolServiceImpl flowPoolServiceImpl;

    private final ControlFeignClient controlFeignClient;

    private final CmsChannelPackageDetailMapper channelPackageDetailMapper;

    private final PackageDirectionRelationService packageDirectionRelationService;

    private final CmsPackageDayRemainMapper cmsPackageDayRemainMapper;

    private final CmsPackageCycleRemainMapper cmsPackageCycleRemainMapper;

    private final ChannelRebateUtil channelRebateUtil;

    private final PackageOverdueService packageOverdueService;

    @Resource
    private Executor luExecutor;

    @Resource
    private AbstractInquirePackageTemplate unactivatedPackageChain;

    @Value("${refuelPackage.recoveryExpires:3}")
    private Long recoveryExpires;

    @Value("${package.allowUpdateTime:30}")
    private Integer allowUpdateTime;

    @Value("${order.maxOrderNum:100}")
    private Integer maxOrderNum;

    public CreateOrderResp createOrder(CreateOrderReq createOrderReq) {
        CreateOrderResp createOrderResp;
        OrderService orderService;
        createOrderContext.set(new CreateOrderContext(createOrderReq));
        try {
            orderService = SpringContextHolder.getBean("orderService");
            createOrderResp = orderService.createOrder1(createOrderReq);
        } catch (Exception e) {
            createOrderContext.remove();
            throw e;
        }

        try {
            if (createOrderResp.getIsRepeat()) {
                createOrderContext.remove();
                return createOrderResp;
            }
            CreateOrderContext currentThreadContext = OrderService.createOrderContext.get();
            switch (currentThreadContext.getPurchaseType()) {
                case CARD_AND_PACKAGE:
                    if (currentThreadContext.isEsimCard()) {
                        ChannelCard channelCard = currentThreadContext.getChannelCards().get(0);
                        orderService.sendCancelLocationIfNeeded(createOrderReq.getDataBundleId(), currentThreadContext.getOrderDate(),
                                channelCard.getCorpId(), channelCard.getImsi(), channelCard.getIccid());
                    }
                    break;
                case BIND_PACKAGE:
                    ChannelCard channelCard = currentThreadContext.getChannelCards().get(0);
                    orderService.sendCancelLocationIfNeeded(createOrderReq.getDataBundleId(), new Date(), null, channelCard.getImsi(), channelCard.getIccid());
                    break;
                case COMMON_PACKAGE:
                    for (ChannelCard card : currentThreadContext.getChannelCards()) {
                        orderService.sendCancelLocationIfNeeded(createOrderReq.getDataBundleId(), currentThreadContext.getOrderDate(),
                                card.getCorpId(), card.getImsi(), card.getIccid());
                    }
            }
        } finally {
            createOrderContext.remove();
        }

        return createOrderResp;
    }

    /**
     * 创建订单
     * 本来是该这创建完的，但是发rar又不能在事务里
     *
     * @param createOrderReq
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional(rollbackFor = Exception.class, timeoutMills = 30000)
    public CreateOrderResp createOrder1(CreateOrderReq createOrderReq) {
        /**
         * 省移动卡校验参数
         * */
        if ((StrUtil.isBlank(createOrderReq.getCmccNumber()) && StrUtil.isNotBlank(createOrderReq.getCmccNumberProvince())) ||
                (StrUtil.isNotBlank(createOrderReq.getCmccNumber()) && StrUtil.isBlank(createOrderReq.getCmccNumberProvince()))) {
            throw new BizException("cmccNumber or cmccNumberProvince is empty", "1000165");
        }
        if (StrUtil.isNotBlank(createOrderReq.getCmccNumber()) && StrUtil.isNotBlank(createOrderReq.getCmccNumberProvince())) {
            ICCIDNumberRelation iccidNumberRelation = iccidNumberRelationMapper.selectOne(Wrappers.lambdaQuery(ICCIDNumberRelation.class)
                    .eq(ICCIDNumberRelation::getIccid, createOrderReq.getIccid()));
            if (ObjectUtil.isNull(iccidNumberRelation)) {
                iccidNumberRelationMapper.insert(ICCIDNumberRelation.builder()
                        .cmccNumber(createOrderReq.getCmccNumber())
                        .cmccNumberProvince(createOrderReq.getCmccNumberProvince())
                        .iccid(createOrderReq.getIccid())
                        .build());
            } else {
                if (!createOrderReq.getCmccNumber().equals(iccidNumberRelation.getCmccNumber())
                        || !createOrderReq.getCmccNumberProvince().equals(iccidNumberRelation.getCmccNumberProvince())) {
                    throw new BizException(ApiResponseEnum.NUMBER_RELATION_ERROR);
                }
            }
        }

        /**
         * 判断是否是零级渠道商
         */
        CmsTopchannel cmsTopchannel = cmsTopchannelMapper.selectOne(Wrappers.lambdaQuery(CmsTopchannel.class)
                .eq(CmsTopchannel::getCorpId, createOrderReq.getUserId()));
        if (ObjectUtil.isNotNull(cmsTopchannel)) {
            log.debug("用户是零级渠道商");
            if (CmsTopchannel.isSubEnum.NO.getValue().equals(cmsTopchannel.getIsSub())) {
                throw new BizException("Ordering is not permitted", "1000163");
            }
            if (StrUtil.isBlank(createOrderReq.getIccid())) {
                throw new BizException("ICCID is empty", "1000164");
            }
            String corpId = channelCardMapper.selectCorpId(createOrderReq.getIccid());
            CmsChannelRelation cmsChannelRelation = cmsChannelRelationMapper.selectOne(Wrappers.lambdaQuery(CmsChannelRelation.class)
                    .eq(CmsChannelRelation::getCorpId, corpId)
                    .eq(CmsChannelRelation::getParentCorpId, createOrderReq.getUserId()));
            if (ObjectUtil.isNull(cmsChannelRelation)) {
                throw new BizException("No permission to operate this IMSI or ICCID", "1000076");
            }
            //替换该卡userID为子渠道商的corpID
            createOrderReq.setUserId(cmsChannelRelation.getCorpId());
        }

        String goodsId = null;

        if (StrUtil.isNotBlank(createOrderReq.getDataBundleId())) {
            String productId = pmsFeignClient.selectProductId(createOrderReq.getDataBundleId()).getData();
            if (StrUtil.isNotBlank(productId)) {
                log.debug("对DataBundleId进行替换");
                goodsId = createOrderReq.getDataBundleId();
                createOrderReq.setDataBundleId(productId);
            }
        }
        String userIdAndThirdOrderIdLock = createOrderReq.getUserId() + "_" + createOrderReq.getThirdOrderId();
        final boolean lock = redissonLock.tryLock(userIdAndThirdOrderIdLock);
        if (!lock) {
            log.warn("获取分布式锁" + userIdAndThirdOrderIdLock + "失败");
            throw new BizException(ApiResponseEnum.REPEAT_REQUEST);
        }
        if (StringUtils.isNotBlank(createOrderReq.getTransactionCode())) {
            ChannelOrder order = channelOrderMapper.selectOne(Wrappers.lambdaQuery(ChannelOrder.class)
                    .eq(ChannelOrder::getBillFlow, createOrderReq.getTransactionCode()));
            //若流水号重复则把重复的订单信息返回给用户
            if (order != null) {
                CreateOrderResp repeatTransactionOrder = new CreateOrderResp();
                repeatTransactionOrder.setTotalAmount(String.valueOf(order.getAmount()));
                repeatTransactionOrder.setOrderID(order.getOrderUniqueId());

                repeatTransactionOrder.setCurrency(order.getCurrencyCode());
                repeatTransactionOrder.setQuantity(order.getCount());

                BigDecimal price = order.getAmount().divide(BigDecimal.valueOf(order.getCount()), RoundingMode.UNNECESSARY);
                repeatTransactionOrder.setPrice(String.valueOf(price));
                repeatTransactionOrder.setIsRepeat(true);
                return repeatTransactionOrder;
            }
            boolean codeLock = redissonLock.tryLock(createOrderReq.getTransactionCode());
            if (!codeLock) {
                log.warn("交易流水号获取分布式锁失败");
                throw new BizException(ApiResponseEnum.REPEAT_REQUEST);
            }
        }
        try {
            if (StringUtils.isNotBlank(createOrderReq.getCurrency()) && StringUtils.isBlank(CurrencyCode.getName(createOrderReq.getCurrency()))) {
                throw new BizException(ApiResponseEnum.CURRENCY_INPUT_ERROR);
            }
            Integer role = createOrderReq.getRole();
            Integer includeCard = createOrderReq.getIncludeCard();
            CreateOrderResp createOrderResp = new CreateOrderResp();
            final String userId = createOrderReq.getUserId();
            if (packageEndService.channelApiAccessCheck(userId, role.toString(), "", createOrderReq.getIccid(), "")) {
                throw new BizException(ApiResponseEnum.CARD_NO_PERMISSION);
            }

            if (CreateOrderReq.RoleTypeEnum.MSISDN.matches(role) || CreateOrderReq.RoleTypeEnum.EMAIL.matches(role)) {
                // 个人用户
                final String accountId = createOrderReq.getUserId();
                createOrderReq.setUserId(accountId);
                final Channel channel = channelMapper.selectOne(Wrappers.lambdaQuery(Channel.class)
                        .eq(Channel::getCorpId, accountId));
                if (channel == null) {
                    throw new BizException(ApiResponseEnum.USER_MESSAGE_NOT_EXIST);
                }
                final String corpName = channel.getCorpName();
                if (includeCard == 0) {
                    // 购买套餐/加油包，算酬金时套餐为间接收入,加油包不算酬金
                    log.debug("个人用户，购买套餐/加油包");
                    createOrderReq.setOrderChannel(OrderChannel.OFFICIAL.getType());
                    purchasePackage(createOrderReq, createOrderResp, null, RemunerationTypeEnum.INDIRECT_INCOME, corpName, goodsId);
                } else {
                    // 购买卡/卡+套餐
                    log.debug("个人用户，购买卡+套餐");
                    purchaseCardAndPackage(createOrderReq, createOrderResp, corpName, goodsId);
                }
            } else if (CreateOrderReq.RoleTypeEnum.TEMPORARY.matches(role)) {
                // 临时用户，购买卡+套餐
                log.debug("临时用户，只允许购买卡+套餐");
                if (includeCard == 0) {
                    throw new BizException(ApiResponseEnum.TEMPORARY_USER_LIMIT);
                }
                String corpId = noticeConfigProperties.getCorpId();
                final Channel channel = channelMapper.selectOne(Wrappers.lambdaQuery(Channel.class)
                        .eq(Channel::getCorpId, corpId));
                if (channel == null) {
                    log.warn("corpId: {}对应客户信息不存在", corpId);
                    throw new BizException(ApiResponseEnum.USER_MESSAGE_NOT_EXIST);
                }
                createOrderReq.setUserId(corpId);
                final String corpName = channel.getCorpName();
                purchaseCardAndPackage(createOrderReq, createOrderResp, corpName, goodsId);
            } else if (CreateOrderReq.RoleTypeEnum.ICCID.matches(role)) {
                // ICCID用户，只购买套餐/加油包，以userid作为ICCID，算酬金时为间接收入
                log.debug("ICCID用户，只允许购买套餐/加油包");
                if (includeCard == 1) {
                    throw new BizException(ApiResponseEnum.CHANNEL_OR_ICCID_USER_LIMIT);
                }
                createOrderReq.setIccid(userId);
                createOrderReq.setOrderChannel(OrderChannel.OFFICIAL.getType());
                purchasePackage(createOrderReq, createOrderResp, null, RemunerationTypeEnum.INDIRECT_INCOME, userId, goodsId);
            } else if (CreateOrderReq.RoleTypeEnum.CHANNEL.matches(role)) {
                // 渠道商，购买套餐/加油包，算酬金时为直接收入
                log.debug("渠道商，允许购买套餐/加油包");
                if (includeCard == 1) {
                    throw new BizException(ApiResponseEnum.CHANNEL_OR_ICCID_USER_LIMIT);
                }
                final Channel channel = channelMapper.selectOne(Wrappers.lambdaQuery(Channel.class)
                        .eq(Channel::getCorpId, userId)
                        .eq(Channel::getStatus, Channel.StatusEnum.NORMAL.getValue())
                        .eq(Channel::getCheckStatus, ChannelCheckStatus.PASS.getCode()));
                if (channel == null) {
                    throw new BizException(ApiResponseEnum.CHANNEL_INFORMATION_IS_ABNORMAL);
                }
                final String corpName = channel.getCorpName();
                final ChannelDistributorDetail distributorDetail = getChannelDistributorDetail(userId);
                if (ChannelDistributorDetail.PermitSubscribeEnum.NO.matches(distributorDetail.getIsSub())) {
                    throw new BizException(ApiResponseEnum.ORDER_NOT_ALLOWED);
                }
                if (StringUtils.isBlank(createOrderReq.getOrderChannel())) {
                    createOrderReq.setOrderChannel(OrderChannel.API.getType());
                }
                createOrderReq.setCurrency(distributorDetail.getCurrencyCode());

                ChannelDistributorDetail targetDistributorDetail;
                if (Channel.ChannelTypeEnum.SUBCHANNEL.getValue().equals(channel.getType())) {
                    createOrderResp.setSubCorpId(createOrderReq.getUserId());
                    createOrderResp.setParentCorpId(channel.getParentCorpId());
                    targetDistributorDetail = getChannelDistributorDetail(channel.getParentCorpId());
                } else {
                    targetDistributorDetail = distributorDetail;
                }
                createOrderReq.setPrice(null);
                purchasePackage(createOrderReq, createOrderResp, targetDistributorDetail, RemunerationTypeEnum.DIRECT_INCOME, corpName, goodsId);
            } else {
                log.warn("非个人或渠道用户 role: {}", role);
                throw new BizException(ApiResponseEnum.USER_NOT_HAVE_PERMISSION);
            }
            createOrderResp.setQuantity(createOrderReq.getQuantity());
            return createOrderResp;
        } catch (Exception e) {
            log.error("创建订单失败", e);
            throw e;
        } finally {
            try {
                if (redissonLock.isHeldByCurrentThread(userIdAndThirdOrderIdLock)) {
                    redissonLock.unlock(userIdAndThirdOrderIdLock);
                }
                if (StringUtils.isNotBlank(createOrderReq.getTransactionCode())) {
                    if (redissonLock.isHeldByCurrentThread(createOrderReq.getTransactionCode())) {
                        redissonLock.unlock(createOrderReq.getTransactionCode());
                    }
                }
            } catch (Exception e) {
                log.warn("redis解锁失败，请检查redis阻塞情况", e);
            }
        }

    }

    private void purchaseBindPackage(CreateOrderReq createOrderReq, CreateOrderResp createOrderResp,
                                     ChannelDistributorDetail distributorDetail, PackageVO packageVO,
                                     ChannelCard channelCard, String goodsId) {
        log.debug("购买绑定套餐流程");
        if (distributorDetail == null) {
            throw new BizException(ApiResponseEnum.PACKAGE_IS_NOT_ALLOWED_ORDERED);
        }

        String currencyCode = distributorDetail.getCurrencyCode();
        checkChannelPermission(currencyCode, createOrderReq, createOrderResp, null);

        String orderUniqueId = Utils.cmiUuid(CmiUUID.ORDER_UNIQUE_ID);

        common.bindPackage(BindPackageVo.builder()
                .corpId(channelCard.getCorpId())
                .imsi(channelCard.getImsi())
                .iccid(channelCard.getIccid())
                .msisdn(channelCard.getMsisdn())
                .orderChannel("102")
                .packageType(CardPackageTypeEnum.PACKAGE.getPackageType())
                .packageId(packageVO.getId())
                .orderUniqueID(orderUniqueId)
                .thirdOrderId(createOrderReq.getThirdOrderId())
                .goodsId(goodsId)
                .build());
//        sendCancelLocationIfNeeded(createOrderReq.getDataBundleId(), new Date(), null, channelCard.getImsi(), channelCard.getIccid());
        createOrderResp.setOrderID(orderUniqueId);
        createOrderResp.setPrice("0");
        createOrderResp.setTotalAmount("0");
        createOrderResp.setCurrency(currencyCode);
        Date now = new Date();
        flushCardExpireTime(Collections.singletonList(channelCard.getIccid()), now);
    }

    private ChannelDistributorDetail getChannelDistributorDetail(String corpId) {
        return channelDistributorDetailMapper.selectOne(Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                .eq(ChannelDistributorDetail::getCorpId, corpId));
    }

    /**
     * 购买加油包
     *
     * @param
     * @param req
     * @param resp
     * @param remunerationType
     * @param distributorDetail
     */
    void purchaseRefuelPackage(CreateOrderReq req, CreateOrderResp resp,
                               RemunerationTypeEnum remunerationType, ChannelDistributorDetail distributorDetail, String orderUserName) {

        if (StringUtils.isBlank(req.getRefuelingId())) {
            throw new BizException(String.format(ApiResponseEnum.REQUIRED_PARAMETER_IS_EMPTY.getMessage(), "refuelingId"), ApiResponseEnum.REQUIRED_PARAMETER_IS_EMPTY.getCode());
        }

        log.info("进入购买加油包流程");
        String packageId = req.getDataBundleId();
        Date currentTime = new Date();

        //根据套餐ID查询是否有已激活套餐，购买加油包必须要有已激活套餐
        final ChannelPackageCard channelPackageCard = channelPackageCardMapper.selectOne(Wrappers.lambdaQuery(ChannelPackageCard.class)
                .eq(ChannelPackageCard::getIccid, req.getIccid())
                .eq(ChannelPackageCard::getPackageId, packageId)
                .eq(ChannelPackageCard::getPackageStatus, PackageStatusEnum.ACTIVATED.getStatus())
                .eq(ChannelPackageCard::getPackageType, CardPackageTypeEnum.PACKAGE.getPackageType())
                .gt(ChannelPackageCard::getExpireTime, currentTime)
                .orderByAsc(ChannelPackageCard::getExpireTime));

        Optional.ofNullable(channelPackageCard).orElseThrow(() -> new BizException(ApiResponseEnum.NO_ACTIVE_PACKAGE));

        //校验/计算加油包数量
        if (ChannelPackageCard.FlowLimitTypeEnum.CYCLE_LIMIT.getValue().equals(channelPackageCard.getFlowLimitType())) {
            //周期内限量 加油包数量不限
            Optional.ofNullable(req.getQuantity()).filter(f -> (f > 0)).orElseThrow(() -> new BizException("Number of add-on pack cannot be empty", ApiResponseEnum.REQUIRED_PARAMETER_IS_EMPTY.getCode()));
        } else {
            int quantity = channelPackageCard.getKeepPeriod();
            //单日限量 加油包数量只能为1或套餐剩余天数
            if (ObjectUtils.isEmpty(req.getQuantity())) {
                //当前剩余周期
                quantity = Utils.getPackageRemainCycle(channelPackageCard.getPeriodUnit(),channelPackageCard.getKeepPeriod(),channelPackageCard.getActiveTime(),currentTime);
                req.setQuantity(quantity);
            } else if (req.getQuantity() != 1 && req.getQuantity() != quantity) {
                String msg = "Purchase quantity can only be 1" + (quantity == 1 ? "" : " or " + quantity + "(" + quantity + " is the remaining cycles of the package)Package");
                throw new BizException(msg, "1000095");
            }
        }

        log.info("购买加油包数量为：{}，通过校验", req.getQuantity());
        //查询套餐信息
        Response<PackageVO> response = pmsFeignClient.queryPackage(packageId);
        if (!ResponseResult.SUCCESS.getCode().equals(response.getCode())) {
            throw new BizException(response.getMsg(), response.getCode());
        }
        PackageVO packageVO = response.get();
        //是否支持加油包，1-是
        Optional.ofNullable(packageVO).filter(f -> "1".equals(f.getSupportRefuel()))
                .orElseThrow(() -> new BizException(ApiResponseEnum.PACKAGE_NOT_EXIST));

        // 加油包ID
        String refuelPackageId = req.getRefuelingId();
        //查询加油包信息和渠道商二次定价
        List<String> groupIds = Optional.ofNullable(distributorDetail)
                .map(m ->
                        channelPackageRelationMapper.selectList(new LambdaQueryWrapper<ChannelPackageRelation>()
                                        .select(ChannelPackageRelation::getGroupId)
                                        .eq(ChannelPackageRelation::getCorpId, distributorDetail.getCorpId())
                                        .eq(StrUtil.isNotBlank(channelPackageCard.getCooperationMode()), ChannelPackageRelation::getCooperationMode, channelPackageCard.getCooperationMode()))
                                .stream().map(ChannelPackageRelation::getGroupId).collect(Collectors.toList()))
                .orElse(new ArrayList<>());

        Response<RefuelPackageVO> refuelPackageVOResponse = pmsFeignClient.queryRefuelPackage(new QueryRefuelPack()
                .setId(refuelPackageId)
                .setPackageId(packageId)
                .setGroupIds(groupIds));
        if (!ResponseResult.SUCCESS.getCode().equals(refuelPackageVOResponse.getCode())) {
            throw new BizException(refuelPackageVOResponse.getMsg(), refuelPackageVOResponse.getCode());
        }

        RefuelPackageVO refuelPackageVO = refuelPackageVOResponse.get();
        //是否允许订购：1-是 ，审批状态：2-通过
        Optional.ofNullable(refuelPackageVO).filter(f -> "12".equals(f.getAllowSub() + f.getAuthStatus()))
                .orElseThrow(() -> new BizException(ApiResponseEnum.PACKAGE_NOT_ALLOWED_ORDER));

        // 单价
        BigDecimal price;
        // 总金额
        BigDecimal totalAmount;

        // 子渠道商单价
        BigDecimal subPrice = null;
        // 子渠道总金额
        BigDecimal subTotalAmount = null;

        OrderUserTypeEnum orderUserTypeEnum;

        boolean isSubChannel = StrUtil.isNotBlank(resp.getParentCorpId());

        boolean havePrice = StringUtils.isNotBlank(req.getPrice());

        String orderUniqueId = getOrderUniqueId();
        Date now = new Date();

        RebateTransVO rebateTransVO = RebateTransVO.builder()
                .isRefuel("1")
                .isGlobalTransactional("1")
                .build();

        BigDecimal discountAmount = BigDecimal.ZERO, amountBeforeDiscount = BigDecimal.ZERO,detailDiscountAmount = BigDecimal.ZERO, detailAmountBeforeDiscount = BigDecimal.ZERO;

        if (distributorDetail != null) {

            orderUserTypeEnum = OrderUserTypeEnum.CHANNEL;
            if (!ChannelDistributorDetail.CooperationModeEnum.A2Z.getType().equals(channelPackageCard.getCooperationMode())) {

                price = getPrice(req.getCurrency(), refuelPackageVO, resp);
                // 计算订单金额
                totalAmount = price.multiply(BigDecimal.valueOf(req.getQuantity()));

                // 渠道商，先看是否有折扣，再看是否有直接/间接收入配置，依次计算
                Integer discount = distributorDetail.getDiscount();


                boolean haveSecondaryPricing = refuelPackageVO.isSecondaryPricing();

                if (discount != null && !haveSecondaryPricing && (!havePrice || isSubChannel)) {
                    price = calculate(price, discount);
                    totalAmount = calculate(totalAmount, discount);
                    calculate(totalAmount, discount);
                    log.info("计算折扣 discount：{}", discount);
                } else {
                    //有二次定价和参数中有金额都不计算二次定价
                    log.info("金额：{}，二次定价：{},不计算折扣", havePrice, haveSecondaryPricing);
                    if (havePrice && !isSubChannel) {
                        totalAmount = new BigDecimal(req.getPrice());
                        price = totalAmount.divide(BigDecimal.valueOf(req.getQuantity()), RoundingMode.UNNECESSARY);
                        log.info("指定单价: {},指定总价：{}", price.toString(), req.getPrice());
                    }
                }

                if (isSubChannel) {
                    ChannelDistributorDetail subChannelDetail = getChannelDistributorDetail(resp.getSubCorpId());
                    subTotalAmount = totalAmount.multiply(BigDecimal.valueOf(subChannelDetail.getRefuelProfitMargin())).divide(BigDecimal.valueOf(100));
                    subPrice = price.multiply(BigDecimal.valueOf(subChannelDetail.getRefuelProfitMargin())).divide(BigDecimal.valueOf(100));

                    if (subTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                        // 扣押金
                        if (!channelDistributorsService.changeDeposit(subChannelDetail.getId(), subTotalAmount, -1)) {
                            throw new BizException(ApiResponseEnum.INSUFFICIENT_DEPOSIT);
                        }
                        channelService.channelBillRecord(BillRecordVo.builder()
                                .amount(subTotalAmount)
                                .billType("6")
                                .corpId(subChannelDetail.getCorpId())
                                .orderId(orderUniqueId)
                                .orderSubOrUnsubDate(now)
                                .build());
                    }
                }

                rebateTransVO.setCorpId(distributorDetail.getCorpId());
                rebateTransVO.setCurrencyCode(distributorDetail.getCurrencyCode());
                rebateTransVO.setOrderUniqueId(orderUniqueId);
                rebateTransVO.setOrderDate(now);
                rebateTransVO.setPackagePrice(totalAmount);
                distributorDetail.setIsRefuel("1");
                rebateTransVO.setChannelDistributorDetail(distributorDetail);

            } else {
                price = BigDecimal.ZERO;
                totalAmount = BigDecimal.ZERO;
            }

        } else {
            orderUserTypeEnum = OrderUserTypeEnum.PERSONAL;
            String currency = StringUtils.isBlank(req.getCurrency()) ? CurrencyCodeEnum.CNY.getCurrencyCode() : req.getCurrency();
            req.setCurrency(currency);

            price = getPrice(currency, refuelPackageVO, resp);
            totalAmount = price.multiply(BigDecimal.valueOf(req.getQuantity()));

            if (havePrice) {

                //折扣金额判断

                if (StringUtils.isBlank(req.getAmountBeforeDiscount()) || StringUtils.isBlank(req.getDiscountAmount())){
                    throw new BizException(ApiResponseEnum.DISCOUNT_IS_NULL);
                }

                amountBeforeDiscount = amountBeforeDiscount.add(new BigDecimal(req.getAmountBeforeDiscount()));
                discountAmount = discountAmount.add(new BigDecimal(req.getDiscountAmount()));
                BigDecimal amountAfterDiscount = new BigDecimal(req.getPrice());
                if (!(amountBeforeDiscount.compareTo(amountAfterDiscount.add(discountAmount)) == 0)){
                    throw new BizException(ApiResponseEnum.DISCOUNT_ERROR);
                }

                //折扣金额计算
                detailDiscountAmount = discountAmount.divide(BigDecimal.valueOf(req.getQuantity()), RoundingMode.UNNECESSARY);
                detailAmountBeforeDiscount = amountBeforeDiscount.divide(BigDecimal.valueOf(req.getQuantity()), RoundingMode.UNNECESSARY);

                totalAmount = new BigDecimal(req.getPrice());
                price = totalAmount.divide(BigDecimal.valueOf(req.getQuantity()), RoundingMode.UNNECESSARY);
                log.info("指定单价: {},指定总价：{}", price, req.getPrice());
            }else {
                //没有传入金额 折扣前金额为订单金额
                amountBeforeDiscount = amountBeforeDiscount.add(totalAmount);
                detailAmountBeforeDiscount = detailAmountBeforeDiscount.add(price);
            }
        }
        log.info("币种：{}, 单价：{}, 总价：{}", req.getCurrency(), price, totalAmount);


        ChannelOrder order = ChannelOrder.builder()
                .packageId(refuelPackageVO.getId())
                .packageName(refuelPackageVO.getNameCn())
                .nameEn(refuelPackageVO.getNameEn())
                .nameTw(refuelPackageVO.getNameTw())
                .orderDate(now)
                .orderName(refuelPackageVO.getNameCn())
                .orderType(OrderTypeEnum.REFUEL_PACKAGE.getOrderType())
                .periodUnit(channelPackageCard.getPeriodUnit())
                .keepPeriod(channelPackageCard.getKeepPeriod())
                .signBizId(channelPackageCard.getSignBizId())
                .limitSpeedSignBizId(channelPackageCard.getLimitSpeedSignBizId())
                .effectiveDay(channelPackageCard.getEffectiveDay())
                .cardForm(req.getCardForm())
                .iccid(req.getIccid())
                .orderChannel(req.getOrderChannel())
                .count(req.getQuantity())
                .currencyCode(req.getCurrency())
                .amount(totalAmount)
                .orderStatus(ChannelOrder.OrderStatusEnum.FINISHED.getValue())
                .payType(ChannelOrder.PayTypeEnum.DIRECT.getValue())
                .orderUniqueId(orderUniqueId)
                .orderUserId(isSubChannel ? resp.getParentCorpId() : req.getUserId())
                .orderUserType(orderUserTypeEnum.getValue())
                .orderUserName(orderUserName)
                .thirdOrderId(req.getThirdOrderId())
                .address(getAddress(req.getAddress()))
                .sendLang(req.getSendLang())
                .cardForm(req.getCardForm())
                .billFlow(StringUtils.isNotBlank(req.getTransactionCode()) ? req.getTransactionCode() : null)
                .addressee(Optional.ofNullable(req.getAddress()).map(CreateOrderReq.Address::getAddressee).orElse(null))
                .phoneNumber(Optional.ofNullable(req.getAddress()).map(CreateOrderReq.Address::getPhoneNumber).orElse(null))
                .postCode(Optional.ofNullable(req.getAddress()).map(CreateOrderReq.Address::getPostCode).orElse(null))
                .email(Optional.ofNullable(req.getAddress()).map(CreateOrderReq.Address::getEmail).orElse(null))
                .discountAmount(discountAmount)
                .amountBeforeDiscount(amountBeforeDiscount)
                .cooperationMode(channelPackageCard.getCooperationMode()).build();

        if (isSubChannel) {
            order.setSubAmount(subTotalAmount);
            order.setSubCorpId(req.getUserId());
        }

        BigDecimal flowValue = new BigDecimal(1024 * 1024).multiply(new BigDecimal(refuelPackageVO.getFlowValue()));
        if (FlowUnitEnum.GB.getValue().equals(refuelPackageVO.getFlowUnit())) {
            flowValue = new BigDecimal(1024).multiply(flowValue);
        }

        List<ChannelOrderDetail> channelOrderDetails = saveOrderRefuel(order, channelPackageCard, distributorDetail, remunerationType, price, flowValue, subPrice,detailDiscountAmount,detailAmountBeforeDiscount);


        if (distributorDetail != null && !ChannelDistributorDetail.CooperationModeEnum.A2Z.getType().equals(channelPackageCard.getCooperationMode())) {
            rebateTransVO.setOrderDetails(channelOrderDetails);
            channelRebateUtil.getRebateInfo(rebateTransVO);
        }
        try {
            flowRecharge(channelPackageCard, req.getQuantity(), flowValue);

            boolean isNeedRecover = StrUtil.isBlank(channelPackageCard.getSpeedControlId());

            if (isNeedRecover) {
                log.info("套餐没有处于控速状态，进行套餐恢复处理");
                List<ChannelSurfInfo> channelSurfInfos = channelSurfInfoMapper.selectInfoListAndImsiDistinctByHImsi(channelPackageCard.getImsi(), channelPackageCard.getPackageUniqueId());
                channelSurfInfos.sort(Comparator.comparing(obj -> ((ChannelSurfInfo) obj).getInternetType()).reversed());
                String firstUpccBizId = getFirstUpccBizId(channelPackageCard.getPackageUniqueId());
                for (ChannelSurfInfo info : channelSurfInfos) {
                    //单日恢复延时消息队列
                    String singleDelayJson = JSONObject.toJSONString(
                            SingleDayDelayVO.builder()
                                    .imsi(info.getImsi())
                                    .msisdn(channelPackageCard.getMsisdn())
                                    .iccid(channelPackageCard.getIccid())
                                    .himsi(channelPackageCard.getImsi())
                                    .cardType(info.getInternetType())
                                    .packageUniqueId(info.getPackageUniqueId())
                                    .signUpccId(firstUpccBizId)
                                    .build());
                    log.info("入rabibitmq单日恢复延时消息队列，延迟时间：{} s，mqJson: {}", recoveryExpires, singleDelayJson);

                    sendMessageWrapper.throwMessageToQueue(singleDelayJson, QueueEnum.ResumeDelayQueue, recoveryExpires * 1000);
                }
            }

            //判断是否刷新ccpc的提醒档位
            Integer count = channelOrderMapper.selectCount(Wrappers.lambdaQuery(ChannelOrder.class)
                    .eq(ChannelOrder::getOrderUniqueId, channelPackageCard.getOrderUniqueId())
                    .in(ChannelOrder::getOrderChannel, Arrays.asList("102", "105", "113")));
            if (count > 0) {
                channelPackageCardMapper.update(null,
                        Wrappers.lambdaUpdate(ChannelPackageCard.class)
                                .set(ChannelPackageCard::getFlowNoticeLevel, null)
                                .eq(ChannelPackageCard::getPackageUniqueId, channelPackageCard.getPackageUniqueId()));
            }
        } catch (Exception e) {
            if ("1".equals(rebateTransVO.getIsSuccess())) {
                rebateTransVO.getTransactionManager().rollback(rebateTransVO.getStatus());
            }
            throw e;
        }

        if ("1".equals(rebateTransVO.getIsSuccess())) {
            rebateTransVO.getTransactionManager().commit(rebateTransVO.getStatus());
        }

        resp.setOrderID(order.getOrderUniqueId());
        resp.setPrice(price.toString());
        resp.setTotalAmount(totalAmount.toString());
    }

    private String getFirstUpccBizId(String packageUniqueId) {
        CmsPackageCardUpccRelation cmsPackageCardUpccRelation = cmsPackageCardUpccRelationMapper.selectOne(Wrappers.lambdaQuery(CmsPackageCardUpccRelation.class)
                .eq(CmsPackageCardUpccRelation::getPackageUniqueId, packageUniqueId)
                .isNull(CmsPackageCardUpccRelation::getAppId)
                .orderByAsc(CmsPackageCardUpccRelation::getConsumption));
        return cmsPackageCardUpccRelation.getUpccSignId();
    }

    List<ChannelOrderDetail> saveOrderRefuel(ChannelOrder order,
                                             ChannelPackageCard channelPackageCard,
                                             ChannelDistributorDetail distributorDetail,
                                             RemunerationTypeEnum remunerationType,
                                             BigDecimal price, BigDecimal flowValue,
                                             BigDecimal subPrice,
                                             BigDecimal detailDiscountAmount,
                                             BigDecimal detailAmountBeforeDiscount) {
        channelOrderMapper.insert(order);

        BigDecimal remuneration;
        if (!ChannelDistributorDetail.CooperationModeEnum.A2Z.getType().equals(channelPackageCard.getCooperationMode())) {
            // 计算酬金
            remuneration = Optional.ofNullable(distributorDetail)
                    .map(m -> calcRemuneration(remunerationType, price, order.getOrderDate(), distributorDetail))
                    .orElse(null);
            log.info("酬金: {}", remuneration);
        } else {
            remuneration = BigDecimal.ZERO;
        }


        List<ChannelOrderDetail> channelOrderDetails = new ArrayList<>();

        for (int i = 0; i < order.getCount(); i++) {

            String packageUniqueId = Utils.cmiUuid(CmiUUID.PACKAGE_UNIQUE_ID);

            //入cms_channel_package_card
            ChannelPackageCard build = ChannelPackageCard.builder()
                    .iccid(order.getIccid())
                    .msisdn(channelPackageCard.getMsisdn())
                    .imsi(channelPackageCard.getImsi())
                    .packageType(CardPackageTypeEnum.REFUEL.getPackageType())
                    .packageId(order.getPackageId())
                    .belongPackageId(channelPackageCard.getPackageUniqueId())
                    .corpId(channelPackageCard.getCorpId())
                    .packageName(order.getPackageName())
                    .nameTw(order.getNameTw())
                    .nameEn(order.getNameEn())
                    .packageStatus(PackageStatusEnum.ACTIVATED.getStatus())
                    .periodUnit(channelPackageCard.getPeriodUnit())
                    .keepPeriod(channelPackageCard.getKeepPeriod())
                    .controlLogic(ChannelPackageCard.ControlLogicEnum.REACH_RELEASE.getValue())
                    .effectiveDay(channelPackageCard.getEffectiveDay())
                    .signBizId(channelPackageCard.getSignBizId())
                    .limitSpeedSignBizId(channelPackageCard.getLimitSpeedSignBizId())
                    .slowSpeedSignBizId(channelPackageCard.getSlowSpeedSignBizId())
                    .activeType(PackageActiveTypeEnum.AUTO.getActiveType())
                    .flowLimitType(channelPackageCard.getFlowLimitType())
                    .flowLimitSum(flowValue)
                    .surfStatus("1")
                    .packageStatus("2")
                    .activeTime(channelPackageCard.getActiveTime())
                    .expireTime(channelPackageCard.getExpireTime())
                    .orderUniqueId(order.getOrderUniqueId())
                    .packageUniqueId(packageUniqueId)
                    .cooperationMode(channelPackageCard.getCooperationMode())
                    .build();
            setActiveExpireTime(build, order.getOrderDate(), i);
            channelPackageCardMapper.insert(build);

            ChannelOrderDetail orderDetail = ChannelOrderDetail.builder()
                    .packageId(order.getPackageId())
                    .orderId(order.getId())
                    .orderDate(order.getOrderDate())
                    .orderType(order.getOrderType())
                    .packageName(order.getPackageName())
                    .nameTw(order.getNameTw())
                    .nameEn(order.getNameEn())
                    .orderChannel(order.getOrderChannel())
                    .iccid(build.getIccid())
                    .msisdn(build.getMsisdn())
                    .imsi(build.getImsi())
                    .currencyCode(order.getCurrencyCode())
                    .amount(price)
                    .orderStatus(ChannelOrder.OrderStatusEnum.FINISHED.getValue())
                    .packageUniqueId(packageUniqueId)
                    .remunerationAmount(remuneration)
                    .remunerationType(remuneration == null ? null : Optional.ofNullable(remunerationType).map(RemunerationTypeEnum::getValue).orElse(null))
                    .orderUserId(order.getOrderUserId())
                    .orderUserType(order.getOrderUserType())
                    .corpId(channelPackageCard.getCorpId())
                    .cardForm(order.getCardForm())
                    .isPromotion("2")
                    .cardType("1")
                    .thirdOrderId(order.getThirdOrderId())
                    .cooperationMode(channelPackageCard.getCooperationMode())
                    .subAmount(subPrice)
                    .subCorpId(order.getSubCorpId())
                    .discountAmount(detailDiscountAmount)
                    .amountBeforeDiscount(detailAmountBeforeDiscount)
                    .build();
            channelOrderDetailMapper.insert(orderDetail);
            channelOrderDetails.add(orderDetail);
        }
        return channelOrderDetails;
    }

    void flowRecharge(ChannelPackageCard channelPackageCard, Integer quantity, BigDecimal limitValue) {

        String packageUniqueId = channelPackageCard.getPackageUniqueId();
        if (ChannelPackageCard.FlowLimitTypeEnum.DAY_LIMIT.getValue().equals(channelPackageCard.getFlowLimitType())) {
            //单日限量
            int index = Utils.getIndex(channelPackageCard.getPeriodUnit(), channelPackageCard.getActiveTime(),new Date());
            for (int i = 0; i < quantity; i++, index++) {

                Integer count = cmsPackageDayRemainMapper.selectCount(Wrappers.lambdaQuery(CmsPackageDayRemain.class)
                        .eq(CmsPackageDayRemain::getPackageUniqueId, packageUniqueId)
                        .eq(CmsPackageDayRemain::getFlowType, "2")
                        .eq(CmsPackageDayRemain::getDayIndex, Long.valueOf(index)));
                if (count > 0) {
                    cmsPackageDayRemainMapper.updateRemainFlow(packageUniqueId, Long.valueOf(index), limitValue.longValue(), "2");
                } else {
                    cmsPackageDayRemainMapper.insert(CmsPackageDayRemain.builder()
                            .flowType("2")
                            .packageUniqueId(packageUniqueId)
                            .dayIndex(Long.valueOf(index))
                            .remainFlow(limitValue.longValue())
                            .build());
                }
            }
        } else if (ChannelPackageCard.FlowLimitTypeEnum.CYCLE_LIMIT.getValue().equals(channelPackageCard.getFlowLimitType())) {
            //周期内限量
            long flowNumber = quantity * limitValue.longValue();

            Integer count = cmsPackageCycleRemainMapper.selectCount(Wrappers.lambdaQuery(CmsPackageCycleRemain.class)
                    .eq(CmsPackageCycleRemain::getPackageUniqueId, packageUniqueId)
                    .eq(CmsPackageCycleRemain::getFlowType, "2"));
            if (count > 0) {
                cmsPackageCycleRemainMapper.updateRemainFlow(packageUniqueId, flowNumber, "2", null);
            } else {
                cmsPackageCycleRemainMapper.insert(CmsPackageCycleRemain.builder()
                        .flowType("2")
                        .packageUniqueId(packageUniqueId)
                        .remainFlow(flowNumber)
                        .build());
            }
        } else {
            throw new BizException(ApiResponseEnum.LIMIT_TYPE_IS_WRONG);
        }
    }

    private void setActiveExpireTime(ChannelPackageCard build, Date time, Integer num) {
        if (ChannelPackageCard.FlowLimitTypeEnum.DAY_LIMIT.getValue().equals(build.getFlowLimitType())) {
            Calendar activeTime = Calendar.getInstance();
            activeTime.setTime(build.getActiveTime());

            Calendar orderDate = Calendar.getInstance();
            orderDate.setTime(time);
            if (build.getPeriodUnit().equals(PackagePeriodUnitEnum.TWENTY_FOUR_HOURS.getValue().toString())||
            build.getPeriodUnit().equals(PackagePeriodUnitEnum.NATURAL_DAY.getValue().toString())) {
                orderDate.add(Calendar.DATE, num);
            }else if (build.getPeriodUnit().equals(PackagePeriodUnitEnum.NATURAL_MONTH.getValue().toString())){
                orderDate.add(Calendar.MONTH, num);
            }else{
                orderDate.add(Calendar.YEAR, num);
            }


            activeTime.set(Calendar.DATE, orderDate.get(Calendar.DATE));
            activeTime.set(Calendar.MONTH, orderDate.get(Calendar.MONTH));
            activeTime.set(Calendar.YEAR, orderDate.get(Calendar.YEAR));

            if (PackagePeriodUnitEnum.TWENTY_FOUR_HOURS.getValue().toString().equals(build.getPeriodUnit())) {
                if (orderDate.getTime().before(activeTime.getTime())) {
                    activeTime.add(Calendar.DATE, -1);
                }
                if (num == 0) {
                    build.setActiveTime(time);
                } else {
                    build.setActiveTime(activeTime.getTime());
                }
                activeTime.add(Calendar.DATE, 1);
                build.setExpireTime(activeTime.getTime());
            } else if(PackagePeriodUnitEnum.NATURAL_DAY.getValue().toString().equals(build.getPeriodUnit())){
                activeTime.set(Calendar.SECOND, 0);
                activeTime.set(Calendar.MINUTE, 0);
                activeTime.set(Calendar.HOUR_OF_DAY, 0);

                if (orderDate.getTime().before(activeTime.getTime())) {
                    activeTime.add(Calendar.DATE, -1);
                }
                if (num == 0) {
                    build.setActiveTime(time);
                } else {
                    build.setActiveTime(activeTime.getTime());
                }
                activeTime.add(Calendar.DATE, 1);
                activeTime.add(Calendar.SECOND, -1);
                build.setExpireTime(activeTime.getTime());
            } else if (PackagePeriodUnitEnum.NATURAL_MONTH.getValue().toString().equals(build.getPeriodUnit())) {
                activeTime.set(Calendar.SECOND, 0);
                activeTime.set(Calendar.MINUTE, 0);
                activeTime.set(Calendar.HOUR_OF_DAY, 0);
                activeTime.set(Calendar.DATE, 1);

                if (orderDate.getTime().before(activeTime.getTime())) {
                    activeTime.add(Calendar.DATE, -1);
                }
                if (num == 0) {
                    build.setActiveTime(time);
                } else {
                    build.setActiveTime(activeTime.getTime());
                }
                // Set expire time to the last day of the next month at 23:59:59
                activeTime.add(Calendar.MONTH, 1);
                activeTime.add(Calendar.SECOND, -1);
                build.setExpireTime(activeTime.getTime());
            } else if (PackagePeriodUnitEnum.NATURAL_YEAR.getValue().toString().equals(build.getPeriodUnit())) {
                activeTime.set(Calendar.SECOND, 0);
                activeTime.set(Calendar.MINUTE, 0);
                activeTime.set(Calendar.HOUR_OF_DAY, 0);
                activeTime.set(Calendar.DATE, 1);
                activeTime.set(Calendar.MONTH, Calendar.getInstance().getMinimum(Calendar.MONTH));

                if (orderDate.getTime().before(activeTime.getTime())) {
                    activeTime.add(Calendar.DATE, -1);
                }
                if (num == 0) {
                    build.setActiveTime(time);
                    activeTime.set(Calendar.MONTH, Calendar.getInstance().getMaximum(Calendar.MONTH));
                    activeTime.set(Calendar.DATE, Calendar.getInstance().getMaximum(Calendar.DATE));
                    activeTime.set(Calendar.SECOND, Calendar.getInstance().getMaximum(Calendar.SECOND));
                    activeTime.set(Calendar.MINUTE, Calendar.getInstance().getMaximum(Calendar.MINUTE));
                    activeTime.set(Calendar.HOUR_OF_DAY, Calendar.getInstance().getMaximum(Calendar.HOUR_OF_DAY));
                    build.setExpireTime(activeTime.getTime());
                } else {
                    build.setActiveTime(activeTime.getTime());
                    activeTime.set(Calendar.MONTH, Calendar.getInstance().getMaximum(Calendar.MONTH));
                    activeTime.set(Calendar.DATE, Calendar.getInstance().getMaximum(Calendar.DATE));
                    activeTime.set(Calendar.SECOND, Calendar.getInstance().getMaximum(Calendar.SECOND));
                    activeTime.set(Calendar.MINUTE, Calendar.getInstance().getMaximum(Calendar.MINUTE));
                    activeTime.set(Calendar.HOUR_OF_DAY, Calendar.getInstance().getMaximum(Calendar.HOUR_OF_DAY));
                    build.setExpireTime(activeTime.getTime());
                }
            } else {
                throw new BizException("套餐周期单位不正确");
            }
        } else {
            build.setActiveTime(time);
        }
    }

    /**
     * 购买套餐或加油包
     *
     * @param createOrderReq
     * @param createOrderResp
     * @param distributorDetail 渠道商信息
     * @param remunerationType  酬金类型
     */
    void purchasePackage(CreateOrderReq createOrderReq, CreateOrderResp createOrderResp,
                         ChannelDistributorDetail distributorDetail, RemunerationTypeEnum remunerationType,
                         String orderUserName, String goodsId) {
        //是否是子渠道商
        boolean isSubChannel = StrUtil.isNotBlank(createOrderResp.getSubCorpId());

        //是否为购买加油包
        final boolean isRefuel = "0".equals(createOrderReq.getIsRefuel());

        //
        BigDecimal subPackagePrice = null;

        List<String> iccids = Collections.singletonList(createOrderReq.getIccid());
        //套餐id
        String dataBundleId = createOrderReq.getDataBundleId();
        // 用ICCID查询客户与卡关系表
        final LambdaQueryWrapper<ChannelCard> queryWrapper = Wrappers.lambdaQuery(ChannelCard.class)
                .eq(ChannelCard::getCardType, CardTypeEnum.H_CARD.getType())
                .in(ChannelCard::getIccid, iccids);
        List<ChannelCard> channelCards = channelCardMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(channelCards)) {
            // 不存在客户与卡关系
            throw new BizException(ApiResponseEnum.NON_CMI_CARD_LIMIT);
        }

        final boolean isChannel = distributorDetail != null;
        final String userId = createOrderReq.getUserId();
        if (isChannel) {
            final String iccid = createOrderReq.getIccid();
            log.debug("渠道商用户，判断卡归属 iccid: {}", iccid);
            final boolean match = channelCards.stream().anyMatch(channelCard -> !channelCard.getCorpId().equals(userId));
            if (match) {
                log.debug("卡不归属当前渠道商用户 iccid: {}", iccid);
                throw new BizException("The card has been attributed to other customers", ApiResponseEnum.CARD_NOT_BELONG_CHANNEL.getCode());
            }
            if (!isSubChannel) {
                //根据卡的合作模式和传入的套餐，判断改套餐是否跟卡的合作模式相同，若不同则提示“套餐的合作模式必须跟卡一致”
                channelCards.forEach(card -> {
                    List<String> groupIds = channelPackageRelationMapper.getGroupIds(card.getCorpId(), card.getCooperationMode());
                    if (CollectionUtils.isEmpty(groupIds) && !ChannelDistributorDetail.CooperationModeEnum.A2Z.getType().equals(card.getCooperationMode()) && !isRefuel) {
                        log.debug("当前合作模式没有可用套餐组");
                        throw new BizException(ApiResponseEnum.PACKAGE_IS_NOT_ALLOWED_ORDERED);
                    }
                    boolean isContainPackage = Response.getAndCheckRemoteData(pmsFeignClient.groupContainPackage(groupIds, dataBundleId, card.getCorpId(), card.getCooperationMode()));
                    if (!isContainPackage && !isRefuel) {
                        throw new BizException(ApiResponseEnum.PACKAGE_IS_NOT_ALLOWED_ORDERED);
                    }
                });
            } else {
                // 判断套餐是否是子渠道的资源若不是直接返回提示“不允许购买该套餐”
                for (ChannelCard card : channelCards) {
                    CmsChannelPackageDetail channelPackageDetail = channelPackageDetailMapper.selectOne(Wrappers.lambdaQuery(CmsChannelPackageDetail.class)
                            .eq(CmsChannelPackageDetail::getPackageId, dataBundleId)
                            .eq(CmsChannelPackageDetail::getCorpId, card.getCorpId()));
                    if (ObjectUtil.isNull(channelPackageDetail)) {
                        throw new BizException(ApiResponseEnum.PACKAGE_IS_NOT_ALLOWED_ORDERED);
                    }
                    subPackagePrice = channelPackageDetail.getPackagePrice();
                }

            }

            log.debug("渠道商购买代销套餐/加油包，判断代销合约时间是否到期");
            channelCards.forEach(channelCard -> {
                Date contractEndTime = distributorDetail.getContractEndTime();
                if (ChannelDistributorDetail.CooperationModeEnum.CONSIGNMENT.getType().equals(channelCard.getCooperationMode())
                        && contractEndTime != null && contractEndTime.before(new Date())) {
                    log.warn("代销合约时间已到期，不允许购买代销套餐/加油包");
                    throw new BizException(ApiResponseEnum.PACKAGE_IS_NOT_ALLOWED_ORDERED);
                }
            });
        }
        List<HcardInfo> hcardInfos = channelCards.stream().map(ChannelCard::getIccid).map(this::checkIfPermitPurchase).collect(Collectors.toList());

        if (isRefuel) {
            // 加油包 渠道商购买算直接收入，个人购买不算酬金
            remunerationType = isChannel ? RemunerationTypeEnum.DIRECT_INCOME : null;
            if (StringUtils.isBlank(createOrderReq.getCardForm())) {
                createOrderReq.setCardForm(hcardInfos.get(0).getCardForm());
            }
            createOrderContext.get().setPurchaseType(CreateOrderContext.PurchaseType.REFUEL_PACKAGE);
            purchaseRefuelPackage(createOrderReq, createOrderResp, remunerationType, distributorDetail, orderUserName);
            return;
        }
        // 查询套餐
        PackageVO packageVO = getPackage(dataBundleId);
        // 促销套餐
        Integer quantity = createOrderReq.getQuantity();
        final String isPromotion = packageVO.getIsPromotion();
        if (PackageIsPromotionEnum.YES.matches(isPromotion)) {
            Integer saleLimit = packageVO.getSaleLimit();
            log.debug("该套餐为促销套餐 packageId: {}，saleLimit：{}", packageVO.getId(), saleLimit);
            channelCards.stream().map(ChannelCard::getIccid).forEach(iccid -> checkPromotionPackage(quantity, iccid, packageVO));
        }

        common.checkPackageCardForm(packageVO.getId(), channelCards.get(0).getCardForm());

        createOrderContext.get().setChannelCards(channelCards);

        //渠道商购买绑定套餐
        if ("2".equals(packageVO.getDeductionModel())) {
            createOrderContext.get().setPurchaseType(CreateOrderContext.PurchaseType.BIND_PACKAGE);
            purchaseBindPackage(createOrderReq, createOrderResp, distributorDetail, packageVO, channelCards.get(0), goodsId);
            return;
        }

        if (createOrderReq.getSetActiveTime() != null) {
            Date activeTime = createOrderReq.getSetActiveTime();

            Date currentTime = new Date();

            Calendar calc = Calendar.getInstance();
            calc.setTime(currentTime);
            calc.add(Calendar.DATE, allowUpdateTime);
            Date maxTime = calc.getTime();

            Optional.ofNullable(activeTime)
                    .filter(f -> f.after(currentTime))
                    .filter(f -> f.before(maxTime))
                    .orElseThrow(() -> new BizException(ApiResponseEnum.ACTIVATION_DATA_IS_WRONG));
        } else {
            createOrderReq.setSetActiveTime(null);
        }

        BigDecimal price, totalAmount, subTotalAmount = null;
        BigDecimal discountAmount = BigDecimal.ZERO, amountBeforeDiscount = BigDecimal.ZERO,detailDiscountAmount = BigDecimal.ZERO, detailAmountBeforeDiscount = BigDecimal.ZERO;
        final int cardsCount = channelCards.size();
        OrderUserTypeEnum orderUserTypeEnum;
        final String currency = createOrderReq.getCurrency();
        final String orderReqPrice = createOrderReq.getPrice();
        final int orderDetailCount = quantity * cardsCount;
        boolean isChannelCreate = packageVO.getIsChannelCreate().equals("1");
        String cooperationMode = channelCards.get(0).getCooperationMode();
        boolean isA2Z = ChannelDistributorDetail.CooperationModeEnum.A2Z.getType().equals(cooperationMode);
        ChannelDistributorDetail subChanneldetail = null;

        String orderUniqueId = getOrderUniqueId();
        Date orderDate = new Date();

        RebateTransVO rebateTransVO = RebateTransVO.builder()
                .isGlobalTransactional("1")
                .build();


        if (isChannel) {
            // 渠道商用户
            Map<String, List<String>> packageGroupDetailMap = new HashMap<>();
            String currencyCode = distributorDetail.getCurrencyCode();
            if (isChannelCreate) {
                checkChannelPermission(userId, packageVO.getCorpId());
            } else {
                packageGroupDetailMap = checkChannelPermission(currencyCode, createOrderReq, createOrderResp, cooperationMode);
            }
            orderUserTypeEnum = OrderUserTypeEnum.CHANNEL;

            if (StringUtils.isNotBlank(orderReqPrice) && !isSubChannel && !isChannelCreate && !isA2Z) {
                totalAmount = new BigDecimal(orderReqPrice);
                price = totalAmount.divide(BigDecimal.valueOf(orderDetailCount), RoundingMode.UNNECESSARY);
                setCurrency(currency, createOrderResp);
            } else if (isChannelCreate || isA2Z) {
                price = BigDecimal.ZERO;
                totalAmount = BigDecimal.ZERO;
                setCurrency(currency, createOrderResp);
            } else {
                // 渠道商，先看是否有二次定价，再看是否有折扣，再看是否有直接/间接收入配置，依次计算
                final String groupId = packageGroupDetailMap.entrySet().stream()
                        .filter(entry -> entry.getValue().contains(dataBundleId))
                        .findFirst()
                        .map(Map.Entry::getKey)
                        .get();
                Response<PackageGroupDetailDTO> packageGroupDetailDTOResponse = pmsFeignClient.getPackageGroupDetail(groupId, dataBundleId);
                if (!ResponseResult.SUCCESS.getCode().equals(packageGroupDetailDTOResponse.getCode())) {
                    throw new BizException(packageGroupDetailDTOResponse.getMsg(), packageGroupDetailDTOResponse.getCode());
                }
                final PackageGroupDetailDTO groupDetailDTO = packageGroupDetailDTOResponse.get();
                if (groupDetailDTO.isTwicePricing()) {
                    log.debug("存在二次定价 packageId: {}", dataBundleId);
                    price = getPrice(currencyCode, groupDetailDTO, createOrderResp);
                    // 计算订单金额
                    totalAmount = price.multiply(BigDecimal.valueOf(orderDetailCount));
                } else {
                    price = getPrice(currencyCode, packageVO, createOrderResp);
                    // 计算订单金额
                    totalAmount = price.multiply(BigDecimal.valueOf(orderDetailCount));
                    final Integer discount = distributorDetail.getDiscount();
                    if (discount != null) {
                        price = calculate(price, discount);
                        totalAmount = calculate(totalAmount, discount);
                    }
                }
            }
            Long id = distributorDetail.getId();
            String corpId = distributorDetail.getCorpId();

            if (isSubChannel) {
                //计算子渠道商金额
                String subCorpId = createOrderResp.getSubCorpId();
                subChanneldetail = channelDistributorDetailMapper.selectOne(Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                        .eq(ChannelDistributorDetail::getCorpId, subCorpId));
                if (subPackagePrice != null) {
                    subTotalAmount = subPackagePrice.multiply(BigDecimal.valueOf(orderDetailCount));
                } else {
                    subTotalAmount = totalAmount.multiply(BigDecimal.valueOf(subChanneldetail.getDiscount())).divide(BigDecimal.valueOf(100));
                }
                if (subTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                    //扣除子渠道商押金
                    if (!channelDistributorsService.changeDeposit(subChanneldetail.getId(), subTotalAmount, -1)) {
                        throw new BizException(ApiResponseEnum.INSUFFICIENT_DEPOSIT);
                    }

                    // 写子渠道流水
                    channelService.channelBillRecord(BillRecordVo.builder()
                            .corpId(subCorpId)
                            .billType("5")
                            .amount(subTotalAmount)
                            .orderId(orderUniqueId)
                            .orderSubOrUnsubDate(orderDate)
                            .cooperationMode(cooperationMode)
                            .build());
                }
                createOrderResp.setSubTotalAmount(subTotalAmount);
            }
            rebateTransVO.setCorpId(corpId);
            rebateTransVO.setCurrencyCode(distributorDetail.getCurrencyCode());
            rebateTransVO.setCooperationMode(cooperationMode);
            rebateTransVO.setOrderUniqueId(orderUniqueId);
            rebateTransVO.setOrderDate(orderDate);
            rebateTransVO.setPackagePrice(totalAmount);
            rebateTransVO.setChannelDistributorDetail(distributorDetail);
            // 扣押金

        } else {
            orderUserTypeEnum = OrderUserTypeEnum.PERSONAL;
            if (StringUtils.isNotBlank(orderReqPrice)) {
                //折扣金额判断

                if (StringUtils.isBlank(createOrderReq.getAmountBeforeDiscount()) || StringUtils.isBlank(createOrderReq.getDiscountAmount())){
                    throw new BizException(ApiResponseEnum.DISCOUNT_IS_NULL);
                }

                amountBeforeDiscount = amountBeforeDiscount.add(new BigDecimal(createOrderReq.getAmountBeforeDiscount()));
                discountAmount = discountAmount.add(new BigDecimal(createOrderReq.getDiscountAmount()));
                BigDecimal amountAfterDiscount = new BigDecimal(createOrderReq.getPrice());
                if (!(amountBeforeDiscount.compareTo(amountAfterDiscount.add(discountAmount)) == 0)){
                    throw new BizException(ApiResponseEnum.DISCOUNT_ERROR);
                }

                //折扣金额计算
                detailDiscountAmount = discountAmount.divide(BigDecimal.valueOf(orderDetailCount), RoundingMode.UNNECESSARY);
                detailAmountBeforeDiscount = amountBeforeDiscount.divide(BigDecimal.valueOf(orderDetailCount), RoundingMode.UNNECESSARY);

                totalAmount = new BigDecimal(orderReqPrice);
                setCurrency(currency, createOrderResp);
                price = totalAmount.divide(BigDecimal.valueOf(orderDetailCount), RoundingMode.UNNECESSARY);
            } else {
                price = getPrice(currency, packageVO, createOrderResp);
                // 计算订单金额
                totalAmount = price.multiply(BigDecimal.valueOf(orderDetailCount));
                detailAmountBeforeDiscount = detailAmountBeforeDiscount.add(price);
                amountBeforeDiscount = amountBeforeDiscount.add(totalAmount);
            }
        }

        // 写入订单表
        ChannelOrder order = saveOrder(iccids, channelCards.iterator().next(), createOrderReq, packageVO,
                orderUserTypeEnum, OrderTypeEnum.PACKAGE, packageVO.getNameCn(), totalAmount,
                ChannelOrder.OrderStatusEnum.FINISHED, createOrderResp, orderUserName, createOrderReq.getSetActiveTime(),
                goodsId, isChannel, orderUniqueId, orderDate, amountBeforeDiscount,discountAmount);
        //实名制
        if (!isChannel) {
            realNameSysCardPackageEsim(
                    false,
                    CardFormEnum.ESIM.matches(order.getCardForm()),
                    order.getOrderType(),
                    channelCards.iterator().next().getImsi(),
                    order.getIccid(),
                    channelCards.iterator().next().getMsisdn(),
                    order.getOrderUniqueId(),
                    String.valueOf(order.getId()),
                    order.getPackageId()
            );
        }
        // 更新主卡语言为订单语言
        if (StringUtils.isNotBlank(createOrderReq.getSendLang())) {
            Response cardBatchupdateSendlangResponse = pmsFeignClient.cardBatchupdateSendlang(null, iccids, createOrderReq.getSendLang());
            if (!ResponseResult.SUCCESS.getCode().equals(cardBatchupdateSendlangResponse.getCode())) {
                throw new BizException(cardBatchupdateSendlangResponse.getMsg(), cardBatchupdateSendlangResponse.getCode());
            }
        }

        createOrderContext.get().setOrderDate(orderDate);
        createOrderContext.get().setPurchaseType(CreateOrderContext.PurchaseType.COMMON_PACKAGE);
        for (ChannelCard channelCard : channelCards) {
            BigDecimal remuneration = null;
            final String corpId = channelCard.getCorpId();
            final ChannelDistributorDetail channelDistributorDetail = getChannelDistributorDetail(corpId);
            if (channelDistributorDetail != null) {
                // 计算酬金
                if (orderUserTypeEnum == OrderUserTypeEnum.CHANNEL) {
                    log.debug("卡归属渠道商corpId: {}, 当前购买客户corpId: {}", corpId, userId);
                    if (userId.equals(corpId)) {
                        remuneration = calcRemuneration(remunerationType, price, orderDate, channelDistributorDetail);
                    }
                } else {
                    remuneration = calcRemuneration(remunerationType, price, orderDate, channelDistributorDetail);
                }
            }
            String packageUniqueId = null;
            BigDecimal subPrice = null;
            //计算子渠道商金额
            if (isChannel && isSubChannel) {
                if (subPackagePrice != null) {
                    subPrice = subPackagePrice;
                } else {
                    subPrice = price.multiply(BigDecimal.valueOf(subChanneldetail.getDiscount())).divide(BigDecimal.valueOf(100));
                }
            }

            List<ChannelOrderDetail> orderDetails = new ArrayList<>();

            for (int i = 0; i < quantity; i++) {

                packageUniqueId = getPackageUniqueId();
                // 写入客户卡与套餐信息
                saveChannelPackageCard(packageVO, orderUniqueId, order, channelCard, packageUniqueId, createOrderReq.getSetActiveTime(), isChannel ? distributorDetail.getA2zCardUseSwitch() : null);
                // 保存订单详情
                orderDetails.add(saveOrderDetail(remunerationType,
                                price,
                                order,
                                channelCard,
                                remuneration,
                                packageUniqueId,
                                isPromotion,
                                createOrderReq.getSetActiveTime(),
                                subPrice,
                                detailAmountBeforeDiscount,
                                detailDiscountAmount));
            }

            if (isChannel) {
                rebateTransVO.setOrderDetails(orderDetails);
                channelRebateUtil.getRebateInfo(rebateTransVO);
            }

            try {
                String iccid = channelCard.getIccid();
                Response<HcardInfo> getCardByIccidResponse = pmsFeignClient.getCardByIccid(iccid);
                if (!ResponseResult.SUCCESS.getCode().equals(getCardByIccidResponse.getCode())) {
                    throw new BizException(getCardByIccidResponse.getMsg(), getCardByIccidResponse.getCode());
                }
                final HcardInfo card = getCardByIccidResponse.get();
                if (card == null) {
                    log.warn("主卡不存在 iccid: {}", iccid);
                    return;
                }
                final String sendLang = StringUtils.isBlank(createOrderReq.getSendLang()) ? card.getSendLang() : createOrderReq.getSendLang();
                String packageName = getPackageName(packageVO, sendLang);
                smsService.sendPurchaseSms(card, packageName, orderUniqueId, packageUniqueId);
            } catch (Exception e) {
                if ("1".equals(rebateTransVO.getIsSuccess())) {
                    rebateTransVO.getTransactionManager().rollback(rebateTransVO.getStatus());
                }
                throw e;
            }

            if ("1".equals(rebateTransVO.getIsSuccess())) {
                rebateTransVO.getTransactionManager().commit(rebateTransVO.getStatus());
            }

        }
        createOrderResp.setOrderID(orderUniqueId);
        createOrderResp.setPrice(price.toString());
        createOrderResp.setTotalAmount(totalAmount.toString());
        // 刷新主卡过期时间

        try {
            flushCardExpireTime(iccids, orderDate);
        } catch (Exception e) {
            log.error("刷新主卡过期时间失败", e);
        }

    }

    public Map<String, List<String>> checkChannelPermission(String currencyCode, CreateOrderReq createOrderReq, CreateOrderResp createOrderResp, String cooperationMode) {

        String currency = createOrderReq.getCurrency();
        String userId;
        if (StrUtil.isNotBlank(createOrderResp.getSubCorpId())) {
            userId = createOrderResp.getParentCorpId();
        } else {
            userId = createOrderReq.getUserId();
        }
        if (StringUtils.isNotBlank(currency)
                && !currency.equals(currencyCode)) {
            throw new BizException("购买失败，币种不匹配", ApiResponseEnum.CURRENCY_IS_MISMATCH.getCode());
        }
        Map<String, List<String>> packageGroupDetailMap = packageService.queryPackagesByCorpId(userId, cooperationMode);
        if (org.springframework.util.CollectionUtils.isEmpty(packageGroupDetailMap)) {
            log.debug("渠道商corpId: {} 无可购买套餐", userId);
            throw new BizException(ApiResponseEnum.PACKAGE_IS_NOT_ALLOWED_ORDERED);
        }
        Set<String> packageIds = new HashSet<>();
        packageGroupDetailMap.values().forEach(packageIds::addAll);
        log.debug("packageIds: {}", packageIds);
        if (!packageIds.contains(createOrderReq.getDataBundleId())) {
            log.debug("渠道商corpId: {} 可购买套餐不包括 dataBundleId: {}", userId, createOrderReq.getDataBundleId());
            throw new BizException(ApiResponseEnum.PACKAGE_IS_NOT_ALLOWED_ORDERED);
        }
        return packageGroupDetailMap;
    }

    public void checkChannelPermission(String cropId, String packageAscriptionId) {
        if (!cropId.equals(packageAscriptionId)) {
            throw new BizException(ApiResponseEnum.PACKAGE_IS_NOT_ALLOWED_ORDERED);
        }
    }


    /**
     * 发送CancelLocation处理流程
     *
     * @param dataBundleId
     * @param orderDate
     * @param corpId       ?
     * @param imsi
     */

    @Async
    public void sendCancelLocationIfNeeded(String dataBundleId, Date orderDate, String corpId, String imsi, String iccid) {
        HimsiStatusAndLocationVO locationVO = new HimsiStatusAndLocationVO();
        locationVO.setImsi(imsi);
        try {
            HimsiStatusAndLocationDTO userLocation = packageEndService.getUserLocation(locationVO);
            final String mcc = userLocation.getMobileCountryCode();
            if (StrUtil.isNotBlank(mcc)) {
                List<ChannelPackageCard> otherAvailablePackages = channelPackageCardMapper.selectList(Wrappers.lambdaQuery(ChannelPackageCard.class)
                        .eq(ChannelPackageCard::getImsi, imsi)
                        .and(wrapper ->
                                wrapper.and(wrapper1 -> wrapper1.eq(ChannelPackageCard::getPackageStatus, PackageStatusEnum.ACTIVATING.getStatus())
                                                .gt(ChannelPackageCard::getEffectiveDay, orderDate))
                                        .or(wrapper2 -> wrapper2.eq(ChannelPackageCard::getPackageStatus, PackageStatusEnum.ACTIVATED.getStatus())
                                                .gt(ChannelPackageCard::getExpireTime, orderDate))));

                if (CollectionUtils.isEmpty(otherAvailablePackages)) {
                    log.debug("没有激活中/已激活套餐");
                    log.debug("查询到当前位置mcc: {}, 判断套餐 packageId: {} 是否覆盖", mcc, dataBundleId);
                    List<String> packageIds = pmsFeignClient.checkPackageInMcc(Collections.singletonList(dataBundleId), mcc).get();
                    if (packageIds.contains(dataBundleId)) {
                        flowPoolServiceImpl.getHCardOrVCard(iccid);
                    }
                } else {
                    List<String> packageIdList = otherAvailablePackages.stream().map(ChannelPackageCard::getPackageId).distinct().collect(Collectors.toList());
                    log.debug("有激活中/已激活套餐");
                    log.debug("查询到当前位置mcc: {}, 判断套餐 packageId: {} 是否覆盖", mcc, packageIdList);
                    List<String> packageIds = pmsFeignClient.checkPackageInMcc(packageIdList, mcc).get();
                    List<CmsFlowpoolCountryRelation> flowpoolIds = flowpoolCountryRelationMapper.selectList(Wrappers.lambdaQuery(CmsFlowpoolCountryRelation.class)
                            .eq(CmsFlowpoolCountryRelation::getMcc, mcc)
                            .in(CmsFlowpoolCountryRelation::getFlowPoolId, packageIdList));
                    if (CollectionUtils.isEmpty(packageIds) && flowpoolIds.size() == 0) {
                        packageIds = pmsFeignClient.checkPackageInMcc(Collections.singletonList(dataBundleId), mcc).get();
                        if (packageIds.contains(dataBundleId)) {
                            flowPoolServiceImpl.getHCardOrVCard(iccid);
                        } else {
                            log.debug("此套餐不支持当前位置");
                        }
                    } else {
                        log.debug("当前位置有激活中或者已激活套餐");
                        final List<String> availablePackageIds = packageIds;
                        List<ChannelPackageCard> whiteListPackageCards = otherAvailablePackages.stream().filter(channelPackageCard -> availablePackageIds.contains(channelPackageCard.getPackageId())).filter(channelPackageCard -> "1".equals(channelPackageCard.getWhitelistPackage())).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(whiteListPackageCards)) {
                            // 有已激活套餐 -> 筛选出白名单套餐，如果当前购买套餐支持所在位置，则释放白名单套餐
                            packageIds = pmsFeignClient.checkPackageInMcc(Collections.singletonList(dataBundleId), mcc).get();
                            if (packageIds.contains(dataBundleId)) {
                                log.debug("当前有白名单套餐，且购买的套餐支持当前位置，白名单套餐进入延期流程");
                                for (ChannelPackageCard whiteListPackageCard : whiteListPackageCards) {
                                    if (packageOverdueService.updateExpireTimeAndSurfEndTime(whiteListPackageCard, new Date())) {
                                        packageOverdueService.logDelayRecord(whiteListPackageCard, imsi, mcc, unactivatedPackageChain);
                                        whiteListPackageCard.setUpdateOverdueTime(false);
                                        packageOverdueService.handleActivatedOne(whiteListPackageCard);
                                    }
                                }
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.warn("向imsi: {}发送HSS cancelLocation失败，错误堆栈：{}", imsi, e);
        }

    }

    private void setCurrency(String currency, CreateOrderResp createOrderResp) {
        if (StringUtils.isBlank(currency)) {
            currency = CurrencyCodeEnum.CNY.getCurrencyCode();
        }
        createOrderResp.setCurrency(currency);
    }

    private String getCardName(String sendLang) {
        String result;
        if (HcardInfo.SendLangEnum.ZH_HK.getValue().equals(sendLang)) {
            result = "全球數據卡及";
        } else if (HcardInfo.SendLangEnum.EN_US.getValue().equals(sendLang)) {
            result = "SIM card and ";
        } else {
            result = "全球数据卡及";
        }
        return result;
    }

    public String getPackageName(PackageVO packageVO, String sendLang) {
        String packageName;
        if (HcardInfo.SendLangEnum.ZH_HK.getValue().equals(sendLang)) {
            packageName = packageVO.getNameTw();
        } else if (HcardInfo.SendLangEnum.EN_US.getValue().equals(sendLang)) {
            packageName = packageVO.getNameEn();
        } else {
            packageName = packageVO.getNameCn();
        }
        return packageName;
    }

    /**
     * 促销套餐检查
     *
     * @param quantity  购买数量
     * @param iccid
     * @param packageVO
     * @return
     */
    private void checkPromotionPackage(Integer quantity, String iccid, PackageVO packageVO) {
        final String packageId = packageVO.getId();
        log.debug("检查是否超过购买份数限制 iccid: {}", iccid);
        Integer saleLimit = packageVO.getSaleLimit();
        final Integer orderedCount = channelOrderDetailMapper.selectCount(Wrappers.lambdaQuery(ChannelOrderDetail.class)
                .eq(ChannelOrderDetail::getIccid, iccid)
                .eq(ChannelOrderDetail::getPackageId, packageId)
                .eq(ChannelOrderDetail::getIsPromotion, PackageIsPromotionEnum.YES.getValue())
                .in(ChannelOrderDetail::getOrderStatus, ChannelOrder.OrderStatusEnum.FINISHED.getValue(),
                        ChannelOrder.OrderStatusEnum.UNSUBSCRIBE_NOT_AUDIT.getValue()));
        if ((quantity + orderedCount) > saleLimit) {
            throw new BizException(ApiResponseEnum.EXCEEDED_THE_NUMBER);
        }
    }

    /**
     * 检查主卡状态是否正常及是否为合作发卡
     *
     * @param iccid
     */
    private HcardInfo checkIfPermitPurchase(String iccid) {
        log.debug("判断主卡状态是否正常及是否为合作发卡 iccid: {}", iccid);
        Response<HcardInfo> response = pmsFeignClient.getCardByIccid(iccid);
        if (!ResponseResult.SUCCESS.getCode().equals(response.getCode())) {
            throw new BizException(response.getMsg(), response.getCode());
        }
        HcardInfo card = response.get();
        if ("2".equals(card.getType())) {
            throw new BizException("The card type is cooperative card issuance, and package/add-on purchase is not allowed", "1000161");
        }
        if (!CardStatus.NORMAL.getStatus().equals(card.getStatus())) {
            throw new BizException("The primary card status is not normal", ApiResponseEnum.CARD_IS_UNUSUAL.getCode());
        }
        return card;
    }

    /**
     * 计算酬金
     *
     * @param remunerationType
     * @param amount
     * @param orderDate
     * @param channelDetail
     * @return
     */
    private BigDecimal calcRemuneration(RemunerationTypeEnum remunerationType, BigDecimal amount, Date orderDate,
                                        ChannelDistributorDetail channelDetail) {
        BigDecimal remuneration = null;
        if (remunerationType == RemunerationTypeEnum.DIRECT_INCOME) {
            // 直接收入
            log.debug("酬金类型为直接收入");
            Integer directRatio = channelDetail.getDirectRatio();
            if (directRatio != null) {
                remuneration = calculate(amount, directRatio);
            }
            return remuneration;
        }
        if (remunerationType == RemunerationTypeEnum.INDIRECT_INCOME) {
            // 间接收入
            log.debug("酬金类型为间接收入");
            Integer indirectRatio = channelDetail.getIndirectRatio();
            if (indirectRatio != null) {
                remuneration = calculate(amount, indirectRatio);
            }
        }
        return remuneration;
    }

    /**
     * 刷新主卡过期时间为订购后N个月
     *
     * @param iccids
     * @param orderDate
     */
    private void flushCardExpireTime(List<String> iccids, Date orderDate) {
        Response.getAndCheckRemoteData(pmsFeignClient.updateCardExpireTime(new UpdateCardExpireTimeReq(iccids,
                orderDate)));
    }

    /**
     * 购买卡+套餐
     *
     * @param createOrderReq
     * @param createOrderResp
     * @param orderUserName
     */
    private void purchaseCardAndPackage(CreateOrderReq createOrderReq, CreateOrderResp createOrderResp, String orderUserName, String goodsId) {
        String cardForm = createOrderReq.getCardForm();
        if (StringUtils.isBlank(cardForm)) {
            throw new BizException("cardForm不能为空");
        }
        final boolean isEsimCard = CardFormEnum.ESIM.matches(cardForm);
        createOrderContext.get().setEsimCard(isEsimCard);
        createOrderContext.get().setPurchaseType(CreateOrderContext.PurchaseType.CARD_AND_PACKAGE);
        final String iccid = createOrderReq.getIccid();
        // 查询套餐
        String dataBundleId = createOrderReq.getDataBundleId();
        PackageVO packageVO = getPackage(dataBundleId);

        common.checkPackageCardForm(packageVO.getId(), cardForm);

        if (createOrderReq.getSetActiveTime() != null) {
            Date activeTime = createOrderReq.getSetActiveTime();

            Date currentTime = new Date();

            Calendar calc = Calendar.getInstance();
            calc.setTime(currentTime);
            calc.add(Calendar.DATE, allowUpdateTime);
            Date maxTime = calc.getTime();

            Optional.ofNullable(activeTime)
                    .filter(f -> f.after(currentTime))
                    .filter(f -> f.before(maxTime))
                    .orElseThrow(() -> new BizException(ApiResponseEnum.ACTIVATION_DATA_IS_WRONG));

        } else {
            createOrderReq.setSetActiveTime(null);
        }
        int quantity = isEsimCard ? 1 : createOrderReq.getQuantity();
        boolean isBigOrder = quantity >= maxOrderNum;
        createOrderReq.setQuantity(quantity);
        BigDecimal price;
        // 计算订单金额
        BigDecimal totalAmount;
        // 写入订单表、订单详情表
        final String reqPrice = createOrderReq.getPrice();
        BigDecimal amountBeforeDiscount = BigDecimal.ZERO,discountAmount = BigDecimal.ZERO,detailDiscountAmount = BigDecimal.ZERO,detailAmountBeforeDiscount = BigDecimal.ZERO;
        if (StringUtils.isNotBlank(reqPrice)) {

            if (StringUtils.isBlank(createOrderReq.getAmountBeforeDiscount()) || StringUtils.isBlank(createOrderReq.getDiscountAmount())){
                throw new BizException(ApiResponseEnum.DISCOUNT_IS_NULL);
            }

            //折扣金额判断

            amountBeforeDiscount = amountBeforeDiscount.add(new BigDecimal(createOrderReq.getAmountBeforeDiscount()));
            discountAmount = discountAmount.add(new BigDecimal(createOrderReq.getDiscountAmount()));
            BigDecimal amountAfterDiscount = new BigDecimal(createOrderReq.getPrice());
            if (!(amountBeforeDiscount.compareTo(amountAfterDiscount.add(discountAmount)) == 0)){
                throw new BizException(ApiResponseEnum.DISCOUNT_ERROR);
            }

            totalAmount = new BigDecimal(reqPrice);
            price = totalAmount.divide(BigDecimal.valueOf(quantity), RoundingMode.UNNECESSARY);
            setCurrency(createOrderReq.getCurrency(), createOrderResp);

            //折扣金额计算
            detailDiscountAmount = detailDiscountAmount.add(discountAmount.divide(BigDecimal.valueOf(quantity), RoundingMode.UNNECESSARY));
            detailAmountBeforeDiscount = detailAmountBeforeDiscount.add(amountBeforeDiscount.divide(BigDecimal.valueOf(quantity), RoundingMode.UNNECESSARY));

        } else {
            price = getPrice(createOrderReq.getCurrency(), packageVO, createOrderResp);
            totalAmount = price.multiply(BigDecimal.valueOf(quantity));
            detailAmountBeforeDiscount = detailAmountBeforeDiscount.add(price);
            amountBeforeDiscount = amountBeforeDiscount.add(totalAmount);
        }
        List<String> iccids;
        ChannelOrder.OrderStatusEnum orderStatusEnum;
        ChannelCard channelCard = null;
        String eSimPackageUniqueId = null;
        HcardInfo eSimCard = null;
        if (isEsimCard) {
            eSimPackageUniqueId = getPackageUniqueId();
            log.debug("卡片形态为Esim卡 iccid: {}", iccid);
            Response<HcardInfo> getCardByIccidResponse = pmsFeignClient.getCardByIccid(iccid);
            if (!ResponseResult.SUCCESS.getCode().equals(getCardByIccidResponse.getCode())) {
                throw new BizException(getCardByIccidResponse.getMsg(), getCardByIccidResponse.getCode());
            }
            eSimCard = getCardByIccidResponse.get();
            if (eSimCard == null) {
                throw new BizException(ApiResponseEnum.CARD_INFORMATION_NOT_EXIST);
            }
            if ("2".equals(eSimCard.getType())) {
                throw new BizException(ApiResponseEnum.CARD_IS_COOPERATIVE_CARD);
            }
            if (!CardFormEnum.ESIM.matches(eSimCard.getCardForm())) {
                throw new BizException(ApiResponseEnum.CARD_IS_NOT_ESIM);
            }
            final String status = eSimCard.getIsStoreOut();
            if (!CardStatus.NORMAL.getStatus().equals(eSimCard.getStatus())) {
                throw new BizException(ApiResponseEnum.CARD_IS_UNUSUAL);
            }
            if (!HcardInfo.CardStoreEnum.IN.matches(status)) {
                log.debug("卡不存在于仓库中");
                throw new BizException(ApiResponseEnum.CARD_IS_OUT_STOCK);
            }
            Response.getAndCheckRemoteData(pmsFeignClient.cardOutStore(eSimCard.getImsi()));
            channelCard = saveChannelCard(createOrderReq, eSimCard, eSimPackageUniqueId);
            createOrderContext.get().setChannelCards(Collections.singletonList(channelCard));
            iccids = Collections.singletonList(iccid);
            orderStatusEnum = ChannelOrder.OrderStatusEnum.FINISHED;
        } else {
            iccids = Collections.emptyList();
            orderStatusEnum = ChannelOrder.OrderStatusEnum.NOT_DELIVERY;
        }
        final String orderName = packageVO.getNameCn() + "+" + CardFormEnum.getTypeName(cardForm);
        final String orderUniqueId = getOrderUniqueId();
        if (isBigOrder) {
            orderStatusEnum = ChannelOrder.OrderStatusEnum.ORDER_PROCESS;
            cmsBigOrderMapper.insert(new CmsBigOrder(orderUniqueId, (long) quantity));
        }
        final ChannelOrder order = saveOrder(iccids, channelCard, createOrderReq, packageVO,
                OrderUserTypeEnum.PERSONAL, OrderTypeEnum.CARD_PACKAGE, orderName,
                totalAmount, orderStatusEnum, createOrderResp, orderUserName, createOrderReq.getSetActiveTime(),
                goodsId, false, orderUniqueId, new Date(),amountBeforeDiscount,discountAmount);
        final String isPromotion = packageVO.getIsPromotion();
        //实名制流程
        realNameSysCardPackageEsim(
                isBigOrder,
                isEsimCard,
                order.getOrderType(),
                channelCard != null ? channelCard.getImsi() : null,
                order.getIccid(),
                channelCard != null ? channelCard.getMsisdn() : null,
                order.getOrderUniqueId(),
                String.valueOf(order.getId()),
                order.getPackageId()
        );
        // 更新主卡语言为订单语言
        if (StringUtils.isNotBlank(createOrderReq.getSendLang()) && iccids.size() > 0) {
            Response cardBatchupdateSendlangResponse = pmsFeignClient.cardBatchupdateSendlang(null, iccids, createOrderReq.getSendLang());
            if (!ResponseResult.SUCCESS.getCode().equals(cardBatchupdateSendlangResponse.getCode())) {
                throw new BizException(cardBatchupdateSendlangResponse.getMsg(), cardBatchupdateSendlangResponse.getCode());
            }

        }
        String packageUniqueId = null;
        if (isEsimCard) {
            packageUniqueId = eSimPackageUniqueId;
            // 保存卡与套餐信息
            saveChannelPackageCard(packageVO, orderUniqueId, order, channelCard, eSimPackageUniqueId, createOrderReq.getSetActiveTime(), null);
            // 保存订单详情
            saveOrderDetail(null, price, order, channelCard, null, eSimPackageUniqueId, isPromotion, createOrderReq.getSetActiveTime(), null,detailAmountBeforeDiscount,detailDiscountAmount);
//            sendCancelLocationIfNeeded(dataBundleId, order.getOrderDate(), channelCard.getCorpId(), channelCard.getImsi(), channelCard.getIccid());
        } else {
            ArrayList<ChannelOrderDetail> channelOrderDetails = new ArrayList<>();
            if (!isBigOrder) {
                for (int i = 0; i < quantity; i++) {
                    packageUniqueId = getPackageUniqueId();
                    // 保存订单详情
                    saveOrderDetail(null, price, order, null, null, packageUniqueId, isPromotion, createOrderReq.getSetActiveTime(), null,detailAmountBeforeDiscount,detailDiscountAmount);
                }
            } else {
                luExecutor.execute(() -> saveBigOrderDetail(price, order, isPromotion, createOrderReq, quantity));
            }
        }
        String phone = Optional.ofNullable(createOrderReq.getAddress())
                .map(CreateOrderReq.Address::getPhoneNumber).orElse(null);
        if (StringUtils.isNotBlank(phone)) {
            log.debug("下发购买短信 phone: {}", phone);
            String sendLang;
            if (StringUtils.isNotBlank(createOrderReq.getSendLang())) {
                sendLang = createOrderReq.getSendLang();
            } else {
                sendLang = isEsimCard ? eSimCard.getSendLang() : Optional.ofNullable(purchaseSmsConfigProperties.getSendLang())
                        .orElse(HcardInfo.SendLangEnum.ZH_CN.getValue());
            }
            long templateId;
            if (isEsimCard) {
                templateId = eSimCard.getTemplateId();
            } else {
                templateId = purchaseSmsConfigProperties.getTemplateId();
            }
            String packageName = getCardName(sendLang) + getPackageName(packageVO, sendLang);
            HcardInfo cardInfo = new HcardInfo();
            cardInfo.setMsisdn(phone);
            cardInfo.setSendLang(sendLang);
            cardInfo.setTemplateId(templateId);
            smsService.sendPurchaseSms(cardInfo, packageName, orderUniqueId, packageUniqueId);
        } else {
            log.debug("地址中的联系人号码为空，不下发购买短信");
        }
        createOrderResp.setOrderID(orderUniqueId);
        createOrderResp.setPrice(price.toString());
        createOrderResp.setTotalAmount(totalAmount.toString());
    }

    private ChannelCard saveChannelCard(CreateOrderReq createOrderReq, HcardInfo card, String eSimPackageUniqueId) {
        ChannelCard channelCard = ChannelCard.builder()
                .cardForm(CardFormEnums.ESIM.getType())
                .cardType(CardTypeEnum.H_CARD.getType())
                .imsi(card.getImsi())
                .iccid(card.getIccid())
                .msisdn(card.getMsisdn())
                .corpId(createOrderReq.getUserId())
                .packageUniqueId(eSimPackageUniqueId)
                .build();
        channelCardMapper.insert(channelCard);
        return channelCard;
    }

    /**
     * 获取单价
     *
     * @param currency
     * @param priceVO
     * @param createOrderResp
     * @return
     */
    private BigDecimal getPrice(String currency, PriceVO priceVO, CreateOrderResp createOrderResp) {
        BigDecimal price;
        String currencyCode = Optional.ofNullable(currency)
                .orElse(CurrencyCodeEnum.CNY.getCurrencyCode());
        if (CurrencyCodeEnum.USD.getCurrencyCode().equals(currencyCode)) {
            price = priceVO.getUsd();
        } else if (CurrencyCodeEnum.HKD.getCurrencyCode().equals(currencyCode)) {
            price = priceVO.getHkd();
        } else {
            price = priceVO.getCny();
        }
        createOrderResp.setCurrency(currencyCode);
        return price;
    }


    /**
     * 保存套餐订单
     *
     * @param iccids            卡号列表
     * @param channelCard
     * @param createOrderReq
     * @param packageVO         套餐
     * @param orderUserTypeEnum 订单类型归属
     * @param orderTypeEnum     订单类型
     * @param orderName         订单名称
     * @param amount            金额
     * @param orderStatusEnum   订单状态枚举
     * @param orderUserName
     * @return
     */
    private ChannelOrder saveOrder(List<String> iccids, ChannelCard channelCard, CreateOrderReq createOrderReq,
                                   PackageVO packageVO, OrderUserTypeEnum orderUserTypeEnum, OrderTypeEnum orderTypeEnum,
                                   String orderName, BigDecimal amount, ChannelOrder.OrderStatusEnum orderStatusEnum,
                                   CreateOrderResp createOrderResp, String orderUserName, Date setActiveTime, String goodsId,
                                   boolean isChannel, String orderUniqueId, Date orderDate,BigDecimal amountBeforeDiscount,BigDecimal discountAmount) {

        String currency = createOrderResp.getCurrency();
        BigDecimal subTotalAmount = createOrderResp.getSubTotalAmount();
        OrderChannel orderChannel = (orderUserTypeEnum == OrderUserTypeEnum.PERSONAL ? OrderChannel.OFFICIAL :
                OrderChannel.API);
        final CreateOrderReq.Address address = createOrderReq.getAddress();
        ChannelOrder order = ChannelOrder.builder()
                .cardForm(channelCard == null ? createOrderReq.getCardForm() : channelCard.getCardForm())
                .orderUserId(createOrderReq.getUserId())
                .orderUserName(orderUserName)
                .iccid(iccids.stream().collect(Collectors.joining(StringPool.PIPE)))
                .orderName(orderName)
                .orderDate(orderDate)
                .count(createOrderReq.getQuantity())
                .orderType(orderTypeEnum.getOrderType())
                .payType(ChannelOrder.PayTypeEnum.DIRECT.getValue())
                .orderChannel(orderChannel.getType())
                .orderUserType(orderUserTypeEnum.getValue())
                .packageId(packageVO.getId())
                .packageName(packageVO.getNameCn())
                .nameTw(packageVO.getNameTw())
                .nameEn(packageVO.getNameEn())
                .keepPeriod(packageVO.getKeepPeriod())
                .effectiveDay(getEffectiveTime(orderDate, packageVO.getEffectiveDay()))
                .periodUnit(packageVO.getPeriodUnit())
                .signBizId(packageVO.getSignBizId())
                .limitSpeedSignBizId(packageVO.getLimitSignBizId())
                .amount(amount)
                .orderStatus(orderStatusEnum.getValue())
                .orderUniqueId(orderUniqueId)
                .thirdOrderId(createOrderReq.getThirdOrderId())
                .currencyCode(currency)
                .activeAt(setActiveTime)
                .address(getAddress(address))
                .billFlow(StringUtils.isNotBlank(createOrderReq.getTransactionCode()) ? createOrderReq.getTransactionCode() : null)
                .addressee(Optional.ofNullable(address).map(CreateOrderReq.Address::getAddressee).orElse(null))
                .phoneNumber(Optional.ofNullable(address).map(CreateOrderReq.Address::getPhoneNumber).orElse(null))
                .postCode(Optional.ofNullable(address).map(CreateOrderReq.Address::getPostCode).orElse(null))
                .email(Optional.ofNullable(address).map(CreateOrderReq.Address::getEmail).orElse(null))
                .sendLang(createOrderReq.getSendLang() == null ? "" : createOrderReq.getSendLang())
                .goodsId(goodsId)
                .discountAmount(discountAmount)
                .amountBeforeDiscount(amountBeforeDiscount)
                .build();
        if (isChannel || "1".equals(packageVO.getIsChannelCreate())) {
            order.setCooperationMode(channelCard.getCooperationMode());
        }
        if (isChannel && subTotalAmount != null) {
            order.setOrderUserId(createOrderResp.getParentCorpId());
            order.setSubAmount(subTotalAmount);
            order.setSubCorpId(createOrderResp.getSubCorpId());
        }
        channelOrderMapper.insert(order);

        return order;
    }

    /**
     * 生成订单唯一id
     *
     * @return
     */
    private String getOrderUniqueId() {
        return Utils.cmiUuid(CmiUUID.ORDER_UNIQUE_ID);
    }

    /**
     * 生成套餐唯一id
     *
     * @return
     */
    private String getPackageUniqueId() {
        return Utils.cmiUuid(CmiUUID.PACKAGE_UNIQUE_ID);
    }

    /**
     * 计算套餐购买后的有效期
     *
     * @param startTime
     * @param effectiveDay
     * @return
     */
    public Date getEffectiveTime(Date startTime, Integer effectiveDay) {
        return DateUtils.addDays(startTime, effectiveDay);
    }

    private ChannelOrderDetail saveOrderDetail(RemunerationTypeEnum remunerationType, BigDecimal amount, ChannelOrder order,
                                               ChannelCard channelCard, BigDecimal remuneration, String packageUniqueId,
                                               String isPromotion, Date setActiveAt, BigDecimal subPrice,BigDecimal amountBeforeDiscount,BigDecimal discountAmount) {
        if (remuneration == null) {
            remunerationType = null;
        }
        ChannelOrderDetail.ChannelOrderDetailBuilder orderDetailBuilder = ChannelOrderDetail.builder()
                .orderId(order.getId())
                .orderDate(order.getOrderDate())
                .orderType(order.getOrderType())
                .packageId(order.getPackageId())
                .packageName(order.getPackageName())
                .nameTw(order.getNameTw())
                .nameEn(order.getNameEn())
                .orderChannel(order.getOrderChannel())
                .orderUserType(order.getOrderUserType())
                .orderStatus(order.getOrderStatus())
                .currencyCode(order.getCurrencyCode())
                .orderUserId(order.getOrderUserId())
                .amount(amount)
                .remunerationAmount(remuneration)
                .remunerationType(Optional.ofNullable(remunerationType).map(RemunerationTypeEnum::getValue).orElse(null))
                .thirdOrderId(order.getThirdOrderId())
                .packageUniqueId(packageUniqueId)
                .address(order.getAddress())
                .addressee(order.getAddressee())
                .phoneNumber(order.getPhoneNumber())
                .email(order.getEmail())
                .activeAt(setActiveAt)
                .cardType(CardType.CARD.getType())
                .cooperationMode(order.getCooperationMode())
                .subCorpId(order.getSubCorpId())
                .subAmount(subPrice)
                .amountBeforeDiscount(amountBeforeDiscount)
                .discountAmount(discountAmount)
                .postCode(order.getPostCode());
        if (StringUtils.isNotBlank(isPromotion)) {
            orderDetailBuilder.isPromotion(isPromotion);
        }
        if (channelCard == null) {
            orderDetailBuilder.cardForm(order.getCardForm());
        } else {
            orderDetailBuilder.cardForm(channelCard.getCardForm())
                    .iccid(channelCard.getIccid())
                    .imsi(channelCard.getImsi())
                    .msisdn(channelCard.getMsisdn())
                    .corpId(channelCard.getCorpId());
        }
        ChannelOrderDetail orderDetail = orderDetailBuilder.build();
        channelOrderDetailMapper.insert(orderDetail);

        return orderDetail;
    }

    private void saveBigOrderDetail(BigDecimal amount, ChannelOrder order, String isPromotion, CreateOrderReq createOrderReq, int orderNum) {
        log.debug("开始异步写入详单");
        try {
            ArrayList<ChannelOrderDetail> list = new ArrayList<>();
            for (int i = 0; i < orderNum; i++) {
                String packageUniqueId = getPackageUniqueId();

               BigDecimal discountAmount;

               BigDecimal amountBeforeDiscount;

               if (order.getAmountBeforeDiscount() == null || order.getDiscountAmount() == null){
                   amountBeforeDiscount = amount;
                   discountAmount = BigDecimal.ZERO;
               }else {
                   amountBeforeDiscount = order.getAmountBeforeDiscount().divide(new BigDecimal(order.getCount()));
                   discountAmount = order.getDiscountAmount().divide(new BigDecimal(order.getCount()));
               }

                ChannelOrderDetail.ChannelOrderDetailBuilder orderDetailBuilder = ChannelOrderDetail.builder()
                        .orderId(order.getId())
                        .orderDate(order.getOrderDate())
                        .orderType(order.getOrderType())
                        .packageId(order.getPackageId())
                        .packageName(order.getPackageName())
                        .nameTw(order.getNameTw())
                        .nameEn(order.getNameEn())
                        .orderChannel(order.getOrderChannel())
                        .orderUserType(order.getOrderUserType())
                        .orderStatus(ChannelOrder.OrderStatusEnum.NOT_DELIVERY.getValue())
                        .currencyCode(order.getCurrencyCode())
                        .orderUserId(order.getOrderUserId())
                        .amount(amount)
                        .remunerationAmount(null)
                        .remunerationType(null)
                        .thirdOrderId(order.getThirdOrderId())
                        .packageUniqueId(packageUniqueId)
                        .address(order.getAddress())
                        .addressee(order.getAddressee())
                        .cardForm(order.getCardForm())
                        .phoneNumber(order.getPhoneNumber())
                        .email(order.getEmail())
                        .activeAt(createOrderReq.getSetActiveTime())
                        .cardType(CardType.CARD.getType())
                        .cooperationMode(order.getCooperationMode())
                        .subCorpId(order.getSubCorpId())
                        .subAmount(null)
                        .amountBeforeDiscount(amountBeforeDiscount)
                        .discountAmount(discountAmount)
                        .postCode(order.getPostCode());
                if (StringUtils.isNotBlank(isPromotion)) {
                    orderDetailBuilder.isPromotion(isPromotion);
                }
                list.add(orderDetailBuilder.build());
                if (list.size() >= 500) {
                    channelOrderDetailMapper.insertBatch(list);
                    list.clear();
                }
            }
            if (CollectionUtils.isNotEmpty(list)) {
                channelOrderDetailMapper.insertBatch(list);
            }
            channelOrderMapper.update(null, Wrappers.lambdaUpdate(ChannelOrder.class)
                    .set(ChannelOrder::getOrderStatus, ChannelOrder.OrderStatusEnum.NOT_DELIVERY.getValue())
                    .eq(ChannelOrder::getId, order.getId()));
        } catch (Exception e) {
            log.warn("生成子单失败");
            channelOrderMapper.update(null, Wrappers.lambdaUpdate(ChannelOrder.class)
                    .set(ChannelOrder::getOrderStatus, ChannelOrder.OrderStatusEnum.ORDER_FAIL.getValue())
                    .eq(ChannelOrder::getId, order.getId()));
            SysSystemAlerts sysSystemAlerts = new SysSystemAlerts();
            sysSystemAlerts.setOperationTime(new Date());
            sysSystemAlerts.setSeverityLevel("3");
            sysSystemAlerts.setModuleName("H5大单订购");
            sysSystemAlerts.setOperationContent(createOrderReq.toString());
            sysSystemAlerts.setExceptionReason(getSimplifiedExceptionMessage(e));
            backFeignClient.insertLog(sysSystemAlerts);
        }

    }


    /**
     * 保存套餐激活信息
     *
     * @param packageVO
     * @param orderUniqueId
     * @param order
     * @param channelCard
     * @param packageUniqueId
     */
    private void saveChannelPackageCard(PackageVO packageVO, String orderUniqueId,
                                        ChannelOrder order, ChannelCard channelCard, String packageUniqueId, Date setActiveAt, String a2zCardUseSwitch) {
        Date effectiveDay = order.getEffectiveDay();

        ChannelPackageCard packageCard = ChannelPackageCard.builder()
                .iccid(channelCard.getIccid())
                .msisdn(channelCard.getMsisdn())
                .imsi(channelCard.getImsi())
                .packageType(CardPackageTypeEnum.PACKAGE.getPackageType())
                .packageId(packageVO.getId())
                .corpId(channelCard.getCorpId())
                .periodUnit(packageVO.getPeriodUnit())
                .keepPeriod(packageVO.getKeepPeriod())
                .packageStatus(PackageStatusEnum.UNACTIVATED.getStatus())
                .packageName(packageVO.getNameCn())
                .nameTw(packageVO.getNameTw())
                .nameEn(packageVO.getNameEn())
                .effectiveDay(effectiveDay)
                .orderUniqueId(orderUniqueId)
                .packageUniqueId(packageUniqueId)
                .signBizId(packageVO.getSignBizId())
                .limitSpeedSignBizId(packageVO.getLimitSignBizId())
                .slowSpeedSignBizId(packageVO.getSlowSignBizId())
                .flowLimitType(packageVO.getFlowLimitType())
                .controlLogic(packageVO.getControlLogic())
                .activationMode(packageVO.getActivationMode())
                .activeAt(setActiveAt)
                .supportRefuel(packageVO.getSupportRefuel())
                .supportChina(packageVO.getSupportChina())
                .cooperationMode(order.getCooperationMode())
                .build();
        if (packageVO.getFlowLimitSum() != null && StrUtil.isNotBlank(packageVO.getFlowLimitUnit())) {
            if (PackageFlowLimitEnums.GB.getUnit().equals(packageVO.getFlowLimitUnit())) {
                packageCard.setFlowLimitSum(packageVO.getFlowLimitSum().multiply(new BigDecimal(1024 * 1024 * 1024)));
            }
            if (PackageFlowLimitEnums.MB.getUnit().equals(packageVO.getFlowLimitUnit())) {
                packageCard.setFlowLimitSum(packageVO.getFlowLimitSum().multiply(new BigDecimal(1024 * 1024)));
            }
        }
        if (StrUtil.isNotBlank(packageVO.getActivationMode()) && packageVO.getActivationMode().equals("3")
                && packageVO.getBillFlowLimit() != null && StrUtil.isNotBlank(packageVO.getBillFlowLimitUnit())) {

            if (PackageFlowLimitEnums.GB.getUnit().equals(packageVO.getBillFlowLimitUnit())) {
                packageCard.setBillFlowLimit(packageVO.getBillFlowLimit().multiply(new BigDecimal(1024 * 1024 * 1024)));
            }
            if (PackageFlowLimitEnums.MB.getUnit().equals(packageVO.getBillFlowLimitUnit())) {
                packageCard.setBillFlowLimit(packageVO.getBillFlowLimit().multiply(new BigDecimal(1024 * 1024)));
            }
        }
        if (StrUtil.isNotBlank(order.getCooperationMode()) && ChannelDistributorDetail.CooperationModeEnum.A2Z.getType().equals(order.getCooperationMode())) {
            packageCard.setPackageUseStatus(A2ZUseStatus.ENABLE.getStatus().equals(a2zCardUseSwitch)
                    || A2ZUseStatus.ENABLING.getStatus().equals(a2zCardUseSwitch)
                    ? A2ZUseStatus.ENABLE.getStatus() : A2ZUseStatus.DEACTIVATE.getStatus());
        }
        channelPackageCardMapper.insert(packageCard);
        packageDirectionRelationService.savePackageAppRelation(packageVO.getAppInfos(), packageUniqueId);
    }

    /**
     * 原始价格计算折扣
     *
     * @param amount
     * @param discount
     * @return
     */
    public BigDecimal calculate(BigDecimal amount, Integer discount) {
        if (discount == 0) {
            return BigDecimal.ZERO;
        }
        return amount.multiply(BigDecimal.valueOf(discount * ONE_PERCENT));
    }

    private String getAddress(CreateOrderReq.Address address) {
        if (address == null) {
            return null;
        }
        return String.join(StringPool.PIPE,
                Objects.toString(address.getCountryName(), StringPool.EMPTY),
                Objects.toString(address.getProvince(), StringPool.EMPTY),
                Objects.toString(address.getCityName(), StringPool.EMPTY),
                Objects.toString(address.getMAddress(), StringPool.EMPTY)
        );
    }

    public PackageVO getPackage(String dataBundleId) {
        PackageVO packageVO = Response.getAndCheckRemoteData(pmsFeignClient.queryPackage(dataBundleId));
        final String auditStatus = packageVO.getAuditStatus();
        final String status = packageVO.getStatus();
        if (!PackageInfoStatus.NORMAL.matches(status)) {
            log.debug("套餐状态不正常 status: {}", status);
            throw new BizException(ApiResponseEnum.PACKAGE_STATUS_IS_ABNORMAL);
        }
        if (PackageIsTerminalEnum.YES.matches(packageVO.getIsTerminal())) {
            log.debug("套餐为终端厂商套餐，不允许购买");
            throw new BizException(ApiResponseEnum.PACKAGE_IS_NOT_ALLOWED_ORDERED);
        }
        if (!AuditStatusEnum.PASSED.matches(auditStatus)) {
            log.debug("套餐未审核通过 auditStatus: {}", auditStatus);
            throw new BizException(ApiResponseEnum.PACKAGE_STATUS_IS_ABNORMAL);
        }
        return packageVO;
    }

    /**
     * 实名制
     *
     * @param isEsimCard
     * @param orderType
     * @param imsi
     * @param iccid
     * @param msisdn
     * @param orderUniqueId
     * @param orderId
     * @param packageId
     */
    private void realNameSysCardPackageEsim(boolean isBigOrder, boolean isEsimCard, String orderType, String imsi, String iccid, String msisdn, String orderUniqueId, String orderId, String packageId) {
        if (isBigOrder) {
            log.debug("大单不进行订单的实名认证");
            return;
        }
        List<String> realNameAttrMccs = new ArrayList<>();
        List<String> countryRelationMccs = new ArrayList<>();
        List<RealNameInfo> realNameInfos;
        log.info("订单类型为:{}[3-卡+套餐,2-套餐],开始实名制处理流程", orderType);

        if (packageId == null) {
            log.error("packageId不能为空,实名制处理流程结束");
            return;
        }

        List<PackageCountryRelation> packageCountryRelations = pmsFeignClient.getPackageMccs(packageId).getData();
        if (CollectionUtils.isEmpty(packageCountryRelations)) {
            log.error("pms-套餐ID[{}]查询国家关联信息为空,实名制处理流程结束", packageId);
            return;
        }

        for (PackageCountryRelation relation : packageCountryRelations) {
            countryRelationMccs.add(relation.getMcc());
        }
        if (StringUtils.isNotBlank(imsi)) {
            HcardInfo hcardInfo = pmsFeignClient.getCardByImsi(imsi).getData();
            if (ObjectUtils.isEmpty(hcardInfo) || ObjectUtils.isEmpty(hcardInfo.getRealnameId())) {
                log.error("pms-imsi[{}]查询实名制ID为空,实名制处理流程结束", imsi);
                return;
            }
            realNameInfos = hcardInfo.getRealNameInfos();
            for (RealNameInfo attr : realNameInfos) {
                realNameAttrMccs.addAll(attr.getRealCountry().stream().map(RealNameInfo.CountryInfo::getMcc).collect(Collectors.toList()));
            }
            //取交集
            if (!CollectionUtils.isEmpty(realNameAttrMccs))
                realNameAttrMccs.retainAll(countryRelationMccs);

            if (realNameAttrMccs.isEmpty()) {
                log.info("实名制国家与套餐覆盖国家无交集,实名制处理流程结束");
                return;
            }
        } else {
            realNameInfos = pmsFeignClient.selectByMcc(countryRelationMccs).getData();
            if (CollectionUtils.isEmpty(realNameInfos)) {
                log.error("没有找到套餐关联国家对应的实名制规则,实名制处理流程结束");
                return;
            }
        }

        log.info("实名制信息保存：{}", realNameInfos);
        //卡+套餐
        if (orderType.equals(OrderTypeEnum.CARD_PACKAGE.getOrderType())) {
            //将imsi、msisdn、iccid、order_unique_id写入cms_channel_realname_info表
            //认证状态为 “待认证”，使用状态为“处理中”，认证对象为“卡”，得到认证ID（自增主键）
            realNameInfos.forEach(item -> {
                ChannelRealNameInfo realName = ChannelRealNameInfo.builder()
                        .imsi(imsi)
                        .msisdn(msisdn)
                        .iccid(iccid)
                        .orderUniqueId(orderUniqueId)
                        .authStatus("1")
                        .useStatus("1")
                        .authObj(isEsimCard ? "1" : "2")
                        .ruleCode(item.getRuleCode())
                        .ruleName(item.getRuleName())
                        .build();
                channelRealNameInfoMapper.insert(realName);
                //将iccid、order_unique_id、order_id、认证ID写入cms_channel_realname_order表
                channelRealNameOrderMapper.insert(ChannelRealNameOrder.builder()
                        .authId(realName.getAuthId())
                        .iccid(realName.getIccid() == null ? "" : realName.getIccid())
                        .orderId(orderId)
                        .orderUniqueId(realName.getOrderUniqueId())
                        .build());
            });
            return;
        }
        //套餐
        //根据iccid查询cms_channel_realname_info表数据，where auth_status=“认证通过”and use_status=“在用”and 证件有效期>当前时间
        if (channelRealNameInfoMapper.selectCount(Wrappers.lambdaQuery(ChannelRealNameInfo.class)
                .eq(ChannelRealNameInfo::getAuthStatus, "3")//认证状态 1-待认证 2-认证中 3-认证通过 4-认证失败 5-证件已过期
                .eq(ChannelRealNameInfo::getUseStatus, "2")// 使用状态 1-处理中 2-在用   3-备份
                .eq(ChannelRealNameInfo::getIccid, iccid)
                .gt(ChannelRealNameInfo::getCertificatesTime, new Date())) == 0) { //未过期

            List<ChannelRealNameInfo> crni = channelRealNameInfoMapper.selectList(
                    Wrappers.lambdaQuery(ChannelRealNameInfo.class)
                            .eq(ChannelRealNameInfo::getIccid, iccid)
                            .eq(ChannelRealNameInfo::getUseStatus, "1") //1、处理中
                            .orderByDesc(ChannelRealNameInfo::getUpdateTime));
            if (crni.isEmpty()) {
                //将imsi、msisdn、iccid、order_unique_id写入cms_channel_realname_info表，“待认证”，“处理中”，“卡”
                realNameInfos.forEach(item -> {
                    ChannelRealNameInfo info = ChannelRealNameInfo.builder()
                            .imsi(imsi)
                            .msisdn(msisdn)
                            .iccid(iccid)
                            .orderUniqueId(orderUniqueId)
                            .authStatus("1")
                            .useStatus("1")
                            .authObj("1")
                            .ruleCode(item.getRuleCode())
                            .ruleName(item.getRuleName())
                            .build();
                    channelRealNameInfoMapper.insert(info);
                    crni.add(info);
                });


            }
            crni.forEach(item -> {
                if (channelRealNameOrderMapper.selectCount(Wrappers.lambdaQuery(ChannelRealNameOrder.class)
                        .eq(ChannelRealNameOrder::getAuthId, item.getAuthId())
                        .eq(ChannelRealNameOrder::getIccid, iccid == null ? "" : iccid)
                        .eq(ChannelRealNameOrder::getOrderId, orderId)
                        .gt(ChannelRealNameOrder::getOrderUniqueId, orderUniqueId)) == 0)
                    //将iccid、order_unique_id、order_id、认证ID写入cms_channel_realname_order表
                    channelRealNameOrderMapper.insert(ChannelRealNameOrder.builder()
                            .authId(item.getAuthId())
                            .iccid(iccid == null ? "" : iccid)
                            .orderId(orderId)
                            .orderUniqueId(orderUniqueId)
                            .build());
            });
        }

    }

    public Long getOrderByUniqueId(String orderUniqueId) {
        final ChannelOrder channelOrder = channelOrderMapper.selectOne(Wrappers.lambdaQuery(ChannelOrder.class)
                .eq(ChannelOrder::getOrderUniqueId, orderUniqueId));
        return ObjectUtil.isNull(channelOrder) ? 0 : channelOrder.getId();
    }

    public void compensationOrder(CompensationReq compensationReq) {
        String orderUniqueId = compensationReq.getOrderUniqueId();
        boolean lock = redissonLock.tryLock(orderUniqueId);
        if (!lock) {
            throw new BizException("订单正在处理中，请稍后重试");
        }
        try {
            ChannelOrder channelOrder = channelOrderMapper.selectOne(Wrappers.lambdaQuery(ChannelOrder.class)
                    .eq(ChannelOrder::getOrderUniqueId, orderUniqueId));
            if (channelOrder == null) {
                throw new BizException("订单不存在");
            }
            if (!channelOrder.getOrderStatus().equals(ChannelOrder.OrderStatusEnum.ORDER_FAIL.getValue())) {
                throw new BizException("订单状态不为订单处理失败");
            }
            Integer count = channelOrder.getCount();
            Integer finishCount = channelOrderDetailMapper.selectCount(Wrappers.lambdaQuery(ChannelOrderDetail.class)
                    .eq(ChannelOrderDetail::getOrderId, channelOrder.getId()));
            log.info("订单数量：{}， 已完成数量：{}", count, finishCount);
            if (count > finishCount) {
                String dataBundleId = channelOrder.getPackageId();
                PackageVO packageVO = getPackage(dataBundleId);
                int quantity = count - finishCount;
                BigDecimal price = channelOrder.getAmount().divide(BigDecimal.valueOf(count), RoundingMode.UNNECESSARY);
                CreateOrderReq createOrderReq = new CreateOrderReq();
                createOrderReq.setSetActiveTime(channelOrder.getActiveAt());
                createOrderReq.setQuantity(quantity);
                channelOrder.setOrderStatus(ChannelOrder.OrderStatusEnum.ORDER_PROCESS.getValue());
                channelOrderMapper.updateById(channelOrder);
                luExecutor.execute(() -> saveBigOrderDetail(price, channelOrder, packageVO.getIsPromotion(), createOrderReq, quantity));
            }
        } finally {
            if (redissonLock.isHeldByCurrentThread(orderUniqueId)) {
                redissonLock.unlock(orderUniqueId);
            }
        }
    }
}
