package com.ebupt.cmi.clientmanagement.domain.response;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 外部API错误码返回
 */
@AllArgsConstructor
@Getter
public enum ApiResponseEnum {

    /**
     * IBoss错误(1000032 - 1000038)
     * IBoss充值流水编码错误
     */
    CODE_REPEAT("1000032","Transaction repeated, top up failed"),

    /**
     * 币种错误
     */
    CURRENCY_ERROR("1000033", "Wrong currency, top up failed"),

    /**
     * 币种与渠道商币种不一致
     */
    CURRENCY_NOT_FIT("1000034", "Currency and channel currency not matched, operation failed"),

    /**
     * 充值类型错误
     */
    DEPOSIT_TYPE_ERROR("1000036", "Top up type is wrong, top up failed"),

    /**
     * 充值后可用额度 < 0
     */
    DEPOSIT_ERROR("1000037", "Available amount after top up is not enough, top up failed"),

    /**
     * ebsCode错误，未找到渠道商或渠道商详情
     */
    EBSCODE_ERROR("1000038", "Can't find channel information, operation failed"),

    COOPERATION_MODE_NOT_FIT("1000181", "Top up type and cooperation model not matched"),

    /**
     * 渠道商退订套餐时错误
     */
    ORDER_NOT_FOUND("1000039", "订单不存在，退订失败"),

    /**
     * 退订类型错误
     */
    ORDER_TYPE_ERROR("1000040", "退订订单类型只支持套餐"),



    /**
     * 订单状态异常
     */
    ORDER_STATUS_ERROR("1000041", "退订订单状态只支持已完成"),

    /**
     * 子单状态不统一
     */
    SUBLIST_STATUS_NOT_UNIFICATION("1000042", "子单状态不统一，退订失败"),

    /**
     * 套餐状态不统一
     */
    PACKAGE_STATUS_NOT_UNIFICATION("1000043", "套餐部分为已激活，操作失败"),

    /**
     * 套餐非待激活
     */
    PACKAGE_NOT_WAITACTIVED("1000044", "套餐非待激活状态，退订失败"),

    /**
     * 套餐个数与子单个数不一致
     */
    PACKAHE_NUM_ERROR("1000045", "套餐个数与子单个数不一致，退订失败"),

    /**
     * 获取分布式锁失败
     */
    ACQUIRE_LOCK_FAILED("1000046", "退订失败，请稍后重试"),

    /**
     * 退款异常，退订失败
     */
    REFUND_FAILED("1000047", "退款失败"),

    /**
     *
     */
    TOTAL_DEPOSIT_ERROR("1000048", "Amount after top up is not enough, top up failed"),

    /**
     *
     */
    USED_DEPOSIT_ERROR("1000049", "Used amount after top up is not enough, top up failed"),

    /**
     *
     */
    MONTH_NOT_MATCH("1000035", "订单时间与当前月份不一致，不支持退订"),

    USER_NOT_EXIST("1000053", "Account does not exist"),

    REQUIRED_PARAMETER_IS_EMPTY("1000005","The required parameter {%s} is empty"),

    CARD_NOT_BELONG_CHANNEL("1000073","ICCID {}不属于该渠道商"),

    CARD_NO_PERMISSION("1000076","No permission to operate this IMSI or ICCID"),

    TEMPORARY_USER_LIMIT(" 1000079","Temporary users are only allowed to purchase packages/add-on packages+套餐"),

    CHANNEL_OR_ICCID_USER_LIMIT(" 1000081","Channel users/ICCID users are only allowed to purchase packages/add-on packages"),

    CURRENCY_INPUT_ERROR("1000083","Currency input error"),

    INDIVIDUAL_USER_NOT_EXIST("1000084","Individual users do not exist"),

    USER_MESSAGE_NOT_EXIST("1000085","Customer information does not exist"),

    NON_CMI_CARD_LIMIT("1000086","Non CMI card, add-on pack/package ordering is not allowed"),

    ACTIVATION_DATA_IS_WRONG("1000087","The package’s activation date is empty or not within the allowable range"),

    CARD_INFORMATION_NOT_EXIST("1000088","Primary card information does not exist"),

    CARD_IS_COOPERATIVE_CARD("1000089","The primary card type is cooperative issuance"),

    CARD_IS_NOT_ESIM("1000090","The primary card form factor is not an eSIM card"),

    CARD_IS_OUT_STOCK("1000091","The primary card has been delivered"),

    NO_ACTIVE_PACKAGE("1000094","No activated package, add-on pack ordering is not allowed"),

    PACKAGE_NOT_EXIST("1000096","Package does not exist or does not support add-on pack"),

    PACKAGE_NOT_ALLOWED_ORDER("1000097","Add-on package is not allowed to order or has not been approved"),

    CHANNEL_INFORMATION_IS_ABNORMAL("1000099","The channel information does not exist or the status is abnormal"),

    ORDER_NOT_ALLOWED("1000100","The current status is not allowed to order"),

    CURRENCY_IS_MISMATCH("1000102","Purchased failed, the currency is mismatch"),

    INSUFFICIENT_DEPOSIT("1000103","Purchase failure, insufficient deposit"),

    PACKAGE_IS_NOT_ALLOWED_ORDERED("1000105","This package is not allowed to be purchased"),

    /**
     * 订单同步接口重复请求
     */
    REPEAT_REQUEST("1000154", "The order is being processed, please do not submit it repeatly"),

    REPEAT_CODE("1000162", "The transaction number already exists, order creation failed"),



    /********************省移动外部接口对接相关返回码********************/
    NOT_HAVE_PERMISSION("1000163","当前渠道商无操作此订单权限"),
    ORDER_STATUS_ERROR_2("1000247","退订订单状态只支持已完成或者待发货"),
    NUMBER_RELATION_ERROR("1000165", "号码对应关系错误"),
    ORDER_NOT_ALLOWED_UNS("1000166","订单数量不唯一，操作失败"),

    /**
     * 控速API错误码
     */
    NOT_HAVE_PERMISSION2("1000168", "无权限操作此速度模板id"),
    PACKAGE_NOT_USED("1000169", "当前在用套餐非此套餐，不能进行控速"),
    NOT_UPCC_SIGN("1000171", "V卡非动态签约卡池，不能进行控速"),
    NOT_HAVE_PACKAGE("1000172", "未找到符合要求的套餐"),
    NOT_HAVE_LOCATION("1000173", "未查询到卡位置信息，无法进行控速"),
    NOT_HAVE_SURF_INFORMATION("1000174", "数据异常，未找到iccid当前位置上网信息"),

    /********************实名认证接口相关返回码********************/
    AUTH_REAL_NAME_PARAMS_ICCID_ORDERID ("1000107", "iccid、orderID必须填写一项"),
    AUTH_REAL_NAME_PARAMS_PHONE_EMAIL ("1000108", "手机号、邮箱必须填写一项"),
    AUTH_REAL_NAME_PARAMS_COUNTRY ("1000109", "护照国家必填不能为空"),
    AUTH_REAL_NAME_PARAMS_OLD_TYPE_ID ("1000110", "旧证件类型和ID必须同时填写/不填写"),
    AUTH_REAL_NAME_PARAMS_BIRTH ("1000111", "出生年月格式错误：YYYYMMDD"),
    AUTH_REAL_NAME_PARAMS_FILE_TYPE ("1000112", "证件图片仅支持JPG/PNG"),
    AUTH_REAL_NAME_PARAMS_FILE_PIXEL ("1000113", "证件图片尺寸不小于15×15像素，最长边不超过4096像素"),
    AUTH_REAL_NAME_PARAMS_FILE_SIZE ("1000114", "证件图片文件大小不超过10MB"),
    AUTH_REAL_NAME_CARD_INFO_FAILED ("1000115", "获取主卡信息失败,请确认ICCID是否正确"),
    AUTH_REAL_NAME_ORDER_INFO_FAILED ("1000116", "获取订单信息失败,请确认订单是否存在"),
    AUTH_REAL_NAME_USED_INFO_EXIST ("1000117", "当前iccid/orderID已存在在用状态信息"),
    AUTH_REAL_NAME_HAS_PASSED_INFO ("1000118", "卡+套餐订单已实名认证通过不允许再次认证"),
    AUTH_REAL_NAME_RULE_INFO_FAILED ("1000119", "获取规则信息失败,请确认认证编码是否正确"),
    AUTH_REAL_NAME_WATERMARK_SAVE_FAILED ("1000120", "加密水印图片保存失败,请确认上传文件是否正确"),
    AUTH_REAL_NAME_BIND_COUNT_OVER ("1000121", "当前证件已绑定卡数量超出X，限制1证绑定X个号码"),
    AUTH_REAL_NAME_OLD_INFO_NOT_EXIST ("1000122", "旧证件类型/ID不存在在用，不允许修改认证信息"),
    AUTH_REAL_NAME_NAME_ERROR ("1000123", "旧认证信息姓名与输入姓名不一致，不允许修改认证信息"),
    AUTH_REAL_NAME_PASSPORT_TYPE_ERROR ("1000124", "护照类型不符合要求"),
    AUTH_REAL_NAME_REPEATED ("1000125", "该认证请求正在处理中，请勿重复提交"),
    AUTH_REAL_NAME_FAILED ("1000126", "H5实名认证失败"),
    AUTH_REAL_NAME_PARAMS_EXPIRE_DATE ("1000178", "证件过期时间格式错误"),
    AUTH_REAL_NAME_THE_CERTIFICATE_HAS_EXPIRED ("1000179", "证件已过期"),
    AUTH_REAL_NAME_UNDER_16_YEARS_OLD ("1000180", "未满16周岁"),


    /********************请求资源&Vimsi下发接口相关返回码********************/
    VIMSI_SEND_ORDER_PACKAGE_NOT_EXIST ("1000127", "未查询到当前订单订购的套餐"),
    VIMSI_SEND_APPKEY_NOT_NULL ("1000128", "AppKey不能为空"),
    VIMSI_SEND_PLMNS_GET_FAILED ("1000129", "获取plmns信息失败"),
    VIMSI_SEND_APPKEY_NO_INFO ("1000130", "无效的APP KEY"),
    VIMSI_SEND_NO_CHANNEL_INFO ("1000131", "没有找到厂商"),
    VIMSI_SEND_PACKAGE_INFO_NOT_EXIST ("1000132", "没有找到套餐信息"),
    VIMSI_SEND_VCARD_KI_NOT_NULL ("1000136", "分配V卡KI不能为空"),
    VIMSI_SEND_VCARD_KI_ENCRYPTION_ERROR ("1000137", "分配V卡KI加密失败"),
    VIMSI_SEND_HSS_OPEN_ERROR ("1000138", "HSS开户失败"),
    VIMSI_SEND_UPCC_OPEN_ERROR ("1000139", "Upcc开户失败"),

    FILE_SIZE_OVER ("1000141", "上传的文件大小过大"),

    NO_SUPPORT_MCC("1000142", "套餐支持的MCC不存在于卡池支持的MCC中"),

    LIMIT_TYPE_IS_WRONG("1000150","Wrong package data limit type"),

    PACKAGE_STATUS_IS_ABNORMAL("1000151","The status of the package is not normal"),

    EXCEEDED_THE_NUMBER("1000152","The amount of purchasing promotional package has been exceeded"),

    CARD_IS_UNUSUAL("1000153","The primary card status is not normal"),



    /**
     * 渠道商类型错误,用不到,把ma给其他人用了。
     */
    CHANNEL_TYPE_ERROR("1000155", "Channel type is wrong, top up failed"),

    USER_NOT_HAVE_PERMISSION("1000054","The type of current user does not have permission"),

    /**
     * 订单同步接口错误提示
     */
    COOPERATION_MODE_MUST_CONSISTENT("1000202","The cooperation model of the package must be the same as the card"),

    /**个人账户相关错误码*/
    PHONE_HAS_REGIST("1000183", "This mobile phone number has been registered and cannot be bound to an email address"),
    EMAIL_HAS_REGIST("1000184", "This email address has been registered and cannot be bound to a mobile phone"),

    PHONE_NOT_REGIST("1000185", "This mobile phone number is not registered and cannot be bound to an email address"),

    EMAIL_NOT_REGIST("1000186", "This email address is not registered and cannot be bound to a mobile phone"),
    SERVICE_TYPE_ERROR("1000190", "serviceType is wrong"),

    ACCOUNT_TYPE_ERROR("1000191", "accountType is wrong"),

    ADDRESS_NOT_FIND("1000192","The corresponding address information of the user was not found."),
    ACCESS_TOKEN_NOT_FIT("1000193", "The accessToken does not fit this account"),
    PACKAGE_CARDFORM_ERROR("1000243","Package is available to purchase for {%s} only"),

    // h5退订返回码
    ORDER_TYPE_ERROR_2("1000244", "退订订单类型只支持套餐和卡加套餐"),
    ORDER_NUMBER_ERROR("1000245", "符合要求的子单数量不足，退订失败"),

    ORDER_NOTIFY_ERROR("1000246", "退订通知发送失败，退订失败"),

    ORDER_STATUS_ERROR_3("1000248","大单此状态不能进行退订，请联系客服处理"),

    USER_PERMISSION_ERROR("1000249", "无权限操作此订单"),

    ORDER_ERROR("1000250", "大单不能进行订单实名认证"),

    REALNAME_AUTH_ONLY_ICCID("1000251","当前仅支持iccid进行实名制认证"),

    RULE_CODE_NOT_FOUND("1000252","此卡无当前国家认证规则"),

    DISCOUNT_ERROR("1000253","The discount amount is wrong, please upload again" ),

    DISCOUNT_IS_NULL("1000254","Discount amount or pre-discount amount cannot be empty" ),

    /********************远程调用服务相关返回码********************/
    /**
     * 远程调用服务失败
     * 	0：cms
     * 	1：pms
     * 	2：control
     * 	3：oms
     * 	4：sms
     * 	5：sys
     * 	6：stat
     */
    SERVICE_UNAVAILABLE_CMS("2000000", "Failed to call the service remotely"),
    SERVICE_UNAVAILABLE_PMS("2000001", "Failed to call the service remotely"),
    SERVICE_UNAVAILABLE_CONTROL("2000002", "Failed to call the service remotely"),
    SERVICE_UNAVAILABLE_OMS("2000003", "Failed to call the service remotely"),
    SERVICE_UNAVAILABLE_SMS("2000004", "Failed to call the service remotely"),
    SERVICE_UNAVAILABLE_SYS("2000005", "Failed to call the service remotely"),
    SERVICE_UNAVAILABLE_STAT("2000006", "Failed to call the service remotely"),
    SERVICE_UNAVAILABLE_ABILITY("2000007", "Failed to call the service remotely"),

    /**
     * 服务内部异常
     */
    SERVER_ERROR("9999999", "System error, request failed"),
    SUCCESS("0000", "成功") ;


    private final String code;
    private final String message;

    public static Response<Void> matchCode(String code){
        ApiResponseEnum[] values = ApiResponseEnum.values();
        for (ApiResponseEnum value : values) {
            if (code.equals(value.getCode())){
                return Response.error(code, value.getMessage());
            }
        }
        return null;
    }
}
