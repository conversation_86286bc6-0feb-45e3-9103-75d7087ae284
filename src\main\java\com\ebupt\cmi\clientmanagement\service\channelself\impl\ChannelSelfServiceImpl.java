package com.ebupt.cmi.clientmanagement.service.channelself.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebupt.cmi.clientmanagement.annotion.SdcMultipleQueues;
import com.ebupt.cmi.clientmanagement.config.NoticeConfigProperties;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.enums.QueueEnum;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.vo.SingleDayDelayVO;
import com.ebupt.cmi.clientmanagement.consumer.uitils.SendMessageWrapper;
import com.ebupt.cmi.clientmanagement.domain.dto.*;
import com.ebupt.cmi.clientmanagement.domain.dto.channelself.OrderDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.channelself.*;
import com.ebupt.cmi.clientmanagement.domain.dto.cooperation.MyOwnPage;
import com.ebupt.cmi.clientmanagement.domain.entity.*;
import com.ebupt.cmi.clientmanagement.domain.entity.redis.RedissonLock;
import com.ebupt.cmi.clientmanagement.domain.enums.*;
import com.ebupt.cmi.clientmanagement.domain.enums.lu.CardTypeEnum;
import com.ebupt.cmi.clientmanagement.domain.enums.lu.PackageStatusEnum;
import com.ebupt.cmi.clientmanagement.domain.properties.PaymentEmailProps;
import com.ebupt.cmi.clientmanagement.domain.req.ChannelIncomeInfoMonth;
import com.ebupt.cmi.clientmanagement.domain.req.UpccControlReq;
import com.ebupt.cmi.clientmanagement.domain.response.*;
import com.ebupt.cmi.clientmanagement.domain.vo.*;
import com.ebupt.cmi.clientmanagement.domain.vo.channel.UserPackageForm;
import com.ebupt.cmi.clientmanagement.domain.vo.channel.UserPackageVO;
import com.ebupt.cmi.clientmanagement.domain.vo.channelself.PackageVO;
import com.ebupt.cmi.clientmanagement.domain.vo.channelself.*;
import com.ebupt.cmi.clientmanagement.domain.vo.through.HistoryQuota;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.feign.back.BackFeignClient;
import com.ebupt.cmi.clientmanagement.feign.back.vo.BatchSyncfileTack;
import com.ebupt.cmi.clientmanagement.feign.back.vo.MailSendParam;
import com.ebupt.cmi.clientmanagement.feign.back.vo.User;
import com.ebupt.cmi.clientmanagement.feign.external.req.PriceInfo;
import com.ebupt.cmi.clientmanagement.feign.external.req.UnsubscribeNotifyReq;
import com.ebupt.cmi.clientmanagement.feign.jms.JmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.oms.OmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.oms.domain.OmsCountry;
import com.ebupt.cmi.clientmanagement.feign.pms.PmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.*;
import com.ebupt.cmi.clientmanagement.feign.pms.domainV2.AvilablePackageVO;
import com.ebupt.cmi.clientmanagement.feign.pms.domainV2.CardPageFormChannel;
import com.ebupt.cmi.clientmanagement.feign.pms.domainV2.CardVO;
import com.ebupt.cmi.clientmanagement.feign.sms.SmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.stat.StatFeignClient;
import com.ebupt.cmi.clientmanagement.feign.stat.vo.ChannelCloseAccounts;
import com.ebupt.cmi.clientmanagement.feign.stat.vo.FlowUsedVO;
import com.ebupt.cmi.clientmanagement.feign.stat.vo.UpdateCloseAccountsDTO;
import com.ebupt.cmi.clientmanagement.mapper.*;
import com.ebupt.cmi.clientmanagement.service.*;
import com.ebupt.cmi.clientmanagement.service.channel.ChannelDistributorsService;
import com.ebupt.cmi.clientmanagement.service.channelself.BatchPackageBuyService;
import com.ebupt.cmi.clientmanagement.service.channelself.ChannelSelfService;
import com.ebupt.cmi.clientmanagement.service.decorator.BlankCardOrderSerivceStatusMachineDecorator;
import com.ebupt.cmi.clientmanagement.service.impl.BlankCardOrderServiceImpl;
import com.ebupt.cmi.clientmanagement.service.impl.ChannelServiceImpl;
import com.ebupt.cmi.clientmanagement.service.lu.context.CoreNetContext;
import com.ebupt.cmi.clientmanagement.service.lu.context.LocationUpdateContextFactory;
import com.ebupt.cmi.clientmanagement.service.lu.corenet.CoreNetCaller;
import com.ebupt.cmi.clientmanagement.utils.*;
import com.ebupt.elk.log.TraceLogger;
import com.ebupt.excel.core.AnnotationParser;
import com.ebupt.excel.core.WorkbookUtil;
import com.ebupt.excel.domain.model.ExportExcel;
import com.ebupt.excel.util.CommonUtils;
import com.ebupt.excel.util.ExcelUtils;
import com.ebupt.pdf.util.PdfUtils;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.ebupt.excel.core.AnnotationParser.parseAnnotations;
import static org.assertj.core.util.DateUtil.now;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ChannelSelfServiceImpl.java
 * @Description 渠道自服务Service实现类
 * @createTime 2021年06月16日 18:07:00
 */

@Service
@Slf4j
@RefreshScope
@EnableAspectJAutoProxy(exposeProxy = true)
public class ChannelSelfServiceImpl implements ChannelSelfService {
    private final static String STOCK_TRANSFER_ERROR_LOGNAME = "stockTransfer_errorlog_%s_%s";
    private final static BigDecimal BYTE_TO_MB = new BigDecimal("1048576");

    @Autowired
    Executor stockTransferExecutor;

    @Autowired
    UnsubscribeNotifyService unsubscribeNotifyService;

    @Autowired
    NoticeConfigProperties noticeConfigProperties;

    @Autowired
    Common commonservice;

    @Autowired
    private ChannelRebateUtil channelRebateUtil;

    @Autowired
    private CmsChannelMarketingRebateMapper channelMarketingRebateMapper;

    @Autowired
    @Qualifier("realBlankCardOrderService")
    BlankCardOrderServiceImpl blankCardOrderServiceImpl;

    @Autowired
    @Qualifier("blankCardOrderSerivceDecorator")
    BlankCardOrderSerivceStatusMachineDecorator blankCardOrderSerivceStatusMachineDecorator;

    @Value("${stockTransfer-error-log-path}")
    String stockTransferErrorlogPath;

    @Value("${redisson.expireTime}")
    Long redisLockTime;

    @Value("${channelSelf.export-order.file_name_prefix}")
    String fileNamePrefix;
    @Value("${channelSelf.export-order.file_path}")
    String filePathPrefix;
    @Value("${channelSelf.export-order.task_name_prefix}")
    String taskNamePrefix;

    @Autowired
    private StoreManageExportTask storeManageExportTask;

    @Autowired
    private CmsChannelBlankcardOrderMapper channelBlankcardOrderMapper;

    @Value("${store-export.filename}")
    private String fileName;

    @Value("${store-export.filePath}")
    private String filePath;

    @Value("${store-export.taskDesc}")
    private String taskDesc;

    @Value("${file-upload.base-path}")
    String nfspath;

    @Value("${blankCardIllustrate}")
    String blankCardIllustrate;

    @Value("${phoneIllustrate}")
    String phoneIllustrate;

    @Autowired
    RedissonLock redissonLock;

    @Autowired
    ChannelMapper channelMapper;

    @Autowired
    ChannelBatchTaskMapper channelBatchTaskMapper;

    @Autowired
    ChannelBatchTaskDetailMapper channelBatchTaskDetailMapper;

    @Autowired
    ChannelDistributorDetailMapper channelDistributorDetailMapper;

    @Autowired
    CmsCardUpccRecordMapper cardUpccRecordMapper;

    @Autowired
    ChannelPackageRelationMapper channelPackageRelationMapper;

    @Autowired
    ChannelChargeRecordMapper channelChargeRecordMapper;

    @Autowired
    ChannelBindMapper channelBindMapper;

    @Resource
    private ChannelService channelService;

    @Autowired
    ChannelOrderDetailMapper channelOrderDetailMapper;

    @Autowired
    ChannelCardMapper channelCardMapper;

    @Autowired
    ChannelOrderMapper channelOrderMapper;

    @Autowired
    ChannelPackageCardMapper channelPackageCardMapper;

    @Autowired
    PackageDirectionRelationMapper packageDirectionRelationMapper;

    @Resource
    private CmsChannelChargeDetailMapper cmsChannelChargeDetailMapper;

    @Autowired
    PmsFeignClient pmsFeignClient;

    @Autowired
    OmsFeignClient omsFeignClient;

    @Autowired
    SmsFeignClient smsFeignClient;

    @Autowired
    StatFeignClient statFeignClient;

    @Autowired
    BackFeignClient backFeignClient;

    @Autowired
    ChannelLuReportMapper channelLuReportMapper;

    @Autowired
    ExportOrderTask exportOrderTask;

    @Autowired
    private ChannelPackageCardService channelPackageCardService;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private BatchPackageBuyServiceImpl batchPackageBuyServiceImpl;

    @Autowired
    private ChannelDistributorsService channelDistributorsService;

    @Autowired
    private ChannelSelfServiceImpl channelSelfServiceImpl;

    @Autowired
    private BeanFactory beanFactory;

    @Autowired
    private Common common;

    @Resource
    private PackageEndService packageEndService;

    @Resource
    private ChannelUpcctemplateRelationMapper channelUpcctemplateRelationMapper;

    @Resource
    private CoreNetCaller coreNetCaller;

    @Resource
    private CmsPackageCardUpccRelationMapper cmsPackageCardUpccRelationMapper;

    @Resource
    private ChannelSurfInfoMapper channelSurfInfoMapper;

    @Resource
    private Utils utils;

    @Resource
    /**
     * 统一丢队方法
     */
    private SendMessageWrapper sendMessageWrapper;


    @Autowired
    OrderService orderService;

    @Resource
    private CmsChannelPackageDetailMapper channelPackageDetailMapper;

    @Resource
    private PaymentEmailProps paymentEmailProps;

    @Resource
    private ExportTask exportTask;

    private final static String obligation = "3";

    private final static String paid = "4";

    @Autowired
    private FlowOperationUtils flowOperationUtils;

    @Value("${lu.support_hotspots.slow_speed_upcc_id}")
    private String slowSpeedUpccId_y;

    @Value("${lu.not_support_hotspots.slow_speed_upcc_id}")
    private String slowSpeedUpccId_n;

    @Value("${lu.upcc_speed}")
    private long slowUpccSpeed;

    @Value("${update-card-size}")
    private Integer updateCardSize;
    @Qualifier("cmsChannelImsiAmountRelationMapper")

    @Resource
    private CmsChannelImsiAmountRelationMapper cmsChannelImsiAmountRelationMapper;
    @Autowired
    private CmsCorpFlowdetailMapper cmsCorpFlowdetailMapper;
    @Resource
    private CmsDepositChargeRecordMapper cmsDepositChargeRecordMapper;
    @Value("${offline-charge.invoice-company-name}")
    private String companyName;
    @Value("${offline-charge.invoice-company-address}")
    private String companyAddress;
    @Value("${offline-charge.invoice-payment-instruction}")
    private String paymentInstruction;

    @Autowired
    private GetExportTaskMessage getExportTaskMessage;

    @Autowired
    private ExportAsyncTask exportAsyncTask;

    @Autowired
    private CmsChannelMarketingRebateMapper cmsChannelMarketingRebateMapper;

    @Autowired
    private CmsChannelMarketBillFlowMapper cmsChannelMarketBillFlowMapper;

    private final static BigDecimal lowFlow = new BigDecimal(10485.76);

    @Autowired
    private CmsInvoiceAuditRecordMapper cmsInvoiceAuditRecordMapper;

    @Override
    public MyOwnPage<TaskBatchDTO> getTaskBatchList(String corpId,
                                                    String startDate,
                                                    String endDate,
                                                    Long pageSize,
                                                    Long pageNumber,
                                                    String taskName) {

        Long startIndex = (pageNumber - 1) * pageSize;

        List<Long> ids = channelBatchTaskMapper.getTaskIdsByCorpId(corpId, startDate, endDate, taskName);

        if (ids.size() < 1) {
            return null;
        }

        List<TaskBatchDTO> records = channelBatchTaskMapper.getTaskBatchs(ids, pageSize, startIndex);

        Long total = channelBatchTaskMapper.getTaskSizeByCorpId(ids);

        MyOwnPage<TaskBatchDTO> result = new MyOwnPage<>(records, total, pageNumber, pageSize);

        return result;
    }

    @Override
    public void stockTransfer(StockTransferVO vo) {
        File tempFile = FileUtil.createTempFile();
        try {
            vo.getFile().transferTo(tempFile);
        } catch (Exception e) {
            throw new BizException("读取文件失败");
        }

        stockTransferExecutor.execute(new StockTransferTask(
                vo.getParentCorpId(),
                vo.getCorpId(),
                tempFile,
                channelPackageCardMapper,
                channelBindMapper,
                channelCardMapper,
                channelOrderMapper,
                channelOrderDetailMapper
        ));
    }

    @Override
    public MyOwnPage<TaskBatchDetailDTO> getTaskBatchDetailDTOs(Long taskId,
                                                                Long pageSize,
                                                                Long pageNumber) {
        Long startIndex = (pageNumber - 1) * pageSize;
        List<TaskBatchDetailDTO> records = channelBatchTaskDetailMapper.getTaskIdsByTaskId(taskId, pageSize, startIndex);

        Long total = channelBatchTaskDetailMapper.getTotalByTaskId(taskId);

        MyOwnPage<TaskBatchDetailDTO> result = new MyOwnPage<>(records, total, pageNumber, pageSize);

        return result;
    }


    /**
     * Stream流去重
     *
     * @param keyExtractor
     * @param <T>
     * @return
     */
    protected <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    @Override
    public MyOwnPage<TaskBatchDetailDTO> getTaskBatchDetailDTOs(CharlieVO pageVO) {


        String corpId = StrUtil.isBlank(pageVO.getChooseCorpId()) ?
                channelService.getSubChannelCorpId(pageVO.getCorpId(), pageVO.getCooperationMode())
                :
                "('" + pageVO.getChooseCorpId() + "')";

        Page<TaskBatchDetailDTO> resultPage = channelBatchTaskMapper.getChannelCardByCorpId(corpId,
                pageVO.getIccid(), pageVO.getCooperationMode(), pageVO.getIsHasPackage(), new Page<>(pageVO.getPageNumber(), pageVO.getPageSize()));

        List<TaskBatchDetailDTO> records = resultPage.getRecords();
        if (records.size() > 0) {

            for (TaskBatchDetailDTO t : records) {

                Response<HcardInfo> response = pmsFeignClient.getCardByIccid(t.getIccid());

                Response.checkRemoteData(response);

                if (response.getData() != null) {

                    String status = response.getData().getStatus();

                    t.setCardStatus(status);

                }

            }

        }

        MyOwnPage<TaskBatchDetailDTO> result = new MyOwnPage<>(records, resultPage.getTotal(),
                pageVO.getPageNumber(), pageVO.getPageSize());

        return result;
    }

    @Override
    public BigDecimal getCashByCorpId(String corpId, String cooperationMode) {
        QueryWrapper<ChannelDistributorDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("corp_id", corpId);
        ChannelDistributorDetail channelDistributorDetail = channelDistributorDetailMapper.selectOne(queryWrapper);
        if (ChannelDistributorDetail.CooperationModeEnum.CONSIGNMENT.getType().equals(cooperationMode)) {
            return channelDistributorDetail.getDeposit().add(channelDistributorDetail.getCreditAmount());
        }
        return channelDistributorDetail.getA2zPreDeposit();
    }

    @Override
    public Response getAvilablePackages(String corpId, String cooperationMode, Long pageSize, Long pageNumber) {
        SubChannelDTO channel = channelMapper.selectChannel(corpId);

        boolean isSubChannel = false;
        if (Channel.ChannelTypeEnum.SUBCHANNEL.getValue().equals(channel.getType())) {
            isSubChannel = true;
            corpId = channel.getParentCorpId();
        }

        //先查出所有套餐组id，带id去pms查
        List<String> groupIds = channelPackageRelationMapper.getGroupIds(corpId, cooperationMode);

        if (ObjectUtil.isEmpty(groupIds)) {
            return Response.ok(new MyOwnPage<>(null, 0L, pageNumber, pageSize));
        }

        QueryWrapper<ChannelDistributorDetail> queryWrapper = new QueryWrapper<>();

        queryWrapper.eq("corp_id", corpId);
        ChannelDistributorDetail channelDistributorDetail = channelDistributorDetailMapper.selectOne(queryWrapper);

        String money = channelDistributorDetail.getCurrencyCode();

        if (isSubChannel) {
            return getSubChannelPackages(channel, groupIds, money, pageSize, pageNumber);
        }

        return pmsFeignClient.getAvilablePackages(groupIds, money, pageSize, pageNumber, corpId, cooperationMode);
    }

    private Response getSubChannelPackages(SubChannelDTO channel, List<String> groupIds, String money, Long pageSize, Long pageNumber) {
        List<CmsChannelPackageDetail> channelPackageDetails = channelPackageDetailMapper.selectList(
                Wrappers.lambdaQuery(CmsChannelPackageDetail.class)
                        .eq(CmsChannelPackageDetail::getCorpId, channel.getCorpId()));

        if (CollectionUtil.isEmpty(channelPackageDetails)) {
            return Response.ok(new MyOwnPage<>(null, 0L, pageNumber, pageSize));
        }

        List<String> packageIds = channelPackageDetails.stream().map(CmsChannelPackageDetail::getPackageId).collect(Collectors.toList());
        IPage<CanBuyPackageDTO> canBuyPackageDTOS = Response.getAndCheckRemoteData(pmsFeignClient.getSubChannelPackages(GetSubChannelVO.builder()
                .corpId(channel.getParentCorpId())
                .currencyCode(money)
                .groupIds(groupIds)
                .packageIds(packageIds)
                .pageNumber(pageNumber)
                .pageSize(pageSize)
                .build()));

        Map<String, CmsChannelPackageDetail> map = channelPackageDetails.stream().collect(Collectors.toMap(CmsChannelPackageDetail::getPackageId, a -> a, (k1, k2) -> k1));
        canBuyPackageDTOS.getRecords().forEach(info -> {

            if (map.get(info.getPackageId()).getPackagePrice() != null) {
                info.setPackagePrice(String.valueOf(map.get(info.getPackageId()).getPackagePrice()));
            } else {
                BigDecimal price = getPackagePrice(PackageVO.builder()
                        .corpId(channel.getParentCorpId())
                        .groupType(info.getGroupType())
                        .groupPrice(new BigDecimal(info.getGroupPrice()))
                        .packagePrice(new BigDecimal(info.getPackagePrice()))
                        .build());
                price = price.multiply(BigDecimal.valueOf(channel.getDiscount()).divide(BigDecimal.valueOf(100)));
                info.setPackagePrice(String.valueOf(price));
            }
            info.setGroupType("2");
        });

        return Response.ok(new MyOwnPage<>(canBuyPackageDTOS.getRecords(), canBuyPackageDTOS.getTotal(), pageNumber, pageSize));
    }

    @Override
    public Response<MyOwnPage<PackageDTO>> getAvilablePackagesDetail(AvlibablePackageVO packageVO) {

        String corpId = packageVO.getCorpId();
        String mcc = packageVO.getMcc();
        String packageName = packageVO.getPackageName();
        String packageNameEn = packageVO.getPackageNameEn();

        SubChannelDTO channel = channelMapper.selectChannel(corpId);

        boolean isSubChannel = false;
        if (Channel.ChannelTypeEnum.SUBCHANNEL.getValue().equals(channel.getType())) {
            isSubChannel = true;
            corpId = channel.getParentCorpId();
        }


        //先查出所有套餐组id，带id去pms查
        List<String> groupIds = channelPackageRelationMapper.getGroupIds(corpId, packageVO.getCooperationMode());

        if (ObjectUtil.isEmpty(groupIds)) {
            if (ChannelDistributorDetail.CooperationModeEnum.CONSIGNMENT.getType().equals(packageVO.getCooperationMode())) {
                return Response.ok(new MyOwnPage<>(null, 0L, packageVO.getPageNumber(), packageVO.getPageSize()));
            } else {
                groupIds.add("");
            }

        }

        QueryWrapper<ChannelDistributorDetail> queryWrapper = new QueryWrapper<>();

        queryWrapper.eq("corp_id", corpId);
        ChannelDistributorDetail channelDistributorDetail = channelDistributorDetailMapper.selectOne(queryWrapper);

        String money = channelDistributorDetail.getCurrencyCode();

        AvilablePackageVO avilablePackageVO = AvilablePackageVO.builder()
                .currencyCode(money)
                .groupIds(groupIds)
                .mcc(mcc)
                .packageName(packageName)
                .packageNameEn(packageNameEn)
                .pageNumber(packageVO.getPageNumber())
                .pageSize(packageVO.getPageSize())
                .corpId(corpId)
                .cooperationMode(packageVO.getCooperationMode())
                .build();

        Response<MyOwnPage<PackageDTO>> result;

        if (isSubChannel) {
            result = getSubChannelPackagesDetail(channel, avilablePackageVO);
        } else {
            result = pmsFeignClient.getAvilablePackagesDetail(avilablePackageVO);
        }

        if (result.getCode().equals(ResponseResult.SUCCESS.getCode()) && result.getData().getRecord() != null) {
            Set<String> mccSet = new HashSet<>();
            //获取该页数据的全部国家信息
            for (PackageDTO packageDTO : result.getData().getRecord()) {
                mccSet.addAll(packageDTO.getMccList());
            }
            //远程调用获取国家信息
            Map<String, OmsCountry> countryMap = omsFeignClient.getCountryInfoBymcclist(new ArrayList<>(mccSet)).ignoreThrow();
            QuerySpecialVO querySpecialVO = new QuerySpecialVO();
            //获取该页数据的全部packageId
            querySpecialVO.setPackageIds(result.getData().getRecord().stream().map(PackageDTO::getPackageId).collect(Collectors.toList()));
            //调用pms服务，根据套餐Id查询特殊国家备注信息
            Map<String, List<SpecialDetailAndRemarkDTO>> remarkMap = pmsFeignClient.selectRemarkByPackageId(querySpecialVO).ignoreThrow();
            //保留之前的for循环结构，以后有字段需要做变更可以在此处理
            for (PackageDTO p : result.getData().getRecord()) {
                //更新国家展示字段
                p.setMccDtoList(changeMccDtoList(p.getPackageId(),p.getMccList(), countryMap,remarkMap));
            }
        }

        return result;

    }

    /***
     * 更改mcc展示字段
     * @param packageId 套餐id
     * @param mccList mcc集合
     * @param countryMap 国家Map
     * @param remarkMap  备注信息map
     * @return
     */
    private List<MccDTO> changeMccDtoList(String packageId,List<String> mccList,Map<String, OmsCountry> countryMap,Map<String, List<SpecialDetailAndRemarkDTO>> remarkMap){
        List<MccDTO> mccDTOS = new ArrayList<>();
        //远程调用报错countryMap返回可能为空，详见ignoreThrow函数。或则mccList为空直接返回空集合
        if(CollectionUtil.isEmpty(mccList)||CollectionUtil.isEmpty(countryMap)){
            return mccDTOS;
        }
        for(String mcc:mccList){
            //没有该mcc国家信息，跳过
            if(!countryMap.containsKey(mcc)){
                continue;
            }
            MccDTO mccDTO =new MccDTO();
            mccDTO.setCountryCn(countryMap.get(mcc).getCountryCn());
            mccDTO.setCountryEn(countryMap.get(mcc).getCountryEn());
            mccDTO.setCountryTw(countryMap.get(mcc).getCountryTw());
            //如果没有特殊国家备注，或则该国家在特殊国家备注中没有查到，则将数据添加到返回参数集合中，跳过当前循环
            if(CollectionUtil.isEmpty(remarkMap)||!remarkMap.containsKey(packageId)){
                mccDTOS.add(mccDTO);
                continue;
            }
            //通过packageId获取该套餐下的所有特殊备注，并且按照mcc聚合。一个国家只能存在一个默认或则特殊规则
            Map<String, SpecialDetailAndRemarkDTO> mccRemarkMap = remarkMap.get(packageId).stream().collect(Collectors.toMap(SpecialDetailAndRemarkDTO::getMcc,v->v, (v1, v2) -> v1));
            if(mccRemarkMap.containsKey(mcc)){
                //国家后面添加特殊规则  例:中国(特殊规则)
                mccDTO.setCountryCn(countryMap.get(mcc).getCountryCn()+"("+mccRemarkMap.get(mcc).getRemarkCn()+")");
                mccDTO.setCountryEn(countryMap.get(mcc).getCountryEn()+"("+mccRemarkMap.get(mcc).getRemarkEn()+")");
            }
            mccDTOS.add(mccDTO);
        }
        return mccDTOS;
    }

    private Response getSubChannelPackagesDetail(SubChannelDTO channel, AvilablePackageVO avilablePackageVO) {
        Long pageNumber = avilablePackageVO.getPageNumber();
        Long pageSize = avilablePackageVO.getPageSize();
        Page<CmsChannelPackageDetail> channelPackageDetails = channelPackageDetailMapper.selectPage(new Page<>(-1, -1),
                Wrappers.lambdaQuery(CmsChannelPackageDetail.class)
                        .eq(CmsChannelPackageDetail::getCorpId, channel.getCorpId()));

        if (CollectionUtil.isEmpty(channelPackageDetails.getRecords())) {
            return Response.ok(new MyOwnPage<>(null, 0L, pageNumber, pageSize));
        }

        avilablePackageVO.setPackageIds(channelPackageDetails.getRecords().stream().map(CmsChannelPackageDetail::getPackageId).collect(Collectors.toList()));

        Page<PackageDTO> canBuyPackageDTOS = Response.getAndCheckRemoteData(pmsFeignClient.getSubChannelPackagesDetail(avilablePackageVO));

        Map<String, CmsChannelPackageDetail> map = channelPackageDetails.getRecords().stream()
                .collect(Collectors.toMap(CmsChannelPackageDetail::getPackageId, a -> a, (k1, k2) -> k1));
        canBuyPackageDTOS.getRecords().forEach(info -> {
            if (map.get(info.getPackageId()).getPackagePrice() != null) {
                info.setPackagePrice(String.valueOf(map.get(info.getPackageId()).getPackagePrice()));
            } else {
                BigDecimal price = getPackagePrice(PackageVO.builder()
                        .corpId(channel.getParentCorpId())
                        .groupType(info.getGroupType())
                        .groupPrice(new BigDecimal(info.getGroupPrice()))
                        .packagePrice(new BigDecimal(info.getPackagePrice()))
                        .build());
                price = price.multiply(BigDecimal.valueOf(channel.getDiscount()).divide(BigDecimal.valueOf(100)));
                info.setPackagePrice(String.valueOf(price));
            }
            info.setGroupType("2");
        });

        return Response.ok(new MyOwnPage<>(canBuyPackageDTOS.getRecords(), canBuyPackageDTOS.getTotal(), pageNumber, pageSize));
    }

    @Override
    public BigDecimal getPackagePrice(PackageVO packageVO) {

        QueryWrapper<ChannelDistributorDetail> queryWrapper1 = new QueryWrapper<>();

        queryWrapper1.eq("corp_id", packageVO.getCorpId());

        ChannelDistributorDetail channelDistributorDetail = channelDistributorDetailMapper.selectOne(queryWrapper1);

        BigDecimal price = null;

        //计算金额
        //若是非二次批价，则直接用价格，否则用折扣*套餐价
        //1：二次定价
        //2：非二次定价
        Boolean isSecondTime = "1".equals(packageVO.getGroupType());
        if (isSecondTime) {
            price = packageVO.getGroupPrice();
        } else {

            Integer discount = channelDistributorDetail.getDiscount();

            if (discount != null) {

                BigDecimal discountMath = new BigDecimal(discount);

                price = packageVO.getPackagePrice().multiply(discountMath).divide(new BigDecimal(100));
            } else {
                price = packageVO.getPackagePrice();
            }

        }
        return price;
    }

    @Override
    public Response buySingle(PackageVO packageVO) {

        log.debug("###############################开始套餐购买####################################");

        //查询渠道商
        Channel channel = channelMapper.selectOne(Wrappers.lambdaQuery(Channel.class).eq(Channel::getCorpId, packageVO.getCorpId()));

        Boolean isSubChannel = Channel.ChannelTypeEnum.SUBCHANNEL.getValue().equals(channel.getType());

        ChannelDistributorDetail parentChannelDetail = null;

        if (isSubChannel) {
            parentChannelDetail = channelDistributorDetailMapper.selectChannelDetail(channel.getParentCorpId());
        }

        //判断是不是促销套餐
        Set<String> packageSet = new HashSet<>();

        packageSet.add(packageVO.getPackageId());

        Response<List<com.ebupt.cmi.clientmanagement.feign.pms.domain.PackageVO>> response_1 = pmsFeignClient.queryPackagesByIds(packageSet);

        Response.getAndCheckRemoteData(response_1);

        //是否是促销套餐
        //1：是
        //2：否
        if ("1".equals(response_1.getData().get(0).getIsPromotion())) {

            //查询已经购买了几个该套餐
            Integer count = channelPackageCardMapper.getBuyPackageNum(packageVO.getPackageId(), packageVO.getIccid());

            if (++count > response_1.getData().get(0).getSaleLimit()) {
                return Response.error("The number of promotional packages purchased by this card has reached the upper limit");
            }

        }

        QueryWrapper<ChannelCard> queryWrapper = new QueryWrapper<>();

        queryWrapper.eq("iccid", packageVO.getIccid());

        queryWrapper.eq("corp_id", packageVO.getCorpId());

        queryWrapper.eq("cooperation_mode", packageVO.getCooperationMode());

        ChannelCard channelCard = channelCardMapper.selectOne(queryWrapper);

        if (channelCard == null) {

            String errorString = "Failed purchase。iccid：" + packageVO.getIccid() + "，package_id：" + packageVO.getCorpId();

            TraceLogger.infoMyOwnMsg(errorString);

            log.debug(errorString + "This card number is not found under this channel or not part of the current cooperation mode");

            return Response.error("This card number is not found under this channel or not part of the current cooperation mode");

        }

        log.debug("卡消息" + channelCard);

        //主卡状态查询
        Response<com.ebupt.cmi.clientmanagement.feign.pms.domain.CardVO> response = pmsFeignClient.queryCardsByIccid(channelCard.getIccid());

        Response.checkRemoteData(response);

        com.ebupt.cmi.clientmanagement.feign.pms.domain.CardVO cardVO = response.getData();

        log.debug("主卡消息" + cardVO);

        if (!cardVO.getStatus().equals("1")) {
            log.debug("主卡状态不正常，不可以购买套餐");
            String errorString = "Failed purchase。iccid：" + packageVO.getIccid() + "，package_id：" + packageVO.getCorpId();
            TraceLogger.infoMyOwnMsg(errorString);
            return Response.error("The status of the main card is abnormal, and the package cannot be purchased");

        }

        common.checkPackageCardForm(packageVO.getPackageId(), channelCard.getCardForm());

        ChannelDistributorDetail channelDetail = channelDistributorDetailMapper.selectChannelDetail(packageVO.getCorpId());

        if (!"1".equals(channel.getStatus()) || "2".equals(channelDetail.getIsSub()) || !"2".equals(channel.getCheckStatus())) {
            log.debug("该渠道商不允许订购套餐");
            String errorString = "Failed purchase。iccid：" + packageVO.getIccid() + "，package_id：" + packageVO.getCorpId();
            TraceLogger.infoMyOwnMsg(errorString);
            return Response.error("Purchase function is temporarily suspended");
        }

        log.debug("渠道商购买代销套餐，判断代销合约是否到期");
        ChannelDistributorDetail distributorDetail = isSubChannel ? parentChannelDetail : channelDetail;
        Date contractEndTime = distributorDetail.getContractEndTime();
        if (ChannelDistributorDetail.CooperationModeEnum.CONSIGNMENT.getType().equals(channelCard.getCooperationMode())
                && contractEndTime != null && contractEndTime.before(new Date())) {
            log.warn("代销合约已到期，不允许购买代销套餐");
            throw new BizException("The distribution contract has expired, and purchasing distribution model packages is not allowed");
        }
        if (!CollectionUtils.isEmpty(response_1.getData()) && ObjectUtil.isNotNull(response_1.getData().get(0))) {
            if (com.ebupt.cmi.clientmanagement.feign.pms.domain.PackageVO.deductionModelEnum.BINDING_MODE.getType().equals(response_1.getData().get(0).getDeductionModel())) {
                String orderRandom = Utils.cmiUuid(CmiUUID.ORDER_UNIQUE_ID);
                log.debug("走扣费模式为绑定的流程");
                common.bindPackage(BindPackageVo.builder()
                        .corpId(packageVO.getCorpId())
                        .imsi(cardVO.getImsi())
                        .iccid(cardVO.getIccid())
                        .msisdn(cardVO.getMsisdn())
                        .orderChannel("113")
                        .packageType("1")
                        .packageId(packageVO.getPackageId())
                        .orderUniqueID(orderRandom)
                        .build());
                //调用主卡过期刷新接口
                UpdateCardExpireTimeReq flushCardExpireTimeReq = UpdateCardExpireTimeReq
                        .builder()
                        .startTime(new Date())
                        .iccids(Collections.singletonList(cardVO.getIccid()))
                        .build();
                Response.checkRemoteData(pmsFeignClient.updateCardExpireTime(flushCardExpireTimeReq));
                return Response.ok();
            }
        }

        //计算金额
        BigDecimal price;

        BigDecimal subPrice = null;

        if (ChannelDistributorDetail.CooperationModeEnum.A2Z.getType().equals(packageVO.getCooperationMode())) {
            price = BigDecimal.ZERO;
        } else {
            //获取子渠道商价格
            if (isSubChannel) {

                List<String> groupIds = channelPackageRelationMapper.getGroupIds(channel.getParentCorpId(), ChannelDistributorDetail.CooperationModeEnum.CONSIGNMENT.getType());
                Response<PackageDTO> priceResponse = pmsFeignClient.getPackageById(groupIds,
                        parentChannelDetail.getCurrencyCode(), packageVO.getPackageId(),
                        channel.getParentCorpId());
                Boolean unSuccess = batchPackageBuyServiceImpl.validateResponse(priceResponse, ChannelDistributorDetail.CooperationModeEnum.CONSIGNMENT.getType());

                if (unSuccess) {
                    throw new BizException("#Bulk Purchase Error Record# Purchase failed");
                }

                PackageDTO priceInfo = priceResponse.getData();
                subPrice = packageVO.getPackagePrice();

                packageVO.setGroupPrice(new BigDecimal(priceInfo.getGroupPrice()));
                packageVO.setPackagePrice(new BigDecimal(priceInfo.getPackagePrice()));
                packageVO.setGroupType(priceInfo.getGroupType());
            }

            //若是非二次批价，则直接用价格，否则用折扣*套餐价
            //1：二次定价
            //2：非二次定价
            Boolean isSecondTime = "1".equals(packageVO.getGroupType());
            if (isSecondTime) {
                price = packageVO.getGroupPrice();
            } else {

                Integer discount = isSubChannel ? parentChannelDetail.getDiscount() : channelDetail.getDiscount();

                if (discount != null) {

                    BigDecimal discountMath = new BigDecimal(discount);

                    price = packageVO.getPackagePrice().multiply(discountMath).divide(new BigDecimal(100));

                } else {

                    price = packageVO.getPackagePrice();

                }

            }
        }

        ChannelSelfServiceImpl selfProxy = (ChannelSelfServiceImpl) beanFactory.getBean("channelSelfServiceImpl");

        selfProxy.confirmBuy(channelDetail, parentChannelDetail, packageVO, price, subPrice, channelCard, cardVO, response_1.getData().get(0));

        orderService.sendCancelLocationIfNeeded(packageVO.getPackageId(), new Date(), packageVO.getCorpId(), cardVO.getImsi(), cardVO.getIccid());

        return Response.ok();
    }


    /**
     * MultipartFile转file
     *
     * @param multipartFile
     * @return
     */
    private File multiToFile(MultipartFile multipartFile) {
        //选择用缓冲区来实现这个转换即使用java 创建的临时文件 使用 MultipartFile.transferto()方法 。
        File file = null;
        try {
            String originalFilename = multipartFile.getOriginalFilename();
            String[] filename = originalFilename.split("\\.");
            file = File.createTempFile(filename[0], filename[1]);
            multipartFile.transferTo(file);
            file.deleteOnExit();
        } catch (IOException e) {
            e.printStackTrace();
            log.warn(e.getMessage());
        }

        return file;
    }

    @GlobalTransactional(rollbackFor = Exception.class, timeoutMills = 30000, name = "buyPackage")
    @Transactional(rollbackFor = Exception.class)
    @SdcMultipleQueues(operationName = DisasterEnum.ORDER_CREATE)
    public void confirmBuy(ChannelDistributorDetail channelDistributorDetail,
                           ChannelDistributorDetail parentChannelDetail,
                           PackageVO packageVO,
                           BigDecimal packagePrice,
                           BigDecimal subPackagePrice,
                           ChannelCard channelCard,
                           com.ebupt.cmi.clientmanagement.feign.pms.domain.CardVO cardVO,
                           com.ebupt.cmi.clientmanagement.feign.pms.domain.PackageVO packageDetail) {
        log.debug("###############################开始扣费####################################");


        BatchPackageBuyService batchPackageBuyService = beanFactory.getBean(BatchPackageBuyService.class);
        ChannelDistributorDetail targetChannelDetail;

        String orderUniqueId = Utils.cmiUuid(CmiUUID.ORDER_UNIQUE_ID);
        Date orderDate = new Date();
        if (ObjectUtil.isNotNull(parentChannelDetail)) {
            log.debug("子渠道商套餐价格为: {}，当前押金为：{}", subPackagePrice, channelDistributorDetail.getDeposit());
            targetChannelDetail = parentChannelDetail;
            batchPackageBuyService.reduceDepositAndRecordFlow(channelDistributorDetail, subPackagePrice,
                    packageVO.getCooperationMode(), orderUniqueId, orderDate);
        } else {
            targetChannelDetail = channelDistributorDetail;
        }

        RebateTransVO rebateTransVO = RebateTransVO.builder()
                .channelDistributorDetail(targetChannelDetail)
                .corpId(targetChannelDetail.getCorpId())
                .currencyCode(targetChannelDetail.getCurrencyCode())
                .packagePrice(packagePrice)
                .cooperationMode(packageVO.getCooperationMode())
                .orderUniqueId(orderUniqueId)
                .orderDate(orderDate)
                .isGlobalTransactional("1")
                .build();

        batchPackageBuyServiceImpl.detectionTime(packageVO, packageDetail);
        commonservice.insertToTables(
                channelDistributorDetail, parentChannelDetail, packageVO,
                packagePrice, subPackagePrice, channelCard, cardVO,
                packageDetail, orderUniqueId, orderDate,rebateTransVO);


    }

    @Override
    public IPage<ChannelChargeRecord> getRecord(String corpId,
                                                Long pageSize,
                                                Long pageNumber) {

        QueryWrapper<ChannelChargeRecord> queryWrapper = new QueryWrapper<>();

        queryWrapper.eq("corp_id", corpId);
        queryWrapper.orderByDesc("charge_time");

        log.info("***********************渠道自服务查流水**********************************");

        return channelChargeRecordMapper.selectPage(new Page<>(pageNumber, pageSize), queryWrapper);

    }

    @Override
    public Page<OrderDTO> getOrders(String packageName,
                                    String packageNameEn,
                                    String iccid,
                                    String startDate,
                                    String endDate,
                                    String orderUserId,
                                    String corpId,
                                    Long pageSize,
                                    Long pageNumber,
                                    String cooperationMode,
                                    String revertCorpId) {
        QueryWrapper<ChannelOrderDetail> queryWrapper = new QueryWrapper<>();

        queryWrapper.between(StringUtils.hasLength(startDate), "order_date", startDate, endDate)
                .eq(StringUtils.hasLength(iccid), "iccid", iccid)
                .eq(StringUtils.hasLength(cooperationMode), "cooperation_mode", cooperationMode);
        Boolean isSubChannel = channelService.getIsSubChannel(corpId);

        if (!isSubChannel && !StringUtils.hasLength(revertCorpId)) {
            queryWrapper.eq("order_user_id", corpId);
        } else {
            queryWrapper.eq("corp_id", StringUtils.hasLength(revertCorpId) ? revertCorpId : corpId);
        }

        log.info("***********************订单查询*****************************");

        if (StringUtil.isNotBlank(packageNameEn)) {

            queryWrapper.like("name_en", packageNameEn);

        } else {

            queryWrapper.like(StringUtils.hasLength(packageName), "package_name", packageName);

        }

        Page<OrderDTO> orders = channelOrderDetailMapper.getOrders(new Page<>(pageNumber, pageSize), queryWrapper);

        Map<String, String> channelNameMap = getRevertChannel(corpId, cooperationMode).stream().collect(Collectors.toMap(RevertChannelDTO::getCorpId, RevertChannelDTO::getCorpName, (value1, value2) -> value1));

        for (OrderDTO orderDTO : orders.getRecords()) {

            if (StrUtil.isNotBlank(orderDTO.getSubCorpId())) {
                orderDTO.setRevertChannelName(channelNameMap.get(orderDTO.getSubCorpId()));
            } else {
                orderDTO.setRevertChannelName(channelNameMap.get(corpId));
            }

            if (StringUtil.isBlank(orderDTO.getPackageStatus())) {
                orderDTO.setIsUsed(null);
                continue;
            }

            Boolean justBoolean = "1".equals(orderDTO.getPackageStatus())
                    && (orderDTO.getEffectiveDay() == null || DateUtil.parse(orderDTO.getEffectiveDay()).after(new Date()));

            if (justBoolean) {
                orderDTO.setIsUsed(false);
                continue;
            }
            orderDTO.setIsUsed(true);

        }
        return orders;
    }

    @Override
    public Response deleteOrder(OrderDTO orderDTO, String corpId) {

        try {

            log.debug("**************************开始退订逻辑**************************");

            boolean isLockSuccess = redissonLock.tryLock(orderDTO.getPackageUniqueId());

            if (!isLockSuccess) {

                log.debug("加分布式锁失败");

                return Response.error("Failed to unsubscribe, the order is not allowed to operate, please try again later！");
            }

            ChannelOrder order = channelOrderMapper.selectOne(Wrappers.lambdaQuery(ChannelOrder.class)
                    .eq(ChannelOrder::getOrderUniqueId, orderDTO.getOrderId()));

            Optional.ofNullable(order).orElseThrow(() -> new BizException("this order does not been found"));

            ChannelOrderDetail orderDetail = channelOrderDetailMapper.selectOne(Wrappers.lambdaQuery(ChannelOrderDetail.class)
                    .eq(ChannelOrderDetail::getPackageUniqueId, orderDTO.getPackageUniqueId()));

            Optional.ofNullable(orderDetail).orElseThrow(() -> new BizException("this order does not been found"));

            if (!ChannelOrder.OrderStatusEnum.FINISHED.getValue().equals(orderDetail.getOrderStatus())) {
                throw new BizException("the status of order is not completed");
            }

            ChannelPackageCard card = channelPackageCardMapper.selectOne(Wrappers.lambdaQuery(ChannelPackageCard.class)
                    .eq(ChannelPackageCard::getPackageUniqueId, orderDTO.getPackageUniqueId()));

            if (!PackageStatus.NOT_ACTIVATY.getK().equals(card.getPackageStatus())) {
                throw new BizException("package has been used");
            }

            orderDTO.setId(order.getId());


            //子渠道商crop_id、金额
            orderDTO.setSubCorpId(order.getSubCorpId());

            orderDTO.setSubAmount(orderDetail.getSubAmount());

            //父渠道商crop_id、金额
            orderDTO.setCorpId(orderDetail.getOrderUserId());

            orderDTO.setAmount(orderDetail.getAmount());
            //取出代理类
            ChannelSelfService channelSelfService = SpringContextHolder.getBean(ChannelSelfService.class);

            channelSelfService.confirmDeleteOrder(orderDTO,orderDetail);

            return Response.ok("Unsubscribe successfully！");

        } catch (Exception ex) {

            throw ex;

        } finally {

            //如果该线程还持有该锁，那么释放该锁。如果该线程不持有该锁，说明该线程的锁已到过期时间，自动释放锁
            if (redissonLock.isHeldByCurrentThread(orderDTO.getPackageUniqueId())) {

                redissonLock.unlock(orderDTO.getPackageUniqueId());

            }
        }
    }

    @Override
    public PageResult<PurchasedPackageDTO> getPurchasedPackages(CurrentPackageForChannelVO packageVO) {
        String imsi = packageVO.getImsi();
        String corpId = packageVO.getCorpId();
        int pageNumber = packageVO.getPageNumber();
        int pageSize = packageVO.getPageSize();
        String startTime = packageVO.getStartTime();
        String endTime = packageVO.getEndTime();
        String packageName = packageVO.getPackageName();
        try {
            Page<PurchasedPackageDTO> result = channelPackageCardMapper.selectPurchasedPackagesByImsiAndCorpId(imsi, corpId, startTime,
                    endTime, packageName,
                    new Page<>(pageNumber, pageSize));
            List<PurchasedPackageDTO> records = result.getRecords();
            if (records.size() > 0) {
                records.forEach(item -> {
                    item.setAmount(item.getAmount().divide(BigDecimal.valueOf(100), 2, RoundingMode.UP));
                    if (PurchasedPackageDTO.PackageType.FLOW_POOL.getValue().equals(item.getPackageType())) {

                    }
                });
                String mccArrayStr = records.stream().map(PurchasedPackageDTO::getCurrentLocation).collect(Collectors.joining(","));
                // 根据mcc查询国家
                Map<String, String> mccMap = Response.getAndCheckRemoteData(omsFeignClient.countryCNEnByMccList(mccArrayStr));

                records.forEach(item -> {
                    String[] mccinfo = mccMap.getOrDefault(item.getCurrentLocation(), "未知,UNKNOW").split(",");
                    item.setCurrentLocation(mccinfo[0]);
                    item.setCurrentLocationEn(mccinfo[1]);
                    //TODO  调用统计服务已使用流量查询接口，获取已使用流量
                    FlowUsedVO flowUsedVO = FlowUsedVO.builder().packageUniqueId(item.getPackageUniqueId()).build();
                    String usedFlowMap = Response.getAndCheckRemoteData(statFeignClient.getUsedFlowByPkgUniqueId(flowUsedVO));
                    item.setUsedFlowBytes(usedFlowMap);
                });
            }
            return PageResult.of(result);
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            throw new BizException("Failed to query the purchased package", e);
        }
    }

    @Override
    public PageResult<ChannelLuReport> getHLuRecords(CurrentPackageForChannelVO packageVO) {
        String imsi = packageVO.getImsi();
        int pageNumber = packageVO.getPageNumber();
        int pageSize = packageVO.getPageSize();
        try {
            LambdaQueryWrapper<ChannelLuReport> queryWrapper = Wrappers.lambdaQuery(ChannelLuReport.class)
                    .orderByDesc(ChannelLuReport::getReportTime)
                    .select(ChannelLuReport::getReportTime, ChannelLuReport::getMcc, ChannelLuReport::getActiveType)
                    .and(wrapper -> wrapper.eq(ChannelLuReport::getHimsi, imsi).eq(ChannelLuReport::getReportType, "2"))
                    .or(wrapper -> wrapper.eq(ChannelLuReport::getImsi, imsi).eq(ChannelLuReport::getReportType, "1"));


            if (StringUtil.isNotBlank(packageVO.getStartTime())) {
                queryWrapper.between(ChannelLuReport::getReportTime, packageVO.getStartTime(), packageVO.getEndTime());
            }

            Page<ChannelLuReport> pageResult = channelLuReportMapper.selectPage(new Page<>(pageNumber, pageSize), queryWrapper);
            List<ChannelLuReport> records = pageResult.getRecords();
            if (records.size() > 0) {
                String mccArrayStr = records.stream().map(ChannelLuReport::getMcc).collect(Collectors.joining(","));
                // 根据mcc查询国家
                Map<String, String> mccMap = Response.getAndCheckRemoteData(omsFeignClient.countryCNEnByMccList(mccArrayStr));
                records.forEach(item -> {
                    String[] mccinfo = mccMap.getOrDefault(item.getMcc(), "未知,UNKNOW").split(",");
                    item.setMcc(mccinfo[0]);
                    item.setMccEn(mccinfo[1]);
                });
            }
            return PageResult.of(pageResult);
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            throw new BizException("Failed to query master card location update record", e);
        }
    }

    @Override
    public MyOwnPage<CardVO> getChannelCard(String corpId,
                                            Long pageSize,
                                            Long pageNumber,
                                            CardPageFormChannel cardPageFormChannel) {

        Long startIndex = (pageNumber - 1) * pageSize;

        List<String> imsiLists = channelCardMapper.getImsisByCorpId(corpId, pageSize, startIndex);

        if (imsiLists.size() < 1) {
            return null;
        }

        Long total = channelCardMapper.getImsitotalByCorpId(corpId);

        cardPageFormChannel.setImsiLists(imsiLists);

        Response<List<CardVO>> response = pmsFeignClient.pageListForChannelSelf(cardPageFormChannel);

        Response.checkRemoteData(response);

        return new MyOwnPage<>(response.getData(), total, pageNumber, pageSize);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmDeleteOrder(OrderDTO orderDTO,ChannelOrderDetail orderDetail) {

        ChannelDistributorDetail channelDistributorDetail = channelDistributorDetailMapper.selectChannelDetail(orderDTO.getCorpId());

        if ("1".equals(channelDistributorDetail.getUnsubscribeRule())) {
            Date orderDate = DateUtil.parse(orderDTO.getOrderDate());

            if (!DateTimeUtil.isThisMonth(orderDate.getTime())) {

                throw new BizException("Unsubscribe failed, the package does not belong to the current month");
            }
        } else {

            Date effectiveDate = DateUtil.parse(orderDTO.getEffectiveDay());

            if (effectiveDate.before(new Date())) {
                throw new BizException("Unsubscribe failed，the order does not belong to the effective day");
            }

        }


        if (!channelDistributorDetail.getCurrencyCode().equals(orderDTO.getCurrencyCode())) {

            throw new BizException("The order currency is different from the channel provider’s deposit, and the cancellation fails");

        }

        ChannelDistributorDetail subChannelDistributorDetail = null;

        if (StringUtils.hasLength(orderDTO.getSubCorpId()) && BigDecimal.ZERO.compareTo(orderDTO.getSubAmount()) != 0) {

            subChannelDistributorDetail = channelDistributorDetailMapper.selectChannelDetail(orderDTO.getSubCorpId());

        }


        Date unsubscribeTime = new Date();
        if (orderDTO.getAmount().compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal amount = orderDTO.getAmount();
            //获取营销活动流水,匹配活动id
            if (orderDetail != null){
                log.debug("===============查询是否存在营销额流水===============");
                LambdaQueryWrapper<CmsChannelMarketBillflow> queryWrapper = Wrappers.lambdaQuery(CmsChannelMarketBillflow.class)
                        .in(CmsChannelMarketBillflow::getOrderId, orderDetail.getId());
                List<CmsChannelMarketBillflow> billflow = cmsChannelMarketBillFlowMapper.selectList(queryWrapper);
                if (!billflow.isEmpty()) {
                    log.debug("===============存在营销金额流水,开始营销额退款===============");
                    //退订营销活动金额-退订营销活动流水
                    changeMarketingAmount(orderDTO.getOrderType(),orderDTO.getCorpId(), billflow);
                    //计算营销额总额
                    BigDecimal totalAmount = billflow.stream().map(CmsChannelMarketBillflow::getAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
                    //退款营销额为负数,这边需要把营销额总额取反
                    amount = amount.add(totalAmount);
                }
            }
            log.debug("####################################开始退还押金##########################################");
            if (amount.compareTo(BigDecimal.ZERO) != 0){
                //退还押金
                log.debug("当前押金为{}，应退金额为{}", channelDistributorDetail.getDeposit(), amount);
                channelDistributorsService.changeSubDeposit(channelDistributorDetail.getId(), amount, 1);
                channelService.channelBillRecord(BillRecordVo.builder()
                        .amount(amount)
                        .billType("7")
                        .corpId(channelDistributorDetail.getCorpId())
                        .orderId(orderDTO.getOrderId())
                        .orderSubOrUnsubDate(unsubscribeTime)
                        .cooperationMode("1")
                        .build());
            }
        }


        if (subChannelDistributorDetail != null) {
            log.debug("子渠道商订单，退子渠道商押金");
            channelDistributorDetailMapper.update(null, Wrappers.lambdaUpdate(ChannelDistributorDetail.class)
                    .setSql("deposit = deposit +" + orderDTO.getSubAmount())
                    .eq(ChannelDistributorDetail::getCorpId, orderDTO.getSubCorpId()));
            channelService.channelBillRecord(BillRecordVo.builder()
                    .amount(orderDTO.getSubAmount())
                    .billType("7")
                    .corpId(orderDTO.getSubCorpId())
                    .orderId(orderDTO.getOrderId())
                    .orderSubOrUnsubDate(unsubscribeTime)
                    .cooperationMode("1")
                    .build());
        }


        QueryWrapper<ChannelPackageCard> queryWrapper = new QueryWrapper<>();

        queryWrapper.eq("package_unique_id", orderDTO.getPackageUniqueId());

        channelPackageCardMapper.delete(queryWrapper);

        packageDirectionRelationMapper.delete(Wrappers.lambdaQuery(PackageDirectionRelation.class)
                .eq(PackageDirectionRelation::getPackageUniqueId, orderDTO.getPackageUniqueId()));


        ChannelOrderDetail channelOrderDetail = ChannelOrderDetail.builder()
                .unsubscribeTime(unsubscribeTime)
                .unsubscribeChannel("206")
                .orderStatus("3")
                .packageStatus("1")
                .build();

        QueryWrapper<ChannelOrderDetail> queryWrapper_1 = new QueryWrapper<>();

        queryWrapper_1.eq("package_unique_id", orderDTO.getPackageUniqueId());

        channelOrderDetailMapper.update(channelOrderDetail, queryWrapper_1);

        //更新order表
        Integer count = channelDistributorDetailMapper
                .selectDeleteOrderCount(orderDTO.getPackageUniqueId(), orderDTO.getId().toString());

        ChannelOrder channelOrder;

        if (count > 0) {

            channelOrder = ChannelOrder.builder()
                    .orderStatus(ChannelOrder.OrderStatusEnum.PARTIAL_UNSUBSCRIBE.getValue())
                    .build();

        } else {
            channelOrder = ChannelOrder.builder()
                    .orderStatus(ChannelOrder.OrderStatusEnum.UNSUBSCRIBE.getValue())
                    .build();
        }

        QueryWrapper<ChannelOrder> orderQueryWrapper = new QueryWrapper<>();

        orderQueryWrapper.eq("order_unique_id", orderDTO.getOrderId());

        channelOrderMapper.update(channelOrder, orderQueryWrapper);

        try {

            if (StringUtil.isNotBlank(channelDistributorDetail.getChannelUrl())) {

                //发送通知短信
                final UnsubscribeNotifyReq notifyReq = UnsubscribeNotifyReq.builder()
                        .unsubscribeType(UnsubscribeNotifyReq.UnsubscirbeTypeEnum.PARTIAL.getValue())
                        .notifyUrl(channelDistributorDetail.getChannelUrl() + noticeConfigProperties.getUrlSuffix().getUnsubscribe())
                        .iccid(Optional.ofNullable(orderDTO.getIccid()).map(Collections::singletonList).orElse(null))
                        .orderID(orderDTO.getOrderId())
                        .packageID(orderDTO.getPackageUniqueId())
                        .priceObj(PriceInfo.builder()
                                .price(orderDTO.getAmount().toString())
                                .currencyCode(channelDistributorDetail.getCurrencyCode())
                                .build())
                        .appKey(channelDistributorDetail.getAppKey())
                        .appSecret(channelDistributorDetail.getAppSecret())
                        .build();

                unsubscribeNotifyService.notifyAsync(notifyReq);

            }

        } catch (Exception ex) {

            log.warn("渠道自服务下发通知消息出现错误, ", ex);

        }

    }

    private void changeMarketingAmount(String orderType,String cropId,List<CmsChannelMarketBillflow> billflow) {
        //过期时间初始化
        Date outTime =new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            outTime = sdf.parse("2099-12-31 23:59:59");
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

        //根据营销额流水开始退款
        Date finalOutTime = outTime;
        billflow.forEach(x->{
            BigDecimal billFlowAmount = x.getAmount().negate();
            //查询营销账户信息
            CmsChannelMarketingRebate cmsChannelMarketingRebate;
            if (x.getRebateId() == null){
                cmsChannelMarketingRebate = cmsChannelMarketingRebateMapper.selectOne(Wrappers.lambdaQuery(CmsChannelMarketingRebate.class)
                        .eq(CmsChannelMarketingRebate::getCorpId, cropId)
                        //v6.10.2,新增rebateId用来校验流水和营销账户绑定
                        .eq(CmsChannelMarketingRebate::getActivityId, x.getActivityId()));
            }else {
                //查询营销账户信息
                cmsChannelMarketingRebate = cmsChannelMarketingRebateMapper.selectOne(Wrappers.lambdaQuery(CmsChannelMarketingRebate.class)
                        .eq(CmsChannelMarketingRebate::getCorpId, cropId)
                        //v6.10.2,新增rebateId用来校验流水和营销账户绑定
                        .eq(CmsChannelMarketingRebate::getId, x.getRebateId())
                        .eq(CmsChannelMarketingRebate::getActivityId, x.getActivityId()));
            }

            Long version = cmsChannelMarketingRebate.getVersion();

            //营销款是否过期 true过期 false未过期
            boolean isTimeOut = cmsChannelMarketingRebate.getExpiryTime().getTime() < System.currentTimeMillis();

            //查询渠道商所有活动未过期营销额总额
            List<CmsChannelMarketingRebate> rebate = cmsChannelMarketingRebateMapper.selectList(Wrappers.lambdaQuery(CmsChannelMarketingRebate.class)
                    .eq(CmsChannelMarketingRebate::getCorpId, cropId)
                    .ne(CmsChannelMarketingRebate::getRemainAmount, BigDecimal.ZERO)
                    .ge(CmsChannelMarketingRebate::getExpiryTime, new Date()));

            BigDecimal remainAmount =rebate.stream().map(CmsChannelMarketingRebate::getRemainAmount).reduce(BigDecimal.ZERO,BigDecimal::add);

            Date targetDate = new Date();
            //营销款过期,先执行清零操作,再执行退款操作
            if (isTimeOut){
                targetDate = finalOutTime;
                //开始清零-若营销金额为0则不写入清零流水
                if (cmsChannelMarketingRebate.getRemainAmount().compareTo(BigDecimal.ZERO) != 0){
                    //先查询当前渠道当前活动是否已存在退订流水
                    CmsChannelMarketBillflow cmsChannelMarketBillflow = cmsChannelMarketBillFlowMapper
                            .selectOne(new QueryWrapper<CmsChannelMarketBillflow>().lambda()
                                    .eq(CmsChannelMarketBillflow::getCorpId, cmsChannelMarketingRebate.getCorpId())
                                    .eq(CmsChannelMarketBillflow::getActivityId, cmsChannelMarketingRebate.getActivityId())
                                    .eq(CmsChannelMarketBillflow::getType, "6"));
                    if (cmsChannelMarketBillflow == null) {
                        //查找同一活动同一渠道商的其他营销账户数据
                        List<CmsChannelMarketingRebate> channelMarketingRebates1 = cmsChannelMarketingRebateMapper.selectList(Wrappers.lambdaQuery(CmsChannelMarketingRebate.class)
                                .eq(CmsChannelMarketingRebate::getCorpId, cropId)
                                .eq(CmsChannelMarketingRebate::getActivityId, cmsChannelMarketingRebate.getActivityId()));
                        BigDecimal remainAmount1 = channelMarketingRebates1.stream().map(CmsChannelMarketingRebate::getRemainAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

                        cmsChannelMarketBillFlowMapper.insert(CmsChannelMarketBillflow.builder()
                                .corpId(cmsChannelMarketingRebate.getCorpId())
                                .corpId(cropId)
                                .type("6")
                                .currencyCode(x.getCurrencyCode())
                                .amount(remainAmount1)
                                .deposit(BigDecimal.ZERO)
                                .activityId(cmsChannelMarketingRebate.getActivityId())
                                .createTime(new Date())
                                .totalAmount(remainAmount)
                                .totalOrderId("")
                                .build());
                    }
                }
                //营销账户退订
                int update =cmsChannelMarketingRebateMapper.update(null,Wrappers.lambdaUpdate(CmsChannelMarketingRebate.class)
                        .eq(CmsChannelMarketingRebate::getId,cmsChannelMarketingRebate.getId())
//                        .eq(CmsChannelMarketingRebate::getVersion,version)
//                        .set(CmsChannelMarketingRebate::getVersion,version + 1)
                        .set(CmsChannelMarketingRebate::getExpiryTime,targetDate)
                        .set(CmsChannelMarketingRebate::getRemainAmount,billFlowAmount));
                if (update <= 0){
                    throw new BizException("营销账户更新失败,请重试");
                }
                //退订营销额流水
                cmsChannelMarketBillFlowMapper.insert(CmsChannelMarketBillflow.builder()
                        .corpId(cropId)
                        .orderId(x.getOrderId())
                        .type("7".equals(orderType) ? "5" : "4")
                        .currencyCode(x.getCurrencyCode())
                        .amount(billFlowAmount)
                        .deposit(billFlowAmount)
                        .activityId(x.getActivityId())
                        .createTime(new Date())
                        .totalAmount(remainAmount.add(billFlowAmount))
                        .totalOrderId(x.getTotalOrderId())
                        .build());
            }else {
                //查询当前活动该营销账户信息,计算总额
                List<CmsChannelMarketingRebate> rebateList = cmsChannelMarketingRebateMapper.selectList
                        (Wrappers.lambdaQuery(CmsChannelMarketingRebate.class)
                                .eq(CmsChannelMarketingRebate::getActivityId,x.getActivityId())
                                .eq(CmsChannelMarketingRebate::getCorpId,cropId)
                                .ge(CmsChannelMarketingRebate::getExpiryTime, new Date()));
                BigDecimal remainAmount1 = rebateList.stream().map(CmsChannelMarketingRebate::getRemainAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
                //未过期,正常更新营销账户信息
                LambdaUpdateWrapper<CmsChannelMarketingRebate> updateWrapper = Wrappers.lambdaUpdate(CmsChannelMarketingRebate.class)
                        .eq(CmsChannelMarketingRebate::getId,cmsChannelMarketingRebate.getId())
//                        .eq(CmsChannelMarketingRebate::getVersion, version)
//                        .set(CmsChannelMarketingRebate::getVersion, version + 1)
                        .setSql("remain_amount = remain_amount"+"+"+billFlowAmount);
                int update = cmsChannelMarketingRebateMapper.update(cmsChannelMarketingRebate, updateWrapper);
                if (update <= 0){
                    throw new BizException("更新营销账户信息失败,请重试");
                }
                //退订营销额流水
                cmsChannelMarketBillFlowMapper.insert(CmsChannelMarketBillflow.builder()
                        .corpId(cropId)
                        .orderId(x.getOrderId())
                        .type("7".equals(orderType) ? "5" : "4")
                        .currencyCode(x.getCurrencyCode())
                        .amount(billFlowAmount)
                        .deposit(remainAmount1.add(billFlowAmount))
                        .activityId(x.getActivityId())
                        .createTime(new Date())
                        .totalAmount(remainAmount.add(billFlowAmount))
                        .totalOrderId(x.getTotalOrderId())
                        .build());
            }
        });
    }

    @Override
    public ExportVO exportOrder(ChannelSelfOrderExportVO orderVO) {
        Long taskId = null;
        try {
            Channel channel = Optional.ofNullable(channelMapper.selectById(orderVO.getCorpId())).orElseThrow(() -> new BizException("没有该渠道商"));
            String taskDesc = taskNamePrefix.replace("渠道商名称", channel.getCorpName()) + DateTimeUtil.getNowTime();
            String fileName = taskDesc + ".xls";
            String filePath = filePathPrefix + File.separator + fileName;

            taskId = Response.getAndCheckRemoteData(backFeignClient.addTask(BatchSyncfileTack.builder()
                    .taskStatus(BatchSyncfileTack.TaskStatus.PROCESSING.getStatus())
                    .taskDesc(taskDesc)
                    .filePath(filePath)
                    .corpId(orderVO.getUserId())
                    .fileName(fileName)
                    .build()));

            exportOrderTask.doTask(orderVO, filePathPrefix, taskId, fileName);
            return ExportVO.builder().taskId(taskId).taskName(fileName).build();
        } catch (Exception e) {
            if (taskId != null) {
                backFeignClient.updateTaskStatus(taskId, BatchSyncfileTack.TaskStatus.FAIL.getStatus(), null);
            }
            e.printStackTrace();
            throw new BizException("创建任务失败");
        }
    }

    @Override
    public ExportVO storeManageExport(String corpId, String userId, String cooperationMode, String chooseCorpId) {
        Long taskId = null;
        try {
            Channel channel = Optional.ofNullable(channelMapper.selectById(corpId)).orElseThrow(() -> new BizException("没有该渠道商"));

            String sufixFileTime = DateTimeUtil.getNowTime();
            String tmpFileDesc = taskDesc.replace("渠道商名称", channel.getCorpName()) + sufixFileTime;
            String tmpFileName = fileName.replace("渠道商名称", channel.getCorpName()) + sufixFileTime + ".xls";
            String tmpFilePath = filePath + File.separator + tmpFileName;
            taskId = Response.getAndCheckRemoteData(backFeignClient.addTask(BatchSyncfileTack.builder()
                    .fileName(tmpFileName)
                    .filePath(tmpFilePath)
                    .taskDesc(tmpFileDesc)
                    .corpId(userId)
                    .taskStatus(BatchSyncfileTack.TaskStatus.PROCESSING.getStatus())
                    .build()));
            storeManageExportTask.doTask(corpId, cooperationMode, tmpFileName, filePath, taskId, chooseCorpId);
            return ExportVO.builder().taskId(taskId).taskName(tmpFileName).build();
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("导出失败");
        }
    }

    @Override
    public ChannelRefuelPage selectRefuelList(String corpId, RefuelInputVO refuelInputVO) {

        boolean isSubChannel = false;
        String subCorpId = null;
        Channel channel = channelMapper.selectOne(Wrappers.lambdaQuery(Channel.class).eq(Channel::getCorpId, corpId));
        if (Channel.ChannelTypeEnum.SUBCHANNEL.getValue().equals(channel.getType())) {
            subCorpId = corpId;
            corpId = channel.getParentCorpId();
            isSubChannel = true;
        }

        List<String> gropIdList = channelPackageRelationMapper.getGroupIds(corpId, null);
        if (!CollectionUtils.isEmpty(gropIdList)) {
            gropIdList = pmsFeignClient.selectSecondaryPricing(gropIdList).getData();
            refuelInputVO.setGroupID(gropIdList);
        }
        List<RefuelOutVo> refuelPacks = Response.getAndCheckRemoteData(pmsFeignClient.selectRefuelPage(refuelInputVO));
        ChannelDistributorDetail channelDistributorDetail = channelDistributorDetailMapper
                .selectOne(Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                        .select(ChannelDistributorDetail::getCurrencyCode, ChannelDistributorDetail::getDiscount)
                        .eq(ChannelDistributorDetail::getCorpId, corpId));

        if (channelDistributorDetail.getDiscount() != null && !CollectionUtils.isEmpty(refuelPacks)) {
            calculateDiscountedPrice(refuelPacks, channelDistributorDetail.getDiscount());
        }

        if (ChannelDistributorDetail.CooperationModeEnum.A2Z.getType().equals(refuelInputVO.getCooperationMode())) {
            refuelPacks.forEach(refuelPack -> {
                refuelPack.setUsd(BigDecimal.ZERO);
                refuelPack.setHkd(BigDecimal.ZERO);
                refuelPack.setCny(BigDecimal.ZERO);
                refuelPack.setSpUsd(BigDecimal.ZERO);
                refuelPack.setSpHkd(BigDecimal.ZERO);
                refuelPack.setSpCny(BigDecimal.ZERO);
            });
        }

        if (isSubChannel) {
            ChannelDistributorDetail subChannelDetail = channelDistributorDetailMapper
                    .selectOne(Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                            .select(ChannelDistributorDetail::getCurrencyCode, ChannelDistributorDetail::getRefuelProfitMargin)
                            .eq(ChannelDistributorDetail::getCorpId, subCorpId));
            refuelPacks.forEach(refuelPack -> {
                refuelPack.setUsd(refuelPack.getUsd().multiply(BigDecimal.valueOf(subChannelDetail.getRefuelProfitMargin())).divide(BigDecimal.valueOf(100)));
                refuelPack.setHkd(refuelPack.getHkd().multiply(BigDecimal.valueOf(subChannelDetail.getRefuelProfitMargin())).divide(BigDecimal.valueOf(100)));
                refuelPack.setCny(refuelPack.getCny().multiply(BigDecimal.valueOf(subChannelDetail.getRefuelProfitMargin())).divide(BigDecimal.valueOf(100)));
                if (refuelPack.getSpUsd() != null) {
                    refuelPack.setSpUsd(refuelPack.getSpUsd().multiply(BigDecimal.valueOf(subChannelDetail.getRefuelProfitMargin())).divide(BigDecimal.valueOf(100)));
                }

                if (refuelPack.getSpHkd() != null) {
                    refuelPack.setSpHkd(refuelPack.getSpHkd().multiply(BigDecimal.valueOf(subChannelDetail.getRefuelProfitMargin())).divide(BigDecimal.valueOf(100)));
                }

                if (refuelPack.getSpCny() != null) {
                    refuelPack.setSpCny(refuelPack.getSpCny().multiply(BigDecimal.valueOf(subChannelDetail.getRefuelProfitMargin())).divide(BigDecimal.valueOf(100)));
                }

            });
        }

        return ChannelRefuelPage.builder().refuelOutVoList(refuelPacks).currencyCode(channelDistributorDetail.getCurrencyCode()).build();
    }

    private void calculateDiscountedPrice(List<RefuelOutVo> data, Integer discount) {
        data.forEach(item -> {
            item.setCny(orderService.calculate(item.getCny(), discount).setScale(2, RoundingMode.UP))
                    .setHkd(orderService.calculate(item.getHkd(), discount).setScale(2, RoundingMode.UP))
                    .setUsd(orderService.calculate(item.getUsd(), discount).setScale(2, RoundingMode.UP));
        });
    }

    @Component
    @RequiredArgsConstructor
    private static class StoreManageExportTask {
        @Value("${store-export.querysize}")
        private int BATCHSIZE;
        //每批次查询数量
//        private static int BATCHSIZE = 30000;

        private final BackFeignClient backFeignClient;
        private final ChannelMapper channelMapper;
        private final ChannelCardMapper channelCardMapper;
        private final PmsFeignClient pmsFeignClient;
        private final ChannelService channelService;

        @Async
        public void doTask(String corpId, String cooperationMode, String fileName, String filePath, Long taskId, String chooseCorpId) {
            try {
                String corpIds = channelService.getSubChannelCorpId(corpId, cooperationMode);

                corpIds = StrUtil.isBlank(chooseCorpId) ? corpIds : "('" + chooseCorpId + "')";

                LambdaQueryWrapper<ChannelCard> wrapper = Wrappers.lambdaQuery(ChannelCard.class)
                        .inSql(ChannelCard::getCorpId, corpIds.replace("(", "").replace(")", ""))
                        .eq(ChannelCard::getCardType, CardTypeEnum.H_CARD.getType())
                        .eq(ChannelCard::getCooperationMode, cooperationMode);

                Map<String, String> corpNameMap = channelMapper.selectList(Wrappers.lambdaQuery(Channel.class)
                                .inSql(Channel::getCorpId, corpIds.replace("(", "").replace(")", "")))
                        .stream()
                        .collect(Collectors.toMap(Channel::getCorpId, Channel::getCorpName));
                log.debug("渠道商名称map: " + corpNameMap);

                int cardCount = channelCardMapper.selectCount(wrapper);

                File fileDir = new File(filePath);
                if (!fileDir.exists()) {
                    if (!fileDir.mkdirs()) {
                        throw new RuntimeException("创建文件夹失败");
                    }
                }

                int startPage = 0;
                List<CardForStoreExport> cardList;
                ExportExcel ee = parseAnnotations(CardForStoreExport.class);
                //分页查询取出数据
                while (startPage <= cardCount) {
                    log.debug("分页查询并处理第{}-{}行数据", startPage, startPage + BATCHSIZE);
                    cardList = getCardInfo(corpIds, corpNameMap, cooperationMode, startPage, BATCHSIZE);
                    parseBean(cardList, ee);
                    startPage += BATCHSIZE;
                }

                CommonUtils.doFile(filePath, fileName, WorkbookUtil.createWorkBook(ee));
                backFeignClient.updateTaskStatus(taskId, BatchSyncfileTack.TaskStatus.FINISH.getStatus(), null);
            } catch (Exception e) {
                backFeignClient.updateTaskStatus(taskId, BatchSyncfileTack.TaskStatus.FAIL.getStatus(), null);
                log.error("", e);
            }
        }

        private List<CardForStoreExport> getCardInfo(String corpId, Map<String, String> corpNameMap, String cooperationMode, int startPage, int batchSize) {
            List<ChannelCard> channelCards = channelCardMapper.getCardByPage(corpId, CardTypeEnum.H_CARD.getType(), startPage, batchSize, cooperationMode);
            Map<String, List<String>> map = channelCards.stream().collect(Collectors.toMap(ChannelCard::getIccid,
                    v -> Arrays.asList(DateTimeUtil.formatTime(v.getCreateTime()), corpNameMap.getOrDefault(v.getCorpId(), ""))));

            List<String> iccidList = channelCards.stream().map(ChannelCard::getIccid).collect(Collectors.toList());
            if (iccidList.size() == 0) {
                return new ArrayList<>();
            } else {
                List<CardForStoreExport> pmsCards = Response.getAndCheckRemoteData(pmsFeignClient.getCardsByiccidList(iccidList));
                pmsCards.forEach(e -> {
                    e.setExcelCreateTime(map.get(e.getIccid()).get(0));
                    e.setExcelChannelName(map.get(e.getIccid()).get(1));
                });

                return pmsCards;
            }
        }

        private void parseBean(List<?> values, ExportExcel ee) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
            AnnotationParser annotationParser = new AnnotationParser();
            Method parseValueByAnnotation = annotationParser.getClass().getDeclaredMethod("parseValueByAnnotation", Object.class, ExportExcel.class);
            parseValueByAnnotation.setAccessible(true);
            for (Object obj : values) {
                parseValueByAnnotation.invoke(annotationParser, obj, ee);
            }
        }
    }

    @Component
    @AllArgsConstructor
    private static class ExportOrderTask {
        private BackFeignClient backFeignClient;
        private ChannelOrderDetailMapper channelOrderDetailMapper;
        private ChannelService channelService;
        private ChannelMapper channelMapper;

        @Async
        public void doTask(ChannelSelfOrderExportVO orderVO, String filePath, Long taskId, String fileName) {
            try {
                File fileDir = new File(filePath);
                if (!fileDir.exists()) {
                    if (!fileDir.mkdirs()) {
                        throw new RuntimeException("创建文件夹失败");
                    }
                }

                QueryWrapper<ChannelOrderDetail> queryWrapper = new QueryWrapper<>();

                queryWrapper.between(StringUtils.hasLength(orderVO.getStartDate()), "order_date", orderVO.getStartDate(), orderVO.getEndDate())
                        .eq(StringUtils.hasLength(orderVO.getOrderUserId()), "order_user_id", orderVO.getOrderUserId())
                        .eq(StringUtils.hasLength(orderVO.getIccid()), "iccid", orderVO.getIccid())
                        .eq(StringUtils.hasLength(orderVO.getCooperationMode()), "cooperation_mode", orderVO.getCooperationMode());
                boolean isSubChannel = channelService.getIsSubChannel(orderVO.getCorpId());

                if (isSubChannel) {
                    queryWrapper.eq("sub_corp_id", orderVO.getCorpId());
                } else {
                    if (StringUtils.hasLength(orderVO.getRevertCorpId())) {
                        queryWrapper.eq(!orderVO.getRevertCorpId().equals(orderVO.getCorpId()), "sub_corp_id", orderVO.getRevertCorpId());
                        queryWrapper.eq(orderVO.getRevertCorpId().equals(orderVO.getCorpId()), "corp_id", orderVO.getCorpId());
                    } else {
                        List<String> subCorpIds = channelMapper.selectSubChannelIds(orderVO.getCorpId());
                        queryWrapper.and(wrapper -> wrapper.eq("corp_id", orderVO.getCorpId()).or(!CollectionUtils.isEmpty(subCorpIds)).in("sub_corp_id", subCorpIds));
                    }
                }

                if (StringUtil.isNotBlank(orderVO.getPackageNameEn())) {

                    queryWrapper.like("name_en", orderVO.getPackageNameEn());

                } else {

                    queryWrapper.like(StringUtils.hasLength(orderVO.getPackageName()), "package_name", orderVO.getPackageName());

                }

                List<OrderDTO> exportOrder = channelOrderDetailMapper.getExportOrder(queryWrapper);

                ChannelSelfService channelSelfService = SpringContextHolder.getBean(ChannelSelfService.class);

                Map<String, String> channelNameMap = channelSelfService.getRevertChannel(orderVO.getCorpId(), orderVO.getCooperationMode())
                        .stream().collect(Collectors.toMap(RevertChannelDTO::getCorpId, RevertChannelDTO::getCorpName, (value1, value2) -> value1));

                List<SubOrderDTO> subOrderDTOS = new ArrayList<>();
                exportOrder.forEach(order -> {
                    if (StrUtil.isNotBlank(order.getSubCorpId())) {
                        order.setRevertChannelName(channelNameMap.get(order.getSubCorpId()));
                    } else {
                        order.setRevertChannelName(channelNameMap.get(orderVO.getCorpId()));
                    }

                    if (StrUtil.isNotBlank(order.getUnsubscribeTime())) {
                        order.setUpdateTime(order.getUnsubscribeTime());
                    } else {
                        order.setUpdateTime(order.getCreateTime());
                    }
                    if (orderVO.getIsSubChannel()) {
                        SubOrderDTO subOrderDTO = new SubOrderDTO();
                        BeanUtils.copyProperties(order, subOrderDTO);
                        subOrderDTOS.add(subOrderDTO);
                    }
                });
                if (orderVO.getIsSubChannel()) {
                    ExcelUtils.doExport(filePath, subOrderDTOS, fileName, SubOrderDTO.class);
                } else {
                    ExcelUtils.doExport(filePath, exportOrder, fileName, OrderDTO.class);
                }

                backFeignClient.updateTaskStatus(taskId, BatchSyncfileTack.TaskStatus.FINISH.getStatus(), null);
            } catch (Exception e) {
                e.printStackTrace();
                backFeignClient.updateTaskStatus(taskId, BatchSyncfileTack.TaskStatus.FAIL.getStatus(), null);
            }
        }
    }

    @Override
    public PageResult<UserPackageVO> userPackagePageList(UserPackageForm form) {
        LambdaQueryWrapper<ChannelPackageCard> wrapper = new LambdaQueryWrapper<ChannelPackageCard>()
                .eq(ChannelPackageCard::getCorpId, form.getCorpId())
                .eq(StringUtils.hasLength(form.getIccid()), ChannelPackageCard::getIccid, form.getIccid())
                //2-已激活
                .eq(ChannelPackageCard::getPackageStatus, "2")
                //1-普通套餐
                .eq(ChannelPackageCard::getPackageType, "1")
                //是否支持加油包，1-是
                .eq(ChannelPackageCard::getSupportRefuel, "1")
                .eq(ChannelPackageCard::getCooperationMode, form.getCooperationMode())
                .orderByDesc(ChannelPackageCard::getCreateTime);

        int current = Optional.ofNullable(form.getCurrent()).filter(m -> m > 1).orElse(1),
                size = Optional.ofNullable(form.getSize()).filter(m -> m > 0).orElse(10);

        Page<ChannelPackageCard> page = channelPackageCardMapper.selectPage(new Page<>(current, size), wrapper);

        List<UserPackageVO> result = new ArrayList<>(size);
        UserPackageVO vo;
        for (ChannelPackageCard entiiy : page.getRecords()) {
            vo = new UserPackageVO();
            BeanUtils.copyProperties(entiiy, vo);
            PackageFlowLimitEnums type = flowLimitType(entiiy.getFlowLimitType());

            vo.setFlowLimitSum(unitConversion(entiiy.getFlowLimitSum()));

            BigDecimal refuelFlow = channelPackageCardService.purchasedPackageRefuelFlow(entiiy.getPackageUniqueId(), type,entiiy.getPeriodUnit());
            vo.setPurchasedPackageRefuelFlow(unitConversion(refuelFlow));

            vo.setUsedHighSpeedFlow(unitConversion(getUsedFlow(entiiy, refuelFlow)));

            vo.setDaysRemaining(Utils.getIntervalDays(entiiy.getExpireTime(), new Date()));

            vo.setRemainderCycle(Utils.getPackageRemainCycle(entiiy.getPeriodUnit(),entiiy.getKeepPeriod(), entiiy.getActiveTime(), new Date()));

            result.add(vo);
        }

        return PageResult.ofPage(result, current, size, page.getTotal());
    }

    @Override
    public CreateOrderResp buyPackageRefuel(BuyPackageRefuelVO buy) {

        CreateOrderReq req = new CreateOrderReq()
                .setOrderChannel(OrderChannel.WEB.getType())
                .setUserId(buy.getCorpId())
                .setRole(CreateOrderReq.RoleTypeEnum.CHANNEL.getRoleType())
                .setIncludeCard(0)
                .setIsRefuel("0")
                .setRefuelingId(buy.getPackageRefuelId())
                .setDataBundleId(buy.getPackageId())
                .setIccid(buy.getIccid());

        if (ObjectUtils.isEmpty(buy.getQuantity()) && !ObjectUtils.isEmpty(buy.getType())) {
            req.setQuantity(buy.getType() == 1 ? 1 : null);
        } else if (!ObjectUtils.isEmpty(buy.getQuantity()) && ObjectUtils.isEmpty(buy.getType())) {
            req.setQuantity(buy.getQuantity());
        } else {
            throw new BizException("请根据流量限制类型填写type和quantity的值");
        }
        log.info("渠道自服务购买加油包");
        return orderService.createOrder(req);

    }

    @Override
    public BigDecimal getUsedFlow(ChannelPackageCard entiiy, BigDecimal refuelFlow) {
        if (ObjectUtils.isEmpty(entiiy.getActiveTime()) || ObjectUtils.isEmpty(entiiy.getExpireTime())) {
            return BigDecimal.ZERO;
        }
        String date = DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDateTime.now());
        String key, flow;
        BigDecimal result;
        String refuelFlowInRedis, refuelkey;
        if (PackageFlowLimitEnums.DAY.getUnit().equals(entiiy.getFlowLimitType())) {
            //24小时  已使用高速流量= 套餐流量上限 + 当日已购买的加油包流量 - 当日剩余的流量
            //2025.6月餐支持自然月重置，获取下标方法改造
            int index = Utils.getIndex(entiiy.getPeriodUnit(),entiiy.getActiveTime(), new Date());
            //获取套餐剩余流量
            flow = String.valueOf(Optional.ofNullable(flowOperationUtils.getDayRemainFlow(entiiy.getPackageUniqueId(), RemianFlowTypeEnum.PACKAGE.getType(), Long.valueOf(index)))
                    .orElse(BigDecimal.ZERO));
            //获取加油包剩余流量
            refuelFlowInRedis = String.valueOf(Optional.ofNullable(flowOperationUtils.getDayRemainFlow(entiiy.getPackageUniqueId(), RemianFlowTypeEnum.REFUEL.getType(), Long.valueOf(index)))
                    .orElse(BigDecimal.ZERO));

            result = Optional.ofNullable(entiiy.getFlowLimitSum()).orElse(new BigDecimal(0))
                    .add(refuelFlow)
                    .subtract(new BigDecimal(new BigDecimal(flow).compareTo(BigDecimal.ZERO) > 0 ? flow : "0"))
                    .subtract(new BigDecimal(refuelFlowInRedis));
            log.info("已使用高速流量(单日限量-24小时)[{}]= 套餐流量上限[{}] + 当日已购买的加油包流量[{}] " +
                            "- 查询当日套餐剩余的流量[{}] - 单日加油包流量剩余值[{}]",
                    result, entiiy.getFlowLimitSum(), refuelFlow, flow, refuelFlowInRedis);
        } else if (PackageFlowLimitEnums.CYCLE.getUnit().equals(entiiy.getFlowLimitType())) {
            //周期限量套餐获取剩余流量
            flow = String.valueOf(Optional.ofNullable(flowOperationUtils.getCycleRemainFlow(entiiy.getPackageUniqueId(), RemianFlowTypeEnum.PACKAGE.getType()))
                    .orElse(BigDecimal.ZERO));

            //周期限量加油包获取剩余流量
            refuelFlowInRedis = String.valueOf(Optional.ofNullable(flowOperationUtils.getCycleRemainFlow(entiiy.getPackageUniqueId(), RemianFlowTypeEnum.REFUEL.getType()))
                    .orElse(BigDecimal.ZERO));

            result = Optional.ofNullable(entiiy.getFlowLimitSum()).orElse(new BigDecimal(0))
                    .add(refuelFlow)
                    .subtract(new BigDecimal(new BigDecimal(flow).compareTo(BigDecimal.ZERO) > 0 ? flow : "0"))
                    .subtract(new BigDecimal(refuelFlowInRedis));
            log.info("已使用高速流量(周期内限量)[{}] = 套餐流量上限[{}] + 已购买的加油包流量[{}] " +
                            "- 查询套餐剩余流量[{}] - 加油包流量剩余值[{}]",
                    result, entiiy.getFlowLimitSum(), refuelFlow, flow, refuelFlowInRedis);
        } else {
            throw new BizException("流量限制类型错误");
        }
        return result;
    }

    @Override
    public Response<Void> upccControl(UpccControlReq upccControlReq) {
        String iccid = upccControlReq.getIccid();
        if (packageEndService.channelApiAccessCheck(upccControlReq.getCropId(), RoleEnum.CHANNEL.getRole(), null, iccid, null)) {
            throw new BizException(ApiResponseEnum.CARD_NO_PERMISSION);
        }

        String upccTemplateId = upccControlReq.getUpccTemplateId();
        if (!checkChannelPermission(upccTemplateId, upccControlReq.getCropId())) {
            throw new BizException(ApiResponseEnum.NOT_HAVE_PERMISSION2);
        }

        String packageId = upccControlReq.getPackageId();
        ChannelPackageCard activatedPackage = getActivatedPackage(iccid, packageId);
        String packageUniqueId = activatedPackage.getPackageUniqueId();
        HcardInfo hcardInfo = pmsFeignClient.getCardByImsi(activatedPackage.getImsi()).get();
        if (!packageUniqueId.equals(hcardInfo.getUpccSignPackageUniqueId())) {
            throw new BizException(ApiResponseEnum.PACKAGE_NOT_USED);
        }

        HimsiStatusAndLocationDTO userLocation = null;
        try {
            userLocation = packageEndService.getUserLocation(HimsiStatusAndLocationVO.builder()
                    .iccid(iccid)
                    .userId(upccControlReq.getCropId())
                    .build());
        } catch (Exception e) {
            log.error("位置查询失败。", e);
            throw new BizException(ApiResponseEnum.NOT_HAVE_LOCATION);
        }
        if (userLocation == null || StringUtils.isEmpty(userLocation.getMobileCountryCode())) {
            throw new BizException(ApiResponseEnum.NOT_HAVE_LOCATION);
        }
        SurfInfoCard surfInfoCard = getSurfInfoCard(userLocation.getMobileCountryCode(), activatedPackage);
        log.debug("当前卡当前套餐使用的上网卡imsi: {}及类型: {}", surfInfoCard.getImsi(), surfInfoCard.getCardType().equals("2") ? "V" : "H");

        UpccTemplate upccTemplate = pmsFeignClient.getUpccSignIdByTemplateId(upccTemplateId).get();
        String upccSignBizId = upccTemplate.getSignId();
        String supportHotspot = upccTemplate.getSupportHotspot();
        long upccSpeed = upccTemplate.getUnit().equals("1") ? upccTemplate.getRate() * 1024L : upccTemplate.getRate() * 1024L * 1024;
        boolean surfUsedV = surfInfoCard.getCardType().equals(ChannelSurfInfo.InternetType.V.getType());
        if (surfUsedV) {
            PmsCardpoolVcardRelation vCardInfo = pmsFeignClient.getVcardInfo(surfInfoCard.getImsi()).get();
            if ("2".equals(vCardInfo.getIsSignUpcc())) {
                throw new BizException(ApiResponseEnum.NOT_UPCC_SIGN);
            }
            if (!upccSignBizId.equals(vCardInfo.getUpccSignBizId())) {
                surfInfoCard.setOldUpccSignBizId(vCardInfo.getUpccSignBizId());
                surfInfoCard.setMsisdn(vCardInfo.getMsisdn());
                surfInfoCard.setNewUpccSingBizId(upccSignBizId);
                signUpcc(surfInfoCard);
                if (upccSpeed > slowUpccSpeed) {
                    if ("1".equals(supportHotspot)) {
                        upccSignBizId = slowSpeedUpccId_y;
                    } else {
                        upccSignBizId = slowSpeedUpccId_n;
                    }
                }
                if (!upccSignBizId.equals(hcardInfo.getUpccSignBizId())) {
                    SurfInfoCard infoCard = new SurfInfoCard(hcardInfo.getImsi(), hcardInfo.getMsisdn(),
                            ChannelSurfInfo.InternetType.H.getType(), hcardInfo.getUpccSignBizId(), upccSignBizId, null);
                    signUpcc(infoCard);
                }
            }
        } else {
            if (!upccSignBizId.equals(hcardInfo.getUpccSignBizId())) {
                surfInfoCard.setMsisdn(hcardInfo.getMsisdn());
                surfInfoCard.setNewUpccSingBizId(upccSignBizId);
                surfInfoCard.setOldUpccSignBizId(hcardInfo.getUpccSignBizId());
                signUpcc(surfInfoCard);
            }
        }


        channelPackageCardMapper.update(null, Wrappers.lambdaUpdate(ChannelPackageCard.class)
                .set(ChannelPackageCard::getSpeedControlId, upccTemplateId)
                .eq(ChannelPackageCard::getPackageUniqueId, packageUniqueId));
        boolean needRecover = saveOrUpdateRedis(activatedPackage);

        log.debug("是否需要进行恢复判断结果：{}", needRecover);
        if (needRecover) {
            long expireTime = Utils.resumeDelayQueueEffectiveTimeCount(activatedPackage.getPeriodUnit(), activatedPackage.getActiveTime());
            throwRabbitMq(surfInfoCard.getSurfInfoList(), activatedPackage, expireTime);
            redisTemplate.opsForValue().set(String.format(BizConstants.PACKAGE_RECOVER_KEY, packageUniqueId), "-1", expireTime, TimeUnit.MILLISECONDS);
        }

        return Response.okForApi("0000000");
    }

    @Override
    public List<RevertChannelDTO> getRevertChannel(String corpId, String cooperationMode) {
        String corpIds = channelService.getSubChannelCorpId(corpId, cooperationMode);
        return channelMapper.getRevertChannel(corpIds);
    }

    @Override
    public void addBlankCardOrders(AddBlankCardVO addBlankCardVO) {
        CmsChannelBlankcardOrder order = CmsChannelBlankcardOrder.builder()
                .address(addBlankCardVO.getAddress())
                .addressee(addBlankCardVO.getRecipient())
                .cardForm(addBlankCardVO.getCardForm())
                .cooperationMode(addBlankCardVO.getCooperationMode())
                .orderUniqueId(Utils.cmiUuid(CmiUUID.ORDER_UNIQUE_ID))
                .orderUserId(addBlankCardVO.getOrderUserId())
                .count(addBlankCardVO.getCount())
                .chargingMode(addBlankCardVO.getChargingMode())
                .phoneNumber(addBlankCardVO.getPhoneNumber())
                .orderStatus(BlankCardOrderStatus.ORDERED.getStatus())
                .build();
        channelBlankcardOrderMapper.insert(order);
        blankCardOrderSerivceStatusMachineDecorator.after(order.getOrderId().toString(),
                blankCardOrderSerivceStatusMachineDecorator.getOneStatMachine(order.getOrderId().toString(), false));
    }

    @Override
    public PageResult<BlandCardOrdersDTO> getBlankCardOrders(getBlankCardOrdersVO getBlankCardOrdersVO) {
        Page<BlandCardOrdersDTO> page = channelBlankcardOrderMapper.selectOrderPage(new Page<CmsChannelBlankcardOrder>(getBlankCardOrdersVO.getPageNum(), getBlankCardOrdersVO.getPageSize()),
                Wrappers.lambdaQuery(CmsChannelBlankcardOrder.class)
                        .eq(CmsChannelBlankcardOrder::getCooperationMode, getBlankCardOrdersVO.getCooperationMode())
                        .eq(CmsChannelBlankcardOrder::getOrderUserId, getBlankCardOrdersVO.getCorpId())
                        .orderByDesc(CmsChannelBlankcardOrder::getUpdateTime));
        return PageResult.of(page);
    }

    @Override
    public void uploadPaymentProof(UploadPaymentProofVO uploadPaymentProofVO) {

        if (!obligation.equals(uploadPaymentProofVO.getOrderStatus())) {
            throw new BizException("Order status is not pending payment, upload payslip is not allowed");
        }

        MultipartFile file = uploadPaymentProofVO.getPaymentProofs();

        if (file != null && !file.isEmpty()) {
            //判断文件后缀是否满足要求
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase();

            if (!paymentEmailProps.getSuffix().contains(extension)) {
                throw new BizException("Payslip document type incorrect，Current file support format：" + paymentEmailProps.getSuffix());
            }

            File dir = new File(nfspath);
            if (!dir.exists()) {
                if (!dir.mkdirs()) {
                    throw new BizException("Payslip upload failed");
                }
            }
            String coverPath = dir + File.separator + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmmssSSS")) + file.getOriginalFilename();
            File f = new File(coverPath);

            //保存付款证明
            try {
                file.transferTo(f);
            } catch (Exception e) {
                log.warn("保存付款证明失败原因:{}", e);
                throw new BizException("Payslip upload failed");
            }

            //更改订单状态并保存付款证明
            channelBlankcardOrderMapper.update(null, Wrappers.lambdaUpdate(CmsChannelBlankcardOrder.class)
                    .set(CmsChannelBlankcardOrder::getOrderStatus, paid)
                    .set(CmsChannelBlankcardOrder::getPaymentProofsPath, coverPath)
                    .set(CmsChannelBlankcardOrder::getPaymentProofsTime, new Date())
                    .eq(CmsChannelBlankcardOrder::getOrderId, uploadPaymentProofVO.getOrderId()));

            CmsChannelBlankcardOrder order = channelBlankcardOrderMapper.getOrderInfo(uploadPaymentProofVO.getOrderId());

            //发送付款证明和发票邮件
            File[] files = new File[2];
            files[0] = new File(order.getPaymentProofsPath());
            files[1] = new File(order.getInvoicePath());

            String mailTitle = blankCardOrderServiceImpl.buildMailContext(paymentEmailProps.getTitle(),
                    DateTimeUtil.formatTime(order.getCreateTime()),
                    order.getCardForm(),
                    order.getCount().toString(),
                    order.getChargingMode(),
                    order.getCorpName());

            String paramReplacedMailContext = blankCardOrderServiceImpl.buildMailContext(paymentEmailProps.getCotent(),
                    DateTimeUtil.formatTime(order.getCreateTime()),
                    order.getCardForm(),
                    order.getCount().toString(),
                    order.getChargingMode(),
                    order.getCorpName());

            MailSendParam mailSendParamFile = MailSendParam.builder()
                    .content(paramReplacedMailContext)
                    .title(mailTitle)
                    .recipientEmailAddress(paymentEmailProps.getEmailAddressList())
                    .files(files).build();

            backFeignClient.sendMailWithFile(mailSendParamFile);

            //发送状态变化邮件
            final ChannelDistributorDetail channelDistributorDetail = Optional.ofNullable(channelDistributorDetailMapper.selectOne(Wrappers.lambdaQuery(ChannelDistributorDetail.class)
                            .eq(ChannelDistributorDetail::getCorpId, uploadPaymentProofVO.getCorpId())))
                    .orElseThrow(() -> new BizException("Cannot find this channel partner"));

            blankCardOrderServiceImpl.sendOrderStatusChangedMail(BlankCardOrderStatusEnum.PAID, channelDistributorDetail.getEmail(),
                    DateTimeUtil.formatTime(order.getCreateTime()), order.getCardForm(), order.getCount().toString(),
                    null, order.getChargingMode(), order.getCorpName());

        } else {
            throw new BizException("Payslip is mandatory");
        }

    }

    @Override
    public Response blankCardExport(String corpId, String orderBatch) {

        return pmsFeignClient.exprotBlankCard(orderBatch, corpId);

    }

    @Override
    public Map<String, String> blankCardIllustrate() {
        Map<String, String> map = new HashMap<>();
        map.put("illustrate", blankCardIllustrate);
        map.put("phoneIllustrate", phoneIllustrate);
        return map;
    }

    @Override
    public List<String> getBlankCardExportIccid(String corpId, String orderBatch) {
        return channelCardMapper.getBlankCards(corpId, orderBatch);
    }

    @Override
    public List<DirectionalAppSurfDetailDTO> getDirectionalAppSurfDetail(String packageId, String packageUniqueId, String imsi) {
        List<DirectionalFlow4Upcc> directionalAppInfos = pmsFeignClient.getDirectionalFlowInfo4channelSelf(packageId).get();
        List<HistoryQuota> historyQuotas = statFeignClient.getDirectionalAppUsedFlow(packageUniqueId).get();
        final Map<String, String> usedFlows = historyQuotas.stream().collect(Collectors
                .toMap(HistoryQuota::getRg, HistoryQuota::getQtaconsumption));

        final Map<String, BigDecimal> groupFlow = new HashMap<>();
        List<DirectionalAppSurfDetailDTO> res = new ArrayList<>();
        directionalAppInfos.forEach(e -> {

            String usedFlow = judgeFlowStr(new BigDecimal(usedFlows.getOrDefault(e.getRg(), "0")));
            BigDecimal usedFlowNum = judgeFlow(new BigDecimal(usedFlows.getOrDefault(e.getRg(), "0")));

            BigDecimal consumption = new BigDecimal(e.getConsumption())
                    .divide(BYTE_TO_MB, 2, RoundingMode.UP);

            res.add(DirectionalAppSurfDetailDTO
                    .builder()
                    .appName(e.getAppName())
                    .highSpeedFlow(new BigDecimal(e.getHighSpeedFlow()).divide(BYTE_TO_MB, 2, RoundingMode.UP))
                    .totleFlow(
                            e.getDirectType().equals("1") ?
                                    "/" : consumption + " MB" + dealFlow(consumption)
                    )
                    .usedFlow(usedFlow.contains("MB")? usedFlow : usedFlow + " MB" + dealFlow(new BigDecimal(usedFlow)))
                    .type(e.getDirectType())
                    .groupId(e.getAppGroupId())
                    .build());
            if (groupFlow.get(e.getAppGroupId()) == null) {
                groupFlow.put(e.getAppGroupId(), usedFlowNum);
            } else {
                groupFlow.put(e.getAppGroupId(), usedFlowNum.add(groupFlow.get(e.getAppGroupId())));
            }
        });
        res.forEach(e -> {
            if (e.getHighSpeedFlow().compareTo(groupFlow.get(e.getGroupId())) < 0) {
                e.setSurfStatus("2");
            } else {
                e.setSurfStatus("1");
            }

            if (e.getTotleFlow().equals("/")) {
                e.setSurfStatus("2");
            }

        });

        return res;
    }

    private String judgeFlowStr(BigDecimal flow) {
        if (lowFlow.compareTo(flow) > 0 && BigDecimal.ZERO.compareTo(flow) < 0){
            return new BigDecimal("0.01").setScale(2,  RoundingMode.UP) + "MB" +  dealByteFlow(flow);
        }else {
            return String.valueOf(flow.divide(new BigDecimal(1024 * 1024), 2, RoundingMode.UP)) ;
        }

    }
    private BigDecimal judgeFlow(BigDecimal flow) {
        if (lowFlow.compareTo(flow) > 0 && BigDecimal.ZERO.compareTo(flow) < 0){
            return new BigDecimal("0.01").setScale(2,  RoundingMode.UP);
        }else {
            return flow.divide(new BigDecimal(1024 * 1024), 2, RoundingMode.UP);
        }

    }

    private String dealByteFlow(BigDecimal flow) {
        log.debug("开始进行byte流量转换");

        String flowStr = flow + " b ";
        if (flow.compareTo(new BigDecimal(1024)) >= 0) {
            flow = flow.divide(new BigDecimal(1024), 2, RoundingMode.UP);
            flowStr = flow +  " kb ";
        }

        log.debug("byte流量转换,{}",flowStr);

        return "(" + flowStr + ")";

    }

    private String dealFlow(BigDecimal flow) {

        String flowStr = flow + " MB ";

        if (flow != null) {
            Boolean isOut = false;
            for (int i = 0; i < 2; i++) {
                if (flow.compareTo(new BigDecimal(1024)) >= 0) {
                    flow = flow.divide(new BigDecimal(1024), 2, RoundingMode.HALF_UP);
                    flowStr = flow + (i == 0 ? " GB " : " TB ");
                    isOut = true;
                }
            }

            if (isOut) {
                return "(" + flowStr + ")";
            } else {
                return "";
            }
        }

        return BigDecimal.ZERO + "MB ";
    }

    @Override
    @Async
    public void updatedCardHasPackage() {
        log.debug("开始更新卡是否有可用套餐");
        long start = System.currentTimeMillis(); // 开始时间

        //查询开始条数
        Integer begin = 0;

        while (true) {
            List<Long> updateIds = channelBatchTaskMapper.getUpdateIds(updateCardSize, begin);
            if (CollectionUtils.isEmpty(updateIds)) {
                break;
            }
            channelCardMapper.update(null, Wrappers.lambdaUpdate(ChannelCard.class)
                    .set(ChannelCard::getHaveUsePackage, null)
                    .in(ChannelCard::getId, updateIds));
            if (updateCardSize > updateIds.size()) {
                break;
            }
            begin += updateCardSize;
        }

        begin = 0;

        while (true) {
            List<Long> hasPackageIds = channelBatchTaskMapper.getIsHasPackageCard(updateCardSize, begin);
            if (CollectionUtils.isEmpty(hasPackageIds)) {
                break;
            }
            channelCardMapper.update(null, Wrappers.lambdaUpdate(ChannelCard.class)
                    .set(ChannelCard::getHaveUsePackage, "1")
                    .in(ChannelCard::getId, hasPackageIds));
            if (updateCardSize > hasPackageIds.size()) {
                break;
            }
            begin += updateCardSize;
        }

        long end = System.currentTimeMillis(); // 结束时间
        long duration = end - start; // 计算耗时（单位为毫秒）
        log.debug("更新卡是否有可用套餐流程结束，耗时{}", duration);

    }

    @Override
    public IPage<GetChannelFlowDetailVO> getChannelFlowDetail(GetChannelFlowDetailDTO dto) {

        Channel channel = channelMapper.selectOne(Wrappers.lambdaQuery(Channel.class)
                .select(Channel::getEbsCode)
                .eq(Channel::getCorpId, dto.getCorpId()));

        if (ObjectUtil.isNotNull(channel) && StrUtil.isNotBlank(channel.getEbsCode())) {
            IPage<GetChannelFlowDetailVO> page = new Page<>(dto.getNum(), dto.getSize());

            IPage<GetChannelFlowDetailVO> record = cmsCorpFlowdetailMapper.getChannelFlowDetail(page, channel.getEbsCode());

            return record;
        }

        return null;
    }


    @Override
    public List<Long> getChannelImsiCost(String corpId, String cooperationMode) {

        List<CmsChannelImsiAmountRelation> imsiAmountRelation = cmsChannelImsiAmountRelationMapper.selectList(Wrappers.lambdaQuery(CmsChannelImsiAmountRelation.class)
                .select(CmsChannelImsiAmountRelation::getRuleId)
                .eq(CmsChannelImsiAmountRelation::getCorpId, corpId)
                .eq(CmsChannelImsiAmountRelation::getCooperationMode, cooperationMode));

        if (!CollectionUtils.isEmpty(imsiAmountRelation)) {
            return imsiAmountRelation.stream().map(CmsChannelImsiAmountRelation::getRuleId).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    @Override
    public ExportVO exportImsiCost(ExportImsiCostDTO vo) {
        Long taskId = null;

        ChannelCloseAccounts channelCloseAccounts = Response.getAndCheckRemoteData(statFeignClient.getCloseAccounts(vo.getId()));

        //判断是否封板
        if (ObjectUtil.isNotNull(channelCloseAccounts) && StrUtil.isNotBlank(channelCloseAccounts.getImsiDetailPath())) {
            List<ExportVO> exportVOS = getExportVOS(vo.getUserId(), channelCloseAccounts.getImsiDetailPath(), vo.getId());
            return exportVOS.get(0);
        }

        String model;

        switch (vo.getCooperationMode()) {
            case "0":
                model = "Distribution_";
                break;
            case "1":
                model = "Distribution_";
                break;
            case "2":
                model = "A~Z_";
                break;
            case "3":
                model = "A~Z_";
                break;
            case "4":
                model = "Combine_";
                break;
            case "5":
                model = "A~Z_";
                break;
            case "6":
                model = "Combine_";
                break;
            case "7":
                model = "Combine_";
                break;
            default:
                model = "";
        }

        ChannelIncomeInfoMonth e = statFeignClient.getById(String.valueOf(vo.getId())).get();

        Channel channel = channelMapper.selectOne(Wrappers.lambdaQuery(Channel.class)
                .eq(Channel::getCorpId, vo.getCorpId()));
        DateFormat df = new SimpleDateFormat("yyyyMMdd");
        Calendar calendar = Calendar.getInstance();
        String dateName = df.format(calendar.getTime());
        String taskName = "IMSI Report_" + channel.getCorpName() + "_" + model + (e == null ? dateName : e.getSvcEndTime().substring(0, 6));
        String fileName = taskName + ".xlsx";
        File dir = new File(filePath + File.separator + vo.getId());
        if (!dir.exists()) {
            dir.mkdirs();
        }

        try {
            String taskFilePath = filePath + File.separator + vo.getId() + File.separator + fileName;
            taskId = Response.getAndCheckRemoteData(backFeignClient.addTask(BatchSyncfileTack.builder()
                    .taskStatus(BatchSyncfileTack.TaskStatus.PROCESSING.getStatus())
                    .taskDesc(taskName)
                    .filePath(taskFilePath)
                    .corpId(vo.getUserId())
                    .fileName(fileName)
                    .build()));

            exportTask.exportImsiCostTask(channel, vo, taskFilePath, taskId, vo.getId(), channelCloseAccounts);
        } catch (Exception ex) {
            if (taskId != null) {
                backFeignClient.updateTaskStatus(taskId, BatchSyncfileTack.TaskStatus.FAIL.getStatus(), null);
            }
            log.error("", ex);
        }
        return ExportVO.builder().taskId(taskId).taskName(fileName).build();
    }

    @Override
    public IPage<DepositRecordVO> depositRecord(GetDepositVo getDepositVo) {
        List<String> corpIds = Optional.ofNullable(getDepositVo.getCorpIds()).orElse(new ArrayList<>());

        if (StringUtils.hasLength(getDepositVo.getCorpId())) {
            corpIds.add(getDepositVo.getCorpId().trim());
        }
        corpIds = corpIds.stream().distinct().filter(StringUtils::hasLength).collect(Collectors.toList());

        String cooperationMode = getDepositVo.getCooperationMode();
        int pageNumber = getDepositVo.getPageNum();
        int pageSize = getDepositVo.getPageSize();
        try {
            Page<DepositRecordVO> pageInfo = new Page<>(pageNumber, pageSize);
            Page<DepositRecordVO> pageDepositRecords = cmsDepositChargeRecordMapper.getDepositRecords(pageInfo, corpIds, cooperationMode, getDepositVo.getCorpName(),
                    getDepositVo.getChargeId(), getDepositVo.getProcUniqueId(), getDepositVo.getIsDisplay());
            List<DepositRecordVO> records = pageDepositRecords.getRecords();
            if (!records.isEmpty()) {
                for (DepositRecordVO depositRecordVO : records) {
                    depositRecordVO.setChargeAmount(depositRecordVO.getChargeAmount().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
                    if ("6".equals(depositRecordVO.getChargeStatus()) || "7".equals(depositRecordVO.getChargeStatus()) || "5".equals(depositRecordVO.getChargeStatus())) {
                        CmsChannelChargeDetail cmsChannelChargeDetail = cmsChannelChargeDetailMapper.selectOne(Wrappers.lambdaQuery(CmsChannelChargeDetail.class)
                                .eq(CmsChannelChargeDetail::getAccountId, depositRecordVO.getId())
                                .orderByDesc(CmsChannelChargeDetail::getChargeTime)
                                .orderByDesc(CmsChannelChargeDetail::getId));
                        if (cmsChannelChargeDetail != null) {
                            depositRecordVO.setNoPassReason(cmsChannelChargeDetail.getNoPassReason());
                            depositRecordVO.setPaymentProofAddress(cmsChannelChargeDetail.getPaymentProofAddress());
                        }
                    }
                }
            }
            return pageDepositRecords;
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            throw new BizException("Failed to query master card location update record", e);
        }
    }

    @Override
    public DepositRecordVO depositInfo(String corpId, String cooperationMode) {
        DepositRecordVO depositRecordVO = new DepositRecordVO();
        depositRecordVO.setChannelType(getChanelType(corpId, cooperationMode));
        Channel channel = channelMapper.selectById(corpId);
        depositRecordVO.setCurrencyCode(channel.getCurrencyCode());
        return depositRecordVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void applyInvoice(ApplyInvoiceVo applyInvoiceVo) {
        String corpId = applyInvoiceVo.getCorpId();
        String billId;
        String procUniqueId = UUID.randomUUID().toString().replace("-", "");
        if (applyInvoiceVo.isRepeatedApply()) {
            CmsDepositChargeRecord cmsDepositChargeRecord1 = cmsDepositChargeRecordMapper.selectOne(Wrappers.lambdaQuery(CmsDepositChargeRecord.class)
                    .eq(CmsDepositChargeRecord::getId, applyInvoiceVo.getId()));
            if (!"3".equals(cmsDepositChargeRecord1.getChargeStatus()) || "2".equals(cmsDepositChargeRecord1.getIsDisplay())) {
                throw new BizException("the status is not refused, do not allow to apply repeatedly，please refresh page");
            }
            billId = cmsDepositChargeRecord1.getBillId();
            cmsDepositChargeRecord1.setIsDisplay("2");
            cmsDepositChargeRecordMapper.updateById(cmsDepositChargeRecord1);
        } else {
            billId = "BILL" + DateTimeUtil.getNowDay() + (int) ((Math.random() * 9 + 1) * 100000);
        }

        QueryWrapper<ChannelDistributorDetail> distributorDetailQueryWrapper = new QueryWrapper<>();
        distributorDetailQueryWrapper.eq("corp_id", corpId);
        ChannelDistributorDetail channelDistributorDetail = channelDistributorDetailMapper.selectOne(distributorDetailQueryWrapper);
        String cooperationMode = applyInvoiceVo.getCooperationMode();
        String channelType = "";
        if ("1".equals(cooperationMode)) {
            channelType = channelDistributorDetail.getChannelType();
        } else if ("2".equals(cooperationMode)) {
            channelType = channelDistributorDetail.getA2zChannelType();
        } else {
            throw new BizException("data exception");
        }
        CmsDepositChargeRecord cmsDepositChargeRecord = CmsDepositChargeRecord.builder()
                .billId(billId)
                .chargeTime(new Date())
                .corpId(corpId)
                .cooperationMode(cooperationMode)
                .chargeStatus("1")
                .channelType(channelType)
                .chargeAmount(applyInvoiceVo.getAmount().multiply(new BigDecimal(100)))
                .proUniqueId(procUniqueId)
                .build();
        Channel channel = channelMapper.selectById(cmsDepositChargeRecord.getCorpId().trim());
        if ("2".equals(channelType)) {
            //预存模式直接生成发票并进入可缴费状态
            // 生成发票
            String invoiceNo = "IN-" + DateTimeUtil.getNowDay() + (int) ((Math.random() * 9 + 1) * 1000) + "-GDS";
            DepositInvoiceTempParam depositInvoiceTempParam = DepositInvoiceTempParam.builder()
                    .companyName(channel.getCompanyName())
                    .companyAddress(channel.getAddress())
                    .invoiceNo(invoiceNo)
                    .invoiceDate(new SimpleDateFormat("yyyy-MM-dd").format(new Date()))
                    .paymentInstruction(paymentInstruction)
                    .build();
            switch (channel.getCurrencyCode()) {
                case "156":
                    depositInvoiceTempParam.setCurrency("CNY");
                    break;
                case "344":
                    depositInvoiceTempParam.setCurrency("HKD");
                    break;
                case "840":
                    depositInvoiceTempParam.setCurrency("USD");
                    break;
                default:
                    depositInvoiceTempParam.setCurrency("");
            }
            depositInvoiceTempParam.setAmount(depositInvoiceTempParam.getCurrency() + new DecimalFormat("#,##0.00").format(applyInvoiceVo.getAmount()));
            depositInvoiceTempParam.setTotalAmount(depositInvoiceTempParam.getCurrency() + new DecimalFormat("#,##0.00").format(applyInvoiceVo.getAmount()));
            String invoiceName = "Invoice_prepay_" + new String(channel.getCorpName().getBytes(StandardCharsets.UTF_8)) + "_" + DateTimeUtil.getNowTime() + ".pdf";
            String pdfTemplatePath = "template/preInvoice_20241105.pdf";
            depositInvoiceTempParam.setProductName("Prepayment for roaming data purchase");
            String invoicePath = nfspath.concat(File.separator);
            PdfUtils.generatorPdfByPdfTemplate(pdfTemplatePath, invoicePath, invoiceName, depositInvoiceTempParam);

            //状态为可缴费
            cmsDepositChargeRecord.setChargeStatus("4");
            cmsDepositChargeRecord.setInvoiceNo(invoiceNo);
            cmsDepositChargeRecord.setInvoiceAddress(invoicePath + invoiceName);
            cmsDepositChargeRecordMapper.insert(cmsDepositChargeRecord);
        } else {
            //押金模式 未缴费状态
            cmsDepositChargeRecord.setChargeStatus("1");
            cmsDepositChargeRecordMapper.insert(cmsDepositChargeRecord);
            //推送代办给销售
            SubmitProcessReq submitProcessReq = new SubmitProcessReq();
            HashMap<String, Object> titleMap = new HashMap<>();
            titleMap.put("corpName", channel.getCorpName());
            titleMap.put("date", new SimpleDateFormat("yyyyMMdd").format(new Date()));
            submitProcessReq.setTitleParamMap(titleMap);
            submitProcessReq.setTitle(new SimpleDateFormat("yyyyMMdd").format(new Date()) + "申请发票");
            submitProcessReq.setProcId("14");
            submitProcessReq.setType("1");
            submitProcessReq.setProcUniqueId(procUniqueId);
            submitProcessReq.setTodoNodeId("1");


            String salesMail = channelDistributorDetail.getSalesMail();
            List<User> users = backFeignClient.getUsernameByMail(salesMail).get();
            Assert.notEmpty(users, "找不到销售邮箱对应的账号");
            User user1 = users.stream().filter(user -> salesMail.equals(user.getEmail())).findFirst().orElseThrow(() -> new BizException("系统错误"));
            submitProcessReq.setGdsAccountList(Collections.singletonList(
                    user1.getUsername()));

            HashMap<String, Object> map = new HashMap<>();
            map.put("chargeId", cmsDepositChargeRecord.getId());
            map.put("corpId", corpId);
            map.put("cooperationMode", cooperationMode);
            map.put("page", "1");
            submitProcessReq.setParamMap(map);
            backFeignClient.processSubmission(submitProcessReq).get();

        }
    }
    @Override
    public void cancelInvoice(String chargeId) {
        boolean lock = redissonLock.tryLock(chargeId);
        if (!lock) {
            throw new BizException("invoice is in progress, please try again later");
        }
        try {
            QueryWrapper<CmsDepositChargeRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("bill_id", chargeId);
            queryWrapper.eq("is_display", "1");
            CmsDepositChargeRecord cmsDepositChargeRecord = cmsDepositChargeRecordMapper.selectOne(queryWrapper);
            if (!"1".equals(cmsDepositChargeRecord.getChargeStatus())) {
                throw new BizException("invoice is not in unpaid status and cannot be cancelled");
            }

            //修改状态为已取消
            cmsDepositChargeRecord.setChargeStatus("8");
            cmsDepositChargeRecordMapper.updateById(cmsDepositChargeRecord);
            // 删除代办
            SubmitProcessReq submitProcessReq = new SubmitProcessReq();
            submitProcessReq.setProcUniqueId(cmsDepositChargeRecord.getProUniqueId());
            submitProcessReq.setType("3");
            submitProcessReq.setProcId("14");
            submitProcessReq.setTodoNodeId("1");
            backFeignClient.processSubmission(submitProcessReq);
        } finally {
            try {
                if (redissonLock.isHeldByCurrentThread(chargeId)) {
                    redissonLock.unlock(chargeId);
                }
            } catch (Exception e) {
                log.warn("redis解锁失败");
            }
        }
    }

    @Override
    public void salesAppro(InvoiceApproVo invoiceApproVo) {
        String chargeId = invoiceApproVo.getChargeId();
        boolean lock = redissonLock.tryLock(chargeId);
        if (!lock) {
            throw new BizException("invoice is in progress, please try again later");
        }
        try {
            QueryWrapper<CmsDepositChargeRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("id", chargeId);
            CmsDepositChargeRecord cmsDepositChargeRecord = cmsDepositChargeRecordMapper.selectOne(queryWrapper);
            if (!"1".equals(cmsDepositChargeRecord.getChargeStatus())) {
                throw new BizException("invoice is not in unpaid status");
            }

            if (invoiceApproVo.isStatus()) {
                String procUniqueId = UUID.randomUUID().toString().replace("-", "");
                //修改状态为发票审批中
                cmsDepositChargeRecord.setChargeStatus("2");
                cmsDepositChargeRecord.setProUniqueId(procUniqueId);
                cmsDepositChargeRecordMapper.updateById(cmsDepositChargeRecord);
                //记录流转意见记录
                setComments(invoiceApproVo,procUniqueId,"发票审批申请");
                // 推送代办给财务
                Channel channel = channelMapper.selectOne(Wrappers.lambdaQuery(Channel.class)
                        .eq(Channel::getCorpId, cmsDepositChargeRecord.getCorpId()));
                SubmitProcessReq submitProcessReq = new SubmitProcessReq();
                HashMap<String, Object> titleMap = new HashMap<>();
                titleMap.put("corpName", channel.getCorpName());
                titleMap.put("date", new SimpleDateFormat("yyyyMMdd").format(new Date()));
                submitProcessReq.setTitleParamMap(titleMap);
                submitProcessReq.setTitle(new SimpleDateFormat("yyyyMMdd").format(new Date()) + "审批发票");
                submitProcessReq.setProcId("15");
                submitProcessReq.setType("1");
                submitProcessReq.setProcUniqueId(procUniqueId);
                submitProcessReq.setTodoNodeId("1");
                HashMap<String, Object> map = new HashMap<>();
                map.put("chargeId", chargeId);
                map.put("corpId", cmsDepositChargeRecord.getCorpId().trim());
                map.put("cooperationMode", cmsDepositChargeRecord.getCooperationMode());
                map.put("page", "2");
                submitProcessReq.setParamMap(map);
                backFeignClient.processSubmission(submitProcessReq).get();
            } else {
                //修改状态为发票审批拒绝
                cmsDepositChargeRecord.setChargeStatus("3");
                cmsDepositChargeRecord.setNoPassReason(invoiceApproVo.getDispositionComments());
                cmsDepositChargeRecordMapper.updateById(cmsDepositChargeRecord);
                //记录流转意见记录
                setComments(invoiceApproVo, invoiceApproVo.getProcUniqueId(),"发票审批拒绝");
            }
            //todo 推送办结给销售
            SubmitProcessReq submitProcessReq = new SubmitProcessReq();
            submitProcessReq.setType("2");
            submitProcessReq.setProcUniqueId(invoiceApproVo.getProcUniqueId());
            submitProcessReq.setTodoUniqueId(invoiceApproVo.getTodoUniqueId());
            submitProcessReq.setProcId("14");
            submitProcessReq.setTodoNodeId("1");
            backFeignClient.processSubmission(submitProcessReq);
        } finally {
            try {
                if (redissonLock.isHeldByCurrentThread(chargeId)) {
                    redissonLock.unlock(chargeId);
                }
            } catch (Exception e) {
                log.warn("redis解锁失败");
            }
        }
    }

    private void setComments(InvoiceApproVo invoiceApproVo, String procUniqueId,String operationType) {
        cmsInvoiceAuditRecordMapper.insert(CmsInvoiceAuditRecord.builder()
                .operationType(operationType)
                .dispositionComments(invoiceApproVo.getDispositionComments())
                .procUniqueId(procUniqueId)
                .operateTime(now())
                .operateUser(invoiceApproVo.getOperator())
                .build());
    }

    @Override
    public void finanAppro(InvoiceApproVo invoiceApproVo) {
        String chargeId = invoiceApproVo.getChargeId();
        boolean lock = redissonLock.tryLock(chargeId);
        if (!lock) {
            throw new BizException("invoice is in progress, please try again later");
        }
        try {
            QueryWrapper<CmsDepositChargeRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("id", chargeId);
            CmsDepositChargeRecord cmsDepositChargeRecord = cmsDepositChargeRecordMapper.selectOne(queryWrapper);
            if (!"2".equals(cmsDepositChargeRecord.getChargeStatus())) {
                throw new BizException("invoice is not approved");
            }

            if (invoiceApproVo.isStatus()) {
                // 生成发票
                Channel channel = channelMapper.selectById(cmsDepositChargeRecord.getCorpId().trim());
                String invoiceNo = "IN-" + DateTimeUtil.getNowDay() + (int) ((Math.random() * 9 + 1) * 1000) + "-GDS";
                DepositInvoiceTempParam depositInvoiceTempParam = DepositInvoiceTempParam.builder()
                        .companyName(channel.getCompanyName())
                        .companyAddress(channel.getAddress())
                        .invoiceNo(invoiceNo)
                        .invoiceDate(new SimpleDateFormat("yyyy-MM-dd").format(new Date()))
                        .paymentInstruction(paymentInstruction)
                        .build();
                switch (channel.getCurrencyCode()) {
                    case "156":
                        depositInvoiceTempParam.setCurrency("CNY");
                        break;
                    case "344":
                        depositInvoiceTempParam.setCurrency("HKD");
                        break;
                    case "840":
                        depositInvoiceTempParam.setCurrency("USD");
                        break;
                    default:
                        depositInvoiceTempParam.setCurrency("");
                }
                depositInvoiceTempParam.setAmount(depositInvoiceTempParam.getCurrency() + new DecimalFormat("#,##0.00").format(cmsDepositChargeRecord.getChargeAmount().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)));
                depositInvoiceTempParam.setTotalAmount(depositInvoiceTempParam.getCurrency() + new DecimalFormat("#,##0.00").format(cmsDepositChargeRecord.getChargeAmount().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)));
                String invoiceName = "Invoice_deposit_" + new String(channel.getCorpName().getBytes(StandardCharsets.UTF_8)) + "_" + DateTimeUtil.getNowTime() + ".pdf";
                String pdfTemplatePath = "template/DepositInvoice_20241105.pdf";
                depositInvoiceTempParam.setProductName("Security Deposit");
                String invoicePath = nfspath.concat(File.separator);
                PdfUtils.generatorPdfByPdfTemplate(pdfTemplatePath, invoicePath, invoiceName, depositInvoiceTempParam);

                //修改状态为可缴费
                cmsDepositChargeRecord.setChargeStatus("4");
                cmsDepositChargeRecord.setInvoiceNo(invoiceNo);
                cmsDepositChargeRecord.setInvoiceAddress(invoicePath + invoiceName);
                cmsDepositChargeRecordMapper.updateById(cmsDepositChargeRecord);
                setComments(invoiceApproVo, invoiceApproVo.getProcUniqueId(),"发票审批通过");
            } else {
                //修改状态为发票审批拒绝
                cmsDepositChargeRecord.setChargeStatus("3");
                cmsDepositChargeRecord.setNoPassReason(invoiceApproVo.getDispositionComments());
                cmsDepositChargeRecordMapper.updateById(cmsDepositChargeRecord);
                setComments(invoiceApproVo, invoiceApproVo.getProcUniqueId(),"发票审批拒绝");
            }

            //todo 推送办结给财务
            SubmitProcessReq submitProcessReq = new SubmitProcessReq();
            submitProcessReq.setType("2");
            submitProcessReq.setProcUniqueId(invoiceApproVo.getProcUniqueId());
            submitProcessReq.setTodoUniqueId(invoiceApproVo.getTodoUniqueId());
            submitProcessReq.setProcId("15");
            submitProcessReq.setTodoNodeId("1");
            backFeignClient.processSubmission(submitProcessReq);
        } finally {
            try {
                if (redissonLock.isHeldByCurrentThread(chargeId)) {
                    redissonLock.unlock(chargeId);
                }
            } catch (Exception e) {
                log.warn("redis解锁失败");
            }
        }
    }


    @Override
    public List<CmsInvoiceAuditRecord> getInvoiceAuditRecord(String procUniqueId) {
        return cmsInvoiceAuditRecordMapper.selectList(
                new LambdaQueryWrapper<CmsInvoiceAuditRecord>()
                        .eq(CmsInvoiceAuditRecord::getProcUniqueId, procUniqueId));
    }

    @Override
    public DepositRecordVO depositRecordGetById(String id) {
        try {
            CmsDepositChargeRecord pageDepositRecords = cmsDepositChargeRecordMapper.selectById(id);
            DepositRecordVO depositRecordVO = new DepositRecordVO();
            // 复制相同的属性
            depositRecordVO.setId(pageDepositRecords.getId());
            depositRecordVO.setBillId(pageDepositRecords.getBillId());
            depositRecordVO.setChargeTime(pageDepositRecords.getChargeTime());
            depositRecordVO.setCorpId(pageDepositRecords.getCorpId());
            depositRecordVO.setCooperationMode(pageDepositRecords.getCooperationMode());
            depositRecordVO.setChargeStatus(pageDepositRecords.getChargeStatus());
            depositRecordVO.setInvoiceAddress(pageDepositRecords.getInvoiceAddress());
            depositRecordVO.setInvoiceNo(pageDepositRecords.getInvoiceNo());
            depositRecordVO.setChargeAmount(pageDepositRecords.getChargeAmount());
            depositRecordVO.setUpdateTime(pageDepositRecords.getUpdateTime());
            depositRecordVO.setChannelType(pageDepositRecords.getChannelType()); // 假设 CmsDepositChargeRecord 也有这个属性
            depositRecordVO.setNoPassReason(pageDepositRecords.getNoPassReason());
            if (pageDepositRecords != null) {
                Channel channel = channelMapper.selectById(pageDepositRecords.getCorpId().trim());
                depositRecordVO.setCorpName(channel.getCorpName());
                depositRecordVO.setEbsCode(channel.getEbsCode());
                depositRecordVO.setCurrencyCode(channel.getCurrencyCode());
                if ("6".equals(depositRecordVO.getChargeStatus()) || "7".equals(depositRecordVO.getChargeStatus())) {
                    CmsChannelChargeDetail cmsChannelChargeDetail = cmsChannelChargeDetailMapper.selectOne(Wrappers.lambdaQuery(CmsChannelChargeDetail.class)
                            .eq(CmsChannelChargeDetail::getAccountId, depositRecordVO.getId())
                            .orderByDesc(CmsChannelChargeDetail::getChargeTime)
                            .orderByDesc(CmsChannelChargeDetail::getId));
                    if (cmsChannelChargeDetail != null) {
                        depositRecordVO.setNoPassReason(cmsChannelChargeDetail.getNoPassReason());
                        depositRecordVO.setPaymentProofAddress(cmsChannelChargeDetail.getPaymentProofAddress());
                    }
                }


            }
            return depositRecordVO;
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            throw new BizException("Failed to query master card location update record", e);
        }
    }

    @Override
    public void updateChargeStatus(long id, String status) {
        cmsDepositChargeRecordMapper.updateChargeStatus(id, status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void test1() {
        channelMarketingRebateMapper.insert(CmsChannelMarketingRebate.builder()
                .corpId("123")
                .type("2")
                .remainAmount(BigDecimal.ONE)
                .rebateAmount(BigDecimal.ONE)
                .activityId(222L)
                .expiryTime(new Date())
                .version(456L)
                .build());

        RebateTransVO rebateTransVO = RebateTransVO.builder().packagePrice(new BigDecimal(10)).build();



//        channelRebateUtil.getRebateInfo(rebateTransVO);



//        try {
//
//            throw new BizException("反正就是报错了");
//
//        }catch (Exception e){
//            if ("1".equals(rebateTransVO.getIsSuccess())){
//                rebateTransVO.getTransactionManager().rollback(rebateTransVO.getStatus());
//            }
//            throw e;
//        }

        if ("1".equals(rebateTransVO.getIsSuccess())){
            rebateTransVO.getTransactionManager().commit(rebateTransVO.getStatus());
        }
    }



    private List<ExportVO> getExportVOS(String userId, String filePath, Long id) {
        List<String> filePaths = Arrays.stream(filePath.split(","))
                .map(String::trim)
                .collect(Collectors.toList());
        List<ExportVO> exportVOS = getExportTaskMessage.getExportVOS(userId, filePaths);
        exportVOS.forEach(exportVO -> exportAsyncTask.exportFileAsync(exportVO, exportVO.getOldFilePath()));
        return exportVOS;
    }

    @Component
    @RequiredArgsConstructor
    private static class ExportTask {

        private final BackFeignClient backFeignClient;

        private final JmsFeignClient jmsFeignClient;

        private final StatFeignClient statFeignClient;

        public void exportImsiCostTask(Channel channel, ExportImsiCostDTO vo, String filePath, Long taskId, Long id, ChannelCloseAccounts channelCloseAccounts) {

            try (FileOutputStream os = new FileOutputStream(new File(filePath))) {

                List<JmsImsiAccountingDay> imsiAccountingDays = Response.getAndCheckRemoteData(jmsFeignClient.getImsiCostMsg(vo));

                // 构造导出数据

                List<ImsiCostExport> datas = new ArrayList<>();

                Map<String, Object> map;

                BigDecimal totalAmount = new BigDecimal(0);

                for (JmsImsiAccountingDay data : imsiAccountingDays) {

                    data.setAmount(data.getAmount().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));

                    String model = "";

                    String currency = "";

                    switch (data.getCooperationMode()) {
                        case "1":
                            model = "Distribution";
                            break;
                        case "2":
                            model = "A~Z";
                            break;
                        case "3":
                            model = "A~Z";
                            break;
                    }
                    switch (channel.getCurrencyCode()) {
                        case "344":
                            currency = "HKD";
                            break;
                        case "156":
                            currency = "CNY";
                            break;
                        case "840":
                            currency = "USD";
                            break;
                    }

                    datas.add(ImsiCostExport.builder()
                            .channelName(channel.getCorpName())
                            .imsi(data.getImsi())
                            .statTime(data.getDate().substring(0, data.getDate().length() - 2))
                            .amount(String.valueOf(data.getAmount()))
                            .currency(currency)
                            .model(model)
                            .build());


                    totalAmount = totalAmount.add(data.getAmount());
                }

                datas.add(ImsiCostExport.builder()
                        .channelName("Total")
                        .imsi("-")
                        .statTime("-")
                        .currency("-")
                        .model("-")
                        .amount(String.valueOf(totalAmount.setScale(2, RoundingMode.HALF_UP)))
                        .build());

                EasyExcel.write(filePath, ImsiCostExport.class)
                        .registerWriteHandler(new Custemhandler())
                        .registerWriteHandler(ExcelWriteHandler.getHandler())
                        .sheet("")
                        .doWrite(datas);

                if (ObjectUtil.isNotNull(channelCloseAccounts) && com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(channelCloseAccounts.getFlowSummaryPath())) {

                    statFeignClient.updateCloseAccounts(UpdateCloseAccountsDTO.builder()
                            .id(id)
                            .path(filePath)
                            .type(CloseAccountEnum.IMSI.getType())
                            .build());
                }

                backFeignClient.updateTaskStatus(taskId, BatchSyncfileTack.TaskStatus.FINISH.getStatus(), null);
            } catch (Exception e) {
                log.error("导出imsi费用记录失败：", e);
                backFeignClient.updateTaskStatus(taskId, BatchSyncfileTack.TaskStatus.FAIL.getStatus(), null);
            }
        }
    }

    private void signUpcc(SurfInfoCard surfInfoCard) {
        boolean surfUsedV = surfInfoCard.getCardType().equals(ChannelSurfInfo.InternetType.V.getType());
        CoreNetContext coreNetContext = (CoreNetContext) LocationUpdateContextFactory.getInstance(CoreNetContext.class);
        try {
            if (!surfUsedV) {
                //为了防止签约时发生异常，此时重置卡的upcc签约状态
                CoreNetContext.HcardUpdateMarking hcardUpdateMarking = coreNetContext.initHcard(surfInfoCard.getImsi());
                hcardUpdateMarking.setUpccSignStatus(UpccSignStatus.FAILED.getVal());
                coreNetCaller.upccSignature(surfInfoCard.getMsisdn(), surfInfoCard.getNewUpccSingBizId());
                hcardUpdateMarking.setUpccSignStatus(UpccSignStatus.SUCCESS.getVal());
                hcardUpdateMarking.setUpccSignBizId(surfInfoCard.getNewUpccSingBizId());
            } else {
                CoreNetContext.VcardUpdateMarking vcardUpdateMarking = coreNetContext.initVcard(surfInfoCard.getImsi());
                vcardUpdateMarking.setUpccSignStatus(UpccSignStatus.FAILED.getVal());
                coreNetCaller.upccSignature(surfInfoCard.getMsisdn(), surfInfoCard.getNewUpccSingBizId());
                vcardUpdateMarking.setUpccSignStatus(UpccSignStatus.SUCCESS.getVal());
                vcardUpdateMarking.setUpccSignBizId(surfInfoCard.getNewUpccSingBizId());
            }
        } catch (Exception e) {
            throw new BizException("与外部网元交互失败");
        } finally {
            Response.checkRemoteData(pmsFeignClient.updateCoreNetStatus(coreNetContext));
        }
    }

    private void throwRabbitMq(List<ChannelSurfInfo> surfInfoList, ChannelPackageCard channelPackageCard, Long
            expireTime) {
        String packageUniqueId = channelPackageCard.getPackageUniqueId();
        SingleDayDelayVO messegeVo = SingleDayDelayVO.builder()
                .packageUniqueId(packageUniqueId)
                .himsi(channelPackageCard.getImsi())
                .signUpccId(getFirstUpccBizId(packageUniqueId))
                .build();
        for (ChannelSurfInfo channelSurfInfo : surfInfoList) {
            if (ImsiType.HIMSI.getK().equals(channelSurfInfo.getInternetType())) {
                messegeVo.setIccid(channelPackageCard.getIccid());
                messegeVo.setImsi(channelPackageCard.getImsi());
                messegeVo.setMsisdn(channelPackageCard.getMsisdn());
            } else {
                PmsCardpoolVcardRelation vCardInfo = pmsFeignClient.getVcardInfo(channelSurfInfo.getImsi()).get();
                messegeVo.setImsi(vCardInfo.getImsi());
                messegeVo.setIccid(vCardInfo.getIccid());
                messegeVo.setMsisdn(vCardInfo.getMsisdn());
            }
            messegeVo.setCardType(channelSurfInfo.getInternetType());
            String singleDelayJson = JSONObject.toJSONString(messegeVo);
            log.debug("入rabibitmq单日恢复延时消息队列，延迟时间：{} ms",
                    expireTime);
            sendMessageWrapper.throwMessageToQueue(
                    singleDelayJson, QueueEnum.ResumeDelayQueue, expireTime);
        }
    }

    private String getFirstUpccBizId(String packageUniqueId) {
        CmsPackageCardUpccRelation cmsPackageCardUpccRelation = cmsPackageCardUpccRelationMapper.selectOne(Wrappers.lambdaQuery(CmsPackageCardUpccRelation.class)
                .eq(CmsPackageCardUpccRelation::getPackageUniqueId, packageUniqueId)
                .orderByAsc(CmsPackageCardUpccRelation::getConsumption));
        return cmsPackageCardUpccRelation.getUpccSignId();
    }

    private boolean checkChannelPermission(String upccTemplateId, String cropId) {
        List<ChannelUpcctemplateRelation> channelUpcctemplateRelations = channelUpcctemplateRelationMapper.selectList(Wrappers.lambdaQuery(ChannelUpcctemplateRelation.class)
                .eq(ChannelUpcctemplateRelation::getCorpId, cropId));

        return channelUpcctemplateRelations.stream().map(ChannelUpcctemplateRelation::getTemplateId).collect(Collectors.toList())
                .contains(upccTemplateId);
    }

    private boolean saveOrUpdateRedis(ChannelPackageCard activatedPackage) {

        boolean dayLimited = ChannelPackageCard.FlowLimitTypeEnum.DAY_LIMIT.getValue().equals(activatedPackage.getFlowLimitType());

        return dayLimited &&
                Boolean.FALSE.equals(redisTemplate.hasKey(String.format(BizConstants.PACKAGE_RECOVER_KEY, activatedPackage.getPackageUniqueId())));
    }


    private SurfInfoCard getSurfInfoCard(String mcc, ChannelPackageCard activatedPackage) {
        List<ChannelSurfInfo> channelSurfInfos = channelSurfInfoMapper.selectList(Wrappers.lambdaQuery(ChannelSurfInfo.class)
                .eq(ChannelSurfInfo::getPackageUniqueId, activatedPackage.getPackageUniqueId())
                .orderByDesc(ChannelSurfInfo::getUpdateTime));

        if (CollectionUtils.isEmpty(channelSurfInfos)) {
            throw new BizException("数据异常，未找到iccid当前位置上网信息");
        }

        List<ChannelSurfInfo> surfInfos = channelSurfInfos.stream().filter(channelSurfInfo -> mcc.equals(channelSurfInfo.getMcc())).collect(Collectors.toList());

        for (ChannelSurfInfo channelSurfInfo : surfInfos) {
            if (channelSurfInfo.getInternetType().equals(ChannelSurfInfo.InternetType.V.getType())) {
                return new SurfInfoCard(channelSurfInfo.getImsi(), null, ChannelSurfInfo.InternetType.V.getType(), null, null, channelSurfInfos);
            }
        }
        //说明没V卡，没V卡直接返回H信息
        return new SurfInfoCard(activatedPackage.getImsi(), null, ChannelSurfInfo.InternetType.H.getType(), null, null, channelSurfInfos);
    }


    private ChannelPackageCard getActivatedPackage(String iccid, String packageId) {
        Date now = new Date();
        return Optional.ofNullable(channelPackageCardMapper.selectOne(Wrappers.<ChannelPackageCard>lambdaQuery()
                .eq(ChannelPackageCard::getIccid, iccid)
                .eq(ChannelPackageCard::getPackageType, "1")
                .eq(ChannelPackageCard::getPackageId, packageId)
                .and(wrapper -> wrapper.and(wrapper1 -> wrapper1.eq(ChannelPackageCard::getPackageStatus, PackageStatusEnum.ACTIVATING.getStatus())
                        .gt(ChannelPackageCard::getEffectiveDay, now)
                        .or(wrapper2 -> wrapper2.eq(ChannelPackageCard::getPackageStatus, PackageStatusEnum.ACTIVATED.getStatus())
                                .gt(ChannelPackageCard::getExpireTime, now)))))).orElseThrow(() -> new BizException(ApiResponseEnum.NOT_HAVE_PACKAGE));
    }

    public PackageFlowLimitEnums flowLimitType(String type) {
        if (PackageFlowLimitEnums.CYCLE.getUnit().equals(type)) {
            return PackageFlowLimitEnums.CYCLE;
        } else if (PackageFlowLimitEnums.DAY.getUnit().equals(type)) {
            return PackageFlowLimitEnums.DAY;
        } else {
            throw new BizException("流量限制类型错误");
        }

    }

    String unitConversion(BigDecimal source) {
        return Optional.ofNullable(source).orElse(new BigDecimal(0))
                .divide(new BigDecimal(1024 * 1024), 2, RoundingMode.HALF_UP)
                .stripTrailingZeros().toPlainString();
    }

    public static void main(String[] args) {
        System.out.println(DateUtil.parse("2023-03-11 11:24:05"));
    }

    @AllArgsConstructor
    private class StockTransferTask implements Runnable {
        String parentCorpId;

        String corpId;

        File iccidFile;

        ChannelPackageCardMapper channelPackageCardMapper;

        ChannelBindMapper channelBindMapper;

        ChannelCardMapper channelCardMapper;

        ChannelOrderMapper channelOrderMapper;

        ChannelOrderDetailMapper channelOrderDetailMapper;

        @Override
        public void run() {
            HashSet<String> updateList = new HashSet<>();
            String fileName = String.format(STOCK_TRANSFER_ERROR_LOGNAME, parentCorpId, DateTimeUtil.getNowTime());
            String filePath = stockTransferErrorlogPath.concat(File.separator).concat(fileName).concat(".txt");
            File file = new File(filePath);
            File pDir = file.getParentFile();
            if (!pDir.exists() && !pDir.mkdirs()) {
                throw new RuntimeException("创建文件夹失败");
            }

            log.debug("================开始库存划拨===============");
            try (Reader reader = new FileReader(iccidFile)) {
                List<CsvRow> rows = CsvUtil.getReader(reader).read().getRows();
                for (int i = 1; i < rows.size(); i++) {
                    String iccid = rows.get(i).getRawList().get(0);

                    updateList.add(iccid);

                    if (updateList.size() == 2000) {
                        doit(updateList, file);
                    }
                }

                if (updateList.size() > 0) {
                    doit(updateList, file);
                }
            } catch (Exception e) {
                log.error("读取文件失败：", e);
            }
        }

        private void doit(HashSet<String> updateList, File file) {
            Set<String> cardsNotBelong2Corp = null;
            Set<String> usedCards = null;
            Set<String> cardsHavePackage = null;
            Set<String> cardsHaveBindPackage = null;
            Set<String> cardsNotExist = null;
            boolean throwException = false;

            try {
                ChannelServiceImpl channelService = SpringContextHolder.getBean("channelServiceImpl");
                Set<String> corpIds = channelService.getLowerChannel(parentCorpId, true).stream().map(Channel::getCorpId).collect(Collectors.toSet());
                cardsNotBelong2Corp = channelCardMapper.selectList(Wrappers.lambdaQuery(ChannelCard.class)
                                .select(ChannelCard::getIccid)
                                .in(ChannelCard::getIccid, updateList)
                                .notIn(ChannelCard::getCorpId, corpIds))
                        .stream()
                        .map(ChannelCard::getIccid)
                        .collect(Collectors.toSet());
                updateList.removeAll(cardsNotBelong2Corp);
                if (updateList.size() == 0) {
                    return;
                }

                usedCards = channelCardMapper.selectList(Wrappers.lambdaQuery(ChannelCard.class)
                                .select(ChannelCard::getIccid, ChannelCard::getCooperationMode)
                                .in(ChannelCard::getIccid, updateList)
                                .and(
                                        e -> e.ne(ChannelCard::getCorpId, parentCorpId)
                                                .or()
                                                .isNotNull(ChannelCard::getFlowPoolId)
                                                .or()
                                                .eq(ChannelCard::getCooperationMode, ChannelDistributorDetail.CooperationModeEnum.A2Z.getType()))
                        )
                        .stream()
                        .map(ChannelCard::getIccid)
                        .collect(Collectors.toSet());
                updateList.removeAll(usedCards);
                if (updateList.size() == 0) {
                    return;
                }

                List<ChannelOrder> cardsBatchConfig = channelOrderMapper.selectList(Wrappers.lambdaQuery(ChannelOrder.class)

                        //todo iccid
                        .in(ChannelOrder::getIccid, updateList)
                        .in(ChannelOrder::getOrderChannel, ListUtil.toList("105", "106", "110")));

                Set<String> cardsBatchConfigOrderIds = cardsBatchConfig
                        .stream()
                        .map(ChannelOrder::getOrderUniqueId)
                        .collect(Collectors.toSet());

                cardsHavePackage = channelPackageCardMapper.selectList(Wrappers.lambdaQuery(ChannelPackageCard.class)
                                .select(ChannelPackageCard::getIccid)
                                .notIn(cardsBatchConfig.size() > 0, ChannelPackageCard::getOrderUniqueId, cardsBatchConfigOrderIds)
                                .in(ChannelPackageCard::getIccid, updateList))
                        .stream()
                        .map(ChannelPackageCard::getIccid)
                        .collect(Collectors.toSet());

                updateList.removeAll(cardsHavePackage);
                if (updateList.size() == 0) {
                    return;
                }

                cardsHaveBindPackage = channelBindMapper.selectList(Wrappers.lambdaQuery(ChannelBind.class)
                                .select(ChannelBind::getIccid)
                                .ne(ChannelBind::getOrderChannel, "113")
                                .in(ChannelBind::getIccid, updateList))
                        .stream()
                        .map(ChannelBind::getIccid)
                        .collect(Collectors.toSet());
                updateList.removeAll(cardsHaveBindPackage);
                if (updateList.size() == 0) {
                    return;
                }

                Set<String> cardsBatchConfigIccid = cardsBatchConfig
                        .stream()
                        .map(ChannelOrder::getIccid)
                        .collect(Collectors.toSet());

                updateOrder(cardsBatchConfigIccid, updateList);

                log.debug("更新iccid:", updateList);
                int updateCount = channelCardMapper.update(null, Wrappers.lambdaUpdate(ChannelCard.class)
                        .set(ChannelCard::getCorpId, corpId)
                        .in(ChannelCard::getIccid, updateList));

                if (updateCount != updateList.size()) {
                    Set<String> updateSuccessCards = channelCardMapper.selectList(Wrappers.lambdaQuery(ChannelCard.class)
                                    .select(ChannelCard::getIccid)
                                    .in(ChannelCard::getIccid, updateList))
                            .stream()
                            .map(ChannelCard::getIccid)
                            .collect(Collectors.toSet());
                    updateList.removeAll(updateSuccessCards);
                    cardsNotExist = new HashSet<>(updateList);
                }

            } catch (Exception e) {
                throwException = true;
                log.error("失败咯：", e);
            } finally {
                if (throwException) {
                    writeFile(file, updateList, "系统错误");
                }

                updateList.clear();

                if (!CollectionUtils.isEmpty(cardsNotBelong2Corp)) {
                    writeFile(file, cardsNotBelong2Corp, "卡不属于该渠道商和他的归属渠道商");
                }

                if (!CollectionUtils.isEmpty(usedCards)) {
                    writeFile(file, usedCards, "卡已被划拨或已加入流量池或为A2Z合作模式卡");
                }

                if (!CollectionUtils.isEmpty(cardsHavePackage)) {
                    writeFile(file, cardsHavePackage, "卡已购买套餐");
                }

                if (!CollectionUtils.isEmpty(cardsHaveBindPackage)) {
                    writeFile(file, cardsHaveBindPackage, "卡已购买绑定套餐");
                }

                if (!CollectionUtils.isEmpty(cardsNotExist)) {
                    writeFile(file, cardsNotExist, "卡不存在或未出库");
                }
            }
        }

        private void writeFile(File file, Set<String> iccids, String reason) {
            try (FileOutputStream fileOutputStream = new FileOutputStream(file, true)) {
                StringBuilder builder = new StringBuilder();
                for (String iccid : iccids) {
                    builder.append(iccid)
                            .append(StringPool.COMMA)
                            .append(reason)
                            .append(StringPool.NEWLINE);
                }
                fileOutputStream.write(builder.toString().getBytes());
            } catch (Exception e) {
                log.error("写文件失败", e);
            }
        }

        private void updateOrder(Set<String> cardsBatchConfig, Set<String> cardsReady2Transfer) {
            Set<String> cardsOnlyBatchConfig = cardsBatchConfig.stream()
                    .filter(cardsReady2Transfer::contains)
                    .collect(Collectors.toSet());

            if (cardsOnlyBatchConfig.size() == 0) {
                log.debug("没有仅含批配套餐的卡");
                return;
            }

            channelOrderMapper.update(ChannelOrder.builder()
                            .subAmount(BigDecimal.ZERO)
                            .subCorpId(corpId)
                            .build(),
                    Wrappers.lambdaUpdate(ChannelOrder.class)
                            .in(ChannelOrder::getIccid, cardsOnlyBatchConfig));

            channelOrderDetailMapper.update(ChannelOrderDetail.builder()
                            .subAmount(BigDecimal.ZERO)
                            .subCorpId(corpId)
                            .corpId(corpId)
                            .build(),
                    Wrappers.lambdaUpdate(ChannelOrderDetail.class)
                            .in(ChannelOrderDetail::getIccid, cardsOnlyBatchConfig));

            channelPackageCardMapper.update(ChannelPackageCard.builder()
                            .corpId(corpId)
                            .build(),
                    Wrappers.lambdaUpdate(ChannelPackageCard.class)
                            .in(ChannelPackageCard::getIccid, cardsOnlyBatchConfig));
        }
    }

    //检验资源模式是否能够使用该功能
    public void checkResource(String cooperationMode) {
        if (ChannelDistributorDetail.CooperationModeEnum.RESOURCE_COOPERATION.getType().equals(cooperationMode)) {
            throw new BizException("资源合作模式下不允许使用该功能");
        }
    }

    private String getChanelType(String corpId, String cooperationMode) {
        QueryWrapper<ChannelDistributorDetail> distributorDetailQueryWrapper = new QueryWrapper<>();
        distributorDetailQueryWrapper.eq("corp_id", corpId);
        ChannelDistributorDetail channelDistributorDetail = channelDistributorDetailMapper.selectOne(distributorDetailQueryWrapper);
        String channelType = "";
        if ("1".equals(cooperationMode)) {
            channelType = channelDistributorDetail.getChannelType();
        } else if ("2".equals(cooperationMode)) {
            channelType = channelDistributorDetail.getA2zChannelType();
        }
        return channelType;
    }
}
