package com.ebupt.cmi.clientmanagement.service.cooperationMode;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebupt.cmi.clientmanagement.domain.dto.MarketingAccountFlow.MarketingAccountFlowA2zDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.MarketingAccountFlow.MarketingAccountFlowSaleByProxyDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.MarketingAccountFlow.MarketingAccountFlowSaleByProxyRebateDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.Channel;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsChannelMarketBillflow;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsChannelMarketBillflowA2z;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.ExportVO;
import com.ebupt.cmi.clientmanagement.domain.vo.MarketingAccountFlowVO;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.feign.back.vo.BatchSyncfileTack;
import com.ebupt.cmi.clientmanagement.feign.mkt.domain.MarketingCampaignDTO;
import com.ebupt.cmi.clientmanagement.feign.mkt.domain.SelectMarketingCampaignDTO;
import com.ebupt.cmi.clientmanagement.feign.oms.domain.OmsCountry;
import com.ebupt.cmi.clientmanagement.mapper.CmsChannelMarketBillFlowMapper;
import com.ebupt.cmi.clientmanagement.utils.DateTimeUtil;
import com.ebupt.cmi.clientmanagement.utils.ExcelWriteHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CooperationSaleByProxyService extends CooperationModeAbstract{

    @Autowired
    private CmsChannelMarketBillFlowMapper cmsChannelMarketBillflowMapper;



    @Override
    public IPage<?> getMarketingAccountFlow(MarketingAccountFlowVO marketingAccountFlowVO) {

        IPage<CmsChannelMarketBillflow> pageChanneMarketlBillflow = getPageChanneMarketlBillflow(marketingAccountFlowVO);


        List<CmsChannelMarketBillflow> records = pageChanneMarketlBillflow.getRecords();
        //判断当前页面是否有数据
        if(CollUtil.isEmpty(records)){
            return pageChanneMarketlBillflow;
        }
        //获取营销活动id
        List<Long> marketingCampaignList = records.stream().map(CmsChannelMarketBillflow::getActivityId).collect(Collectors.toList());

        SelectMarketingCampaignDTO selectMarketingCampaignDTO = new SelectMarketingCampaignDTO();

        selectMarketingCampaignDTO.setMarketingCampaignIds(marketingCampaignList);

        List<MarketingCampaignDTO> marketingCampaigns = getMarketingCampaign(selectMarketingCampaignDTO);



        Page<MarketingAccountFlowSaleByProxyDTO> marketingAccountFlowSaleByProxyDTOPage = new Page<>();

        BeanUtils.copyProperties(pageChanneMarketlBillflow, marketingAccountFlowSaleByProxyDTOPage);

        try {
            marketingAccountFlowSaleByProxyDTOPage.setRecords(createMarketingAccountFlowSaleByProxyDTO(marketingAccountFlowVO.getSelectType(),records, marketingCampaigns));
        }catch (BizException e){
            log.error(e.getMessage());
            log.error("查询流水信息失败");
            throw new BizException("Activity information is empty");

        }

        return marketingAccountFlowSaleByProxyDTOPage;

    }

    public ExportVO exportFile(String selectType,String corpId, Date beginDate, Date endDate,String activityId) {

        Long taskId = null;
        try {
            String sufixFileTime = "";
            String taskDesc = "";
            String fileName = "";
            String corpName="";
            sufixFileTime = DateTimeUtil.getNowTime();
            Channel channel = channelMapper.selectById(corpId);
            corpName=channel.getCorpName();
            if(CooperationConstant.BILL_FLOW.equals(selectType)){
                taskDesc = corpName+"_营销账单流水" + "_" + sufixFileTime;
                fileName = corpName+"_营销账单流水" + "_" + sufixFileTime;
            }else if (CooperationConstant.BILL_FLOW_REBATE.equals(selectType)){
                taskDesc = corpName+"_营销活动返利流水详情" + "_" + sufixFileTime;
                fileName = corpName+"_营销活动返利流水详情" + "_" + sufixFileTime;
            }


            String filePath = billFlowExportPath;
            taskId = Response.getAndCheckRemoteData(backFeignClient.addTask(BatchSyncfileTack.builder()
                    .fileName(fileName + ".xlsx")
                    .filePath(billFlowExportPath + File.separator + fileName + ".xlsx")
                    .taskDesc(taskDesc)
                    .corpId(corpId)
                    .taskStatus(BatchSyncfileTack.TaskStatus.PROCESSING.getStatus())
                    .build()));

            String finalFileName = fileName;

            Long finalTaskId = taskId;
            CompletableFuture<Void> runAsync = CompletableFuture.runAsync(() -> {
                writeExcel(filePath, finalTaskId, finalFileName, selectType, corpId, beginDate, endDate,activityId);
            });


            return ExportVO.builder().taskId(taskId).taskName(fileName).build();
        } catch (Exception ex) {
            if (taskId != null) {
                backFeignClient.updateTaskStatus(taskId, BatchSyncfileTack.TaskStatus.FAIL.getStatus(), null);
            }
            log.error("创建任务失败", ex);
            throw new BizException("Failed to create download mission");
        }

    }

    public void writeExcel(String filePath,Long taskId,String fileName,String selectType,String corpId, Date beginDate, Date endDate,String activityId){

        try {

            File fileDir = new File(filePath);
            if (!fileDir.exists()) {
                if (!fileDir.mkdirs()) {
                    log.error("创建文件夹失败");
                    throw new RuntimeException("File generation failed");
                }
            }

            File file = new File(filePath + File.separator + fileName+ ".xlsx");

            ExcelWriter excelWriter = EasyExcel.write(file).
                    registerWriteHandler(ExcelWriteHandler.getHandler()).build();

            ExcelWriterSheetBuilder writerSheetBuilder = EasyExcel.writerSheet("营销流水导出接口");

            //流水返利和流水共用一个导出逻辑，如新增字段需对2个表头类字段进行维护
            WriteTable writeTable=null;
            if(CooperationConstant.BILL_FLOW.equals(selectType)){
                writeTable= EasyExcel.writerTable(0).head(MarketingAccountFlowSaleByProxyDTO.class)
                        .registerWriteHandler(ExcelWriteHandler.getHandler())
                        .needHead(Boolean.TRUE).build();
            }else if (CooperationConstant.BILL_FLOW_REBATE.equals(selectType)){
                writeTable = EasyExcel.writerTable(0).head(MarketingAccountFlowSaleByProxyRebateDTO.class)
                        .registerWriteHandler(ExcelWriteHandler.getHandler())
                        .needHead(Boolean.TRUE).build();
            }

            WriteSheet writeSheet = writerSheetBuilder.needHead(Boolean.FALSE).build();


            try {
                Long allCount = cmsChannelMarketBillflowMapper.getCountBySelectParam(corpId, beginDate, endDate,activityId);
                long page=allCount/MAX_PAGE_SIZE+1;
                for(int i=0;i<page;i++){
                    List<CmsChannelMarketBillflow> billflowBySelectParam = cmsChannelMarketBillflowMapper.getBillflowBySelectParam(corpId, beginDate, endDate, i * MAX_PAGE_SIZE, MAX_PAGE_SIZE,activityId);

                    List<Long> activityIds = billflowBySelectParam.stream().map(CmsChannelMarketBillflow::getActivityId).collect(Collectors.toList());
                    SelectMarketingCampaignDTO selectMarketingCampaignDTO = new SelectMarketingCampaignDTO();
                    selectMarketingCampaignDTO.setMarketingCampaignIds(activityIds);
                    List<MarketingCampaignDTO> marketingCampaigns = getMarketingCampaign(selectMarketingCampaignDTO);

                    List<MarketingAccountFlowSaleByProxyDTO> marketingAccountFlowSaleByProxyDTOS = createMarketingAccountFlowSaleByProxyDTO(selectType, billflowBySelectParam, marketingCampaigns);
                    marketingAccountFlowSaleByProxyDTOS.forEach(item->{item.setType(changeType(item.getType()));});
                    excelWriter.write(marketingAccountFlowSaleByProxyDTOS, writeSheet, writeTable);
                }

                backFeignClient.updateTaskStatus(taskId, BatchSyncfileTack.TaskStatus.FINISH.getStatus(), null);

            }catch (Exception e){
                log.error("导出失败：",e);
                throw new BizException("File export failed");
            }finally {
                excelWriter.finish();
            }

        } catch (Exception e) {
            backFeignClient.updateTaskStatus(taskId, BatchSyncfileTack.TaskStatus.FAIL.getStatus(), null);
            e.printStackTrace();
        }
    }



    private IPage<CmsChannelMarketBillflow> getPageChanneMarketlBillflow(MarketingAccountFlowVO marketingAccountFlowVO){
        IPage<CmsChannelMarketBillflow> page = new Page<>(marketingAccountFlowVO.getPageNum(), marketingAccountFlowVO.getPageSize());

        LambdaQueryWrapper<CmsChannelMarketBillflow> lambdaQueryWrapper = new QueryWrapper<CmsChannelMarketBillflow>().lambda();
        if(StrUtil.isNotEmpty(marketingAccountFlowVO.getCorpId())){
            lambdaQueryWrapper.eq(CmsChannelMarketBillflow::getCorpId, marketingAccountFlowVO.getCorpId());
        }
        if(ObjectUtil.isNotEmpty(marketingAccountFlowVO.getBeginDate())){
            lambdaQueryWrapper.ge(CmsChannelMarketBillflow::getCreateTime, marketingAccountFlowVO.getBeginDate());
        }
        if (ObjectUtil.isNotEmpty(marketingAccountFlowVO.getEndDate())){
            lambdaQueryWrapper.le(CmsChannelMarketBillflow::getCreateTime, marketingAccountFlowVO.getEndDate());
        }
        if(ObjectUtil.isNotEmpty(marketingAccountFlowVO.getActivityId())){
            lambdaQueryWrapper.eq(CmsChannelMarketBillflow::getActivityId, marketingAccountFlowVO.getActivityId());
        }
        return cmsChannelMarketBillflowMapper.selectPage(page, lambdaQueryWrapper);
    }

    private List<MarketingAccountFlowSaleByProxyDTO> createMarketingAccountFlowSaleByProxyDTO(String selectType,List<CmsChannelMarketBillflow> CmsChannelMarketBillflowSaleByProxyList, List<MarketingCampaignDTO> marketingCampaigns){

        if(CollectionUtil.isEmpty(marketingCampaigns)){
            throw new BizException("Activity information is empty");
        }

        Map<Long, MarketingCampaignDTO> marketingCampaignDTOMap = marketingCampaigns.stream().collect(Collectors.toMap(MarketingCampaignDTO::getId, mk -> mk, (mk1, mk2) -> mk1));

        List<MarketingAccountFlowSaleByProxyDTO> marketingAccountFlowSaleByProxyDTOS = new ArrayList<>();

        for (CmsChannelMarketBillflow cmsChannelMarketBillflow : CmsChannelMarketBillflowSaleByProxyList) {

            MarketingCampaignDTO marketingCampaignDTO = marketingCampaignDTOMap.get(cmsChannelMarketBillflow.getActivityId());
            if(Objects.isNull(marketingCampaignDTO)){
                marketingCampaignDTO=new MarketingCampaignDTO();
            }
            MarketingAccountFlowSaleByProxyDTO marketingAccountFlowSaleByProxyDTO = new MarketingAccountFlowSaleByProxyDTO();

            marketingAccountFlowSaleByProxyDTO.setActivity(marketingCampaignDTO.getCampaignName());
            marketingAccountFlowSaleByProxyDTO.setAmount(cmsChannelMarketBillflow.getAmount().divide(new BigDecimal(100),2, RoundingMode.HALF_UP));
            marketingAccountFlowSaleByProxyDTO.setCurrency(changeCurrencyCode(cmsChannelMarketBillflow.getCurrencyCode()));
            marketingAccountFlowSaleByProxyDTO.setTotalAmount(cmsChannelMarketBillflow.getTotalAmount().divide(new BigDecimal(100),2, RoundingMode.HALF_UP));
            marketingAccountFlowSaleByProxyDTO.setType(cmsChannelMarketBillflow.getType());
            marketingAccountFlowSaleByProxyDTO.setConsumptionDate(cmsChannelMarketBillflow.getCreateTime());
            marketingAccountFlowSaleByProxyDTO.setOrderId(cmsChannelMarketBillflow.getOrderId());
            marketingAccountFlowSaleByProxyDTO.setTotalOrderId(cmsChannelMarketBillflow.getTotalOrderId());

            marketingAccountFlowSaleByProxyDTO.setDeposit(cmsChannelMarketBillflow.getDeposit().divide(new BigDecimal(100),2, RoundingMode.HALF_UP));
            marketingAccountFlowSaleByProxyDTO.setTotalAmount(cmsChannelMarketBillflow.getTotalAmount().divide(new BigDecimal(100),2, RoundingMode.HALF_UP));



            marketingAccountFlowSaleByProxyDTOS.add(marketingAccountFlowSaleByProxyDTO);
        }

        return marketingAccountFlowSaleByProxyDTOS;
    }

    // 1.增加影响返利款
    //2.代销套餐订购
    //3.加油包订购
    //4.套餐退订
    //5.加油包退订
    private String changeType(String type){
        switch (type){
            case "1":
                return "Increase marketing rebates";
            case "2":
                return "Distribution package ordering";
            case "3":
                return "Add-on pack purchase";
            case "4":
                return "Package refund";
            case "5":
                return "Add-on pack refund";
            case "6":
                return "Reset the expired marketing budget";
            case "7":
                return "Offset Marketing Rebate Amount";
        }
        return type;
    }
}
