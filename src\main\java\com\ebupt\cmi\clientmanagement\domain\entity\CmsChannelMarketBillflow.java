package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

@Builder
@TableName("cms_channel_market_billflow")
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CmsChannelMarketBillflow {

    @TableId
    private Long id;

    //'渠道商ID'
    private String corpId;

    //'子单id'
    private String orderId;

    //类型：
    // 1.增加影响返利款
    //2.代销套餐订购
    //3.加油包订购
    //4.套餐退订
    //5.加油包退订
    private String type;

    //币种编码：156 人民币 840 美元 344 港币
    private String currencyCode;

    //交易金额，单位：分
    private BigDecimal amount;

    //账户余额（可用额度）单位：分
    private BigDecimal deposit;

    //营销活动id
    private Long activityId;

    //'创建时间'
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTime;

    //未过期活动总余额 单位：分
    private BigDecimal totalAmount;

    private String totalOrderId;

    private String rebateId;

}
