<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ebupt.cmi.clientmanagement.mapper.CmsAssignedImsiRecordMapper">

    <insert id="batchInsert">
        insert into
            cms_assigned_imsi_record(id,imsi,corp_id,flow_downlink,flow_uplink,flow_count,supplier_id,supplier_name,mcc,plmnlist,rat_type,start_time,end_time,date_belong_to,msisdn)
        values
        <foreach collection="list" open="" close="" separator="," item="e">
            (#{e.id}, #{e.imsi}, #{e.corpId}, #{e.flowDownlink}, #{e.flowUplink}, #{e.flowCount}, #{e.supplierId}
            ,#{e.supplierName}, #{e.mcc}, #{e.plmnlist}, #{e.ratType}, #{e.startTime}, #{e.endTime}, #{e.dateBelongTo}, #{e.msisdn})
        </foreach>
    </insert>
    <select id="getlist" resultType="com.ebupt.cmi.clientmanagement.domain.entity.CmsAssignedImsiRecord">
        select
            id,imsi,msisdn, flow_downlink, flow_uplink, flow_count, mcc, plmnlist,start_time,end_time,supplier_name,supplier_id
        from
            cms_assigned_imsi_record
        where
            corp_id = #{corpId}
            AND date_belong_to = #{dateBelongTo}
            AND id <![CDATA[ > ]]> #{id}
        limit #{batchSize}
    </select>
</mapper>