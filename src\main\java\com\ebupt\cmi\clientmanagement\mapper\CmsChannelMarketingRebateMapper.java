package com.ebupt.cmi.clientmanagement.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsChannelMarketingRebate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;

/**
 * (CmsChannelMarketingRebate)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-04 14:23:22
 */
@Mapper
public interface CmsChannelMarketingRebateMapper extends BaseMapper<CmsChannelMarketingRebate> {

    @Update("UPDATE cms_channel_marketing_rebate \n" +
            "SET remain_amount = remain_amount - ${deductionAmount}" +
            "WHERE\n" +
            "\tremain_amount >= ${deductionAmount}\n" +
            "\tAND id = ${id}")
    Integer updateRemainAmount(@Param("id") Long id, @Param("deductionAmount") BigDecimal deductionAmount, @Param("version") Long version);

    @Update("UPDATE cms_channel_marketing_rebate \n" +
            "SET remain_amount = remain_amount + ${deductionAmount}\n" +
            "WHERE\n" +
            "\tid = ${id}")
    Integer addRemainAmount(@Param("id") Long id, @Param("deductionAmount") BigDecimal deductionAmount);

    @Select("SELECT IFNULL(SUM(remain_amount),0) FROM cms_channel_marketing_rebate WHERE corp_id = #{corpId} AND type = #{type} AND expiry_time > NOW()")
    BigDecimal getTotalAmount(@Param("corpId") String corpId,@Param("type") String type);

    @Select("SELECT IFNULL(SUM(remain_amount),0) FROM cms_channel_marketing_rebate WHERE corp_id = #{corpId} AND activity_id = #{activityId} AND expiry_time > NOW()")
    BigDecimal getChannelRebateDeposit(@Param("corpId") String corpId,@Param("activityId") Long activityId);

    @Select("select * from cms_channel_marketing_rebate where id = ${id}")
    CmsChannelMarketingRebate selectForUpdate(@Param("id") Long id);

    @Update("UPDATE cms_channel_marketing_rebate \n" +
            "SET remain_amount = remain_amount - ${deductionAmount} \n" +
            "WHERE\n" +
            "\tid = ${id}")
    void reduceA2zMarketingRebate(@Param("id") Long id, @Param("deductionAmount") BigDecimal deductionAmount);
}

