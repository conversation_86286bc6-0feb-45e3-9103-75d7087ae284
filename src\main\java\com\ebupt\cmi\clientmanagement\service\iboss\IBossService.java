package com.ebupt.cmi.clientmanagement.service.iboss;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebupt.cmi.clientmanagement.domain.dto.ChargeRecordDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelChargeRecord;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsChannelChargeDetail;
import com.ebupt.cmi.clientmanagement.domain.req.IBossRechargeReq;
import com.ebupt.cmi.clientmanagement.domain.req.IBossSelectReq;
import com.ebupt.cmi.clientmanagement.domain.req.RebateRechargeReq;
import com.ebupt.cmi.clientmanagement.domain.response.ChannelQuota;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.AuthPayBillVO;
import com.ebupt.cmi.clientmanagement.domain.vo.ChargePageVO;
import com.ebupt.cmi.clientmanagement.domain.vo.PayBillVO;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@Service
public interface IBossService {

    /**
     * IBoss充值接口
     * @param iBossRechargeReq
     * @return
     */
    Response<Void> recharge(IBossRechargeReq iBossRechargeReq);

    Response<ChannelQuota> query(IBossSelectReq iBossSelectReq);

    void payBill(PayBillVO payBillVO);

    void cancelPayBill(long id, String userName);

    Page<CmsChannelChargeDetail> getPage(ChargePageVO chargePageVO);

    void authPayBill(AuthPayBillVO authPayBillVO);

    void downLoad(long id, HttpServletResponse response);

    ResponseEntity<Resource> downLoad2(long id);

    ResponseEntity<Resource> downLoad3(String fileAddress, String corpName,String invoiceNo);

    void chargeDownLoad(long id, HttpServletResponse response);

    Map<Long,Long> rebateRecharge(List<RebateRechargeReq> rebateRechargeReq);

    List<ChannelChargeRecord> getChargeRecord(ChargeRecordDTO chargeRecordDTO);
}
