# H流程V卡上网激活变更需求实现方案

## 1. 变更需求概述

### 1.1 需求背景
基于H流程的V卡上网激活功能变更，重点实现V卡上网场景下的差异化UPCC签约模板选择逻辑。该变更通过 `signSlowSpeed` 参数控制，在V卡上网时触发特殊的签约模板选择策略。

### 1.2 核心变更点
- **H卡签约前置处理**: `postProcessBeforeSignatureWithHcard` 方法为V卡上网准备UPCC上下文
- **差异化签约逻辑**: 基于 `signSlowSpeed=true` 参数的V卡专用签约模板选择
- **速度比较机制**: V卡速度与H卡配置低速模板的智能比较
- **热点支持策略**: 基于热点支持能力的签约模板差异化选择

## 2. 核心方法分析

### 2.1 postProcessBeforeSignatureWithHcard 方法详解

#### 2.1.1 方法职责
该方法在H卡签约前执行，负责准备UPCC签约所需的上下文信息，为后续的签约模板选择提供数据基础。

#### 2.1.2 业务流程
```mermaid
graph TD
    A[postProcessBeforeSignatureWithHcard] --> B{是否流量池套餐?}
    B -->|是| C[跳过UPCC签约ID获取]
    B -->|否| D[创建UpccContext]
    D --> E{是否达量释放且限速?}
    E -->|是| F[取消通用签约]
    E -->|否| G{是否有控速标识?}
    G -->|是| H[从模板获取签约信息]
    G -->|否| I{套餐是否限速?}
    I -->|是| J[使用慢速签约模板]
    I -->|否| K[从缓存/DB获取签约ID]
    F --> L[设置UpccContext到SurfingContext]
    H --> L
    J --> L
    K --> L
```

#### 2.1.3 关键业务逻辑
```java
@Override
public void postProcessBeforeSignatureWithHcard(LocationUpdateHContext context) {
    SurfingContext surfingContext = context.getSurfingContext();
    ChannelPackageCard packageCardRecord = surfingContext.getPackageCardRecord();
    
    // 1. 流量池套餐跳过处理
    if (PackageType.FLOW_POOL.getType().equals(packageCardRecord.getPackageType())) {
        log.debug("[H流程] 流量池不需要获取upcc签约id");
        return;
    }
    
    UpccContext upccContext = new UpccContext();
    boolean isLimitedSpeed = ChannelPackageCard.SurfStatusEnum.LIMIT.getValue()
        .equals(packageCardRecord.getSurfStatus());
    
    // 2. 达量释放套餐特殊处理
    if (isLimitedSpeed && ChannelPackageCard.ControlLogicEnum.REACH_RELEASE.getValue()
        .equals(packageCardRecord.getControlLogic())) {
        upccContext.setUpccSignBizId(StringPool.EMPTY);
        upccContext.setUpccSpeed(-999999999L);
        surfingContext.setUpccContext(upccContext);
        return;
    }
    
    // 3. 控速标识处理
    String upccTemplateId = packageCardRecord.getSpeedControlId();
    if (!StringUtils.isEmpty(upccTemplateId)) {
        UpccTemplate upccTemplate = pmsFeignClient.getUpccSignIdByTemplateId(upccTemplateId).get();
        upccContext.setSupportHotspots(upccTemplate.getSupportHotspot());
        upccContext.setUpccSpeed(calculateSpeed(upccTemplate));
        upccContext.setUpccSignBizId(upccTemplate.getSignId());
        surfingContext.setUpccContext(upccContext);
        return;
    }
    
    // 4. 套餐状态处理
    if (isLimitedSpeed) {
        upccContext.setUpccSignBizId(packageCardRecord.getSlowSpeedSignBizId());
        upccContext.setUpccSpeed(-999999999L);
    } else {
        upccContext = luUtils.getUpccSignBizId(packageCardRecord);
    }
    
    surfingContext.setUpccContext(upccContext);
}
```

## 3. 核心变更代码深度分析

### 3.1 signSlowSpeed 参数影响分析

#### 3.1.1 参数作用机制
```java
// V卡上网调用: signSlowSpeed = true
boolean isPostProcessNecessary = invokeCoreNetWithHcard(context, true);

// H卡上网调用: signSlowSpeed = false  
boolean postProcessIsNecessary = invokeCoreNetWithHcard(context, false);
```

#### 3.1.2 签约模板选择决策树
```mermaid
graph TD
    A[UPCC签约模板选择] --> B{是否流量池?}
    B -->|是| C[流量池处理逻辑]
    B -->|否| D{signSlowSpeed?}
    D -->|false H卡上网| E[直接使用upccContext.getUpccSignBizId]
    D -->|true V卡上网| F[V卡专用逻辑]
    F --> G{upccSpeed < slowUpccSpeed?}
    G -->|是| H[使用V卡模板: upccContext.getUpccSignBizId]
    G -->|否| I{支持热点?}
    I -->|是| J[使用支持热点模板: slowSpeedUpccId_y]
    I -->|否| K[使用不支持热点模板: slowSpeedUpccId_n]
    
    C --> L[根据currentRateType选择]
    L --> M{currentRateType == NORMAL?}
    M -->|是| N{signSlowSpeed?}
    N -->|true| O[低速模板: lowSpeedSignBizId]
    N -->|false| P[高速模板: highSpeedSignBizId]
    M -->|否| Q[限速模板: slowSpeedSignBizId]
```

### 3.2 V卡速度比较逻辑详解

#### 3.2.1 速度比较核心代码
```java
if (signSlowSpeed) {
    // V卡上网，限速或者低速
    Long upccSpeed = upccContext.getUpccSpeed();
    if (upccSpeed.compareTo(slowUpccSpeed) < 0) {
        // V卡速度 < H卡配置低速模板阈值
        upccSignBizId = upccContext.getUpccSignBizId();
    } else {
        // V卡速度 >= H卡配置低速模板阈值
        String supportHotspots = upccContext.getSupportHotspots();
        if (CmsPackageCardUpccRelation.SupportHotSpot.YES.getValue().equals(supportHotspots)) {
            upccSignBizId = slowSpeedUpccId_y;  // 支持热点的低速模板
        } else {
            upccSignBizId = slowSpeedUpccId_n;  // 不支持热点的低速模板
        }
    }
}
```

#### 3.2.2 业务规则说明
1. **速度阈值判断**: 
   - `upccSpeed < slowUpccSpeed`: V卡速度低于系统配置的低速阈值
   - 使用V卡自身的签约模板，保持V卡原有的速度特性

2. **热点支持策略**:
   - `upccSpeed >= slowUpccSpeed`: V卡速度达到或超过低速阈值
   - 根据V卡是否支持热点功能选择不同的H卡低速模板

## 4. 配置参数详解

### 4.1 核心配置参数
```java
@Value("${lu.support_hotspots.slow_speed_upcc_id}")
private String slowSpeedUpccId_y;      // 支持热点的低速UPCC签约模板ID

@Value("${lu.not_support_hotspots.slow_speed_upcc_id}")  
private String slowSpeedUpccId_n;      // 不支持热点的低速UPCC签约模板ID

@Value("${lu.upcc_speed}")
private Long slowUpccSpeed;            // H卡低速模板速度阈值
```

### 4.2 配置参数业务含义

#### 4.2.1 slowSpeedUpccId_y (支持热点低速模板)
- **用途**: V卡速度≥阈值且支持热点时使用
- **特性**: 提供热点功能支持的低速上网服务
- **适用场景**: 高速V卡在H卡低速环境下的热点共享

#### 4.2.2 slowSpeedUpccId_n (不支持热点低速模板)  
- **用途**: V卡速度≥阈值且不支持热点时使用
- **特性**: 仅提供基础低速上网服务，无热点功能
- **适用场景**: 高速V卡在H卡低速环境下的基础上网

#### 4.2.3 slowUpccSpeed (速度阈值)
- **用途**: 判断V卡是否需要降级到H卡低速模板的临界值
- **单位**: 通常为Kbps或Mbps
- **作用**: 速度分界线，决定签约模板选择策略

## 5. 业务场景分析

### 5.1 V卡上网场景分类

#### 5.1.1 场景一: 低速V卡 (upccSpeed < slowUpccSpeed)
```
条件: V卡自身速度 < 系统配置低速阈值
策略: 使用V卡原有签约模板 (upccContext.getUpccSignBizId())
目的: 保持V卡原有的低速特性，不进行模板转换
```

#### 5.1.2 场景二: 高速V卡+支持热点 (upccSpeed >= slowUpccSpeed && supportHotspots = YES)
```
条件: V卡速度 >= 阈值 且 支持热点功能
策略: 使用支持热点的H卡低速模板 (slowSpeedUpccId_y)
目的: 在H卡低速环境下提供热点共享能力
```

#### 5.1.3 场景三: 高速V卡+不支持热点 (upccSpeed >= slowUpccSpeed && supportHotspots = NO)
```
条件: V卡速度 >= 阈值 但 不支持热点功能  
策略: 使用不支持热点的H卡低速模板 (slowSpeedUpccId_n)
目的: 在H卡低速环境下提供基础上网服务
```

### 5.2 流量池与普通套餐差异

#### 5.2.1 流量池套餐处理
```java
if (context.isFlowPool()) {
    final String currentRateType = context.getChannelAndCard().getCurrentRateType();
    if (CurrentRateType.NORMAL.getType().equals(currentRateType)) {
        upccSignBizId = signSlowSpeed ? lowSpeedSignBizId : highSpeedSignBizId;
    } else {
        upccSignBizId = packageCardRecord.getSlowSpeedSignBizId();
    }
}
```

#### 5.2.2 普通套餐处理
```java
else {
    if (signSlowSpeed) {
        // V卡上网的复杂逻辑
    } else {
        // H卡上网的简单逻辑
        upccSignBizId = upccContext.getUpccSignBizId();
    }
}
```

## 6. 变更影响评估

### 6.1 对现有H流程的影响

#### 6.1.1 兼容性分析
- **H卡上网**: 完全兼容，signSlowSpeed=false时走原有逻辑
- **流量池**: 保持原有处理逻辑不变
- **普通套餐**: 仅在V卡上网时启用新逻辑

#### 6.1.2 性能影响
- **新增计算**: V卡速度比较和热点支持判断
- **配置依赖**: 新增3个配置参数的读取和验证
- **决策复杂度**: 增加了签约模板选择的决策分支

### 6.2 风险评估

#### 6.2.1 配置风险
- **参数缺失**: slowSpeedUpccId_y/n 配置错误可能导致签约失败
- **阈值设置**: slowUpccSpeed 设置不当影响模板选择准确性

#### 6.2.2 业务风险  
- **模板匹配**: 错误的签约模板可能影响用户上网体验
- **热点功能**: 热点支持判断错误影响功能可用性

## 7. 实施建议

### 7.1 配置管理
1. **参数验证**: 启动时验证配置参数的有效性
2. **默认值**: 为关键参数设置合理的默认值
3. **动态配置**: 支持运行时配置更新能力

### 7.2 监控告警
1. **签约成功率**: 监控V卡UPCC签约的成功率
2. **模板使用**: 统计各签约模板的使用频率
3. **异常告警**: 配置参数异常或签约失败的告警

### 7.3 测试策略
1. **场景覆盖**: 覆盖所有V卡速度和热点支持的组合场景
2. **边界测试**: 测试速度阈值边界值的处理
3. **兼容性测试**: 确保H卡上网功能不受影响

## 8. 技术实现细节

### 8.1 关键数据结构

#### 8.1.1 UpccContext 上下文
```java
public class UpccContext {
    private String upccSignBizId;      // UPCC签约业务ID
    private Long upccSpeed;            // UPCC速度限制 (单位: bps)
    private String supportHotspots;    // 是否支持热点: "1"-是, "2"-否
}
```

#### 8.1.2 ChannelPackageCard 套餐卡关系
```java
public class ChannelPackageCard {
    private String surfStatus;         // 上网状态: LIMIT-限速, NORMAL-正常
    private String controlLogic;       // 控制逻辑: REACH_RELEASE-达量释放
    private String speedControlId;     // 控速标识ID
    private String slowSpeedSignBizId; // 慢速签约模板ID
    private String signBizId;          // 高速签约模板ID
    private String limitSpeedSignBizId; // 限速签约模板ID
}
```

### 8.2 核心算法实现

#### 8.2.1 速度计算算法
```java
private Long calculateSpeed(UpccTemplate upccTemplate) {
    // 单位转换: 1-Kbps, 其他-Mbps
    if ("1".equals(upccTemplate.getUnit())) {
        return upccTemplate.getRate() * 1024L;      // Kbps -> bps
    } else {
        return upccTemplate.getRate() * 1024 * 1024L; // Mbps -> bps
    }
}
```

#### 8.2.2 签约模板选择算法
```java
private String selectUpccSignBizId(LocationUpdateHContext context, boolean signSlowSpeed) {
    SurfingContext surfingContext = context.getSurfingContext();
    ChannelPackageCard packageCardRecord = surfingContext.getPackageCardRecord();
    UpccContext upccContext = surfingContext.getUpccContext();

    if (context.isFlowPool()) {
        return handleFlowPoolSignature(context, signSlowSpeed, packageCardRecord);
    } else {
        return handleNormalPackageSignature(context, signSlowSpeed, upccContext);
    }
}

private String handleNormalPackageSignature(LocationUpdateHContext context,
                                           boolean signSlowSpeed,
                                           UpccContext upccContext) {
    if (!signSlowSpeed) {
        // H卡上网: 直接返回上下文中的签约ID
        return upccContext.getUpccSignBizId();
    }

    // V卡上网: 复杂的速度比较和热点判断逻辑
    Long upccSpeed = upccContext.getUpccSpeed();
    if (upccSpeed.compareTo(slowUpccSpeed) < 0) {
        // 低速V卡: 保持原有模板
        return upccContext.getUpccSignBizId();
    } else {
        // 高速V卡: 根据热点支持选择H卡低速模板
        String supportHotspots = upccContext.getSupportHotspots();
        return CmsPackageCardUpccRelation.SupportHotSpot.YES.getValue()
               .equals(supportHotspots) ? slowSpeedUpccId_y : slowSpeedUpccId_n;
    }
}
```

### 8.3 异常处理机制

#### 8.3.1 配置参数异常
```java
@PostConstruct
public void validateConfiguration() {
    if (StringUtils.isEmpty(slowSpeedUpccId_y)) {
        throw new BizException("配置参数 lu.support_hotspots.slow_speed_upcc_id 不能为空");
    }
    if (StringUtils.isEmpty(slowSpeedUpccId_n)) {
        throw new BizException("配置参数 lu.not_support_hotspots.slow_speed_upcc_id 不能为空");
    }
    if (slowUpccSpeed == null || slowUpccSpeed <= 0) {
        throw new BizException("配置参数 lu.upcc_speed 必须大于0");
    }
}
```

#### 8.3.2 业务逻辑异常
```java
private String safeGetUpccSignBizId(UpccContext upccContext) {
    try {
        String signBizId = upccContext.getUpccSignBizId();
        if (StringUtils.isEmpty(signBizId)) {
            log.warn("[H流程] UPCC签约ID为空，使用默认模板");
            return getDefaultUpccSignBizId();
        }
        return signBizId;
    } catch (Exception e) {
        log.error("[H流程] 获取UPCC签约ID异常", e);
        throw new BizException("UPCC签约模板获取失败");
    }
}
```

## 9. 流程时序图

### 9.1 V卡上网激活完整时序
```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Adapter as PackageMultiTypeSurfingAdapter
    participant Context as LocationUpdateHContext
    participant PMS as PmsFeignClient
    participant CoreNet as CoreNetCaller

    Client->>Adapter: vCardSurfing(context)
    Adapter->>Adapter: postProcessBeforeSignatureWithHcard(context)

    Note over Adapter: 准备UPCC上下文
    Adapter->>PMS: getUpccSignIdByTemplateId(templateId)
    PMS-->>Adapter: UpccTemplate
    Adapter->>Context: setUpccContext(upccContext)

    Adapter->>Adapter: invokeCoreNetWithHcard(context, true)
    Note over Adapter: signSlowSpeed=true 触发V卡逻辑

    alt upccSpeed < slowUpccSpeed
        Adapter->>Adapter: 使用V卡原有模板
    else upccSpeed >= slowUpccSpeed
        alt supportHotspots = YES
            Adapter->>Adapter: 使用 slowSpeedUpccId_y
        else supportHotspots = NO
            Adapter->>Adapter: 使用 slowSpeedUpccId_n
        end
    end

    Adapter->>CoreNet: upccSignature(msisdn, upccSignBizId)
    CoreNet-->>Adapter: 签约成功

    Adapter->>Adapter: getVcardAccountDetails(context)
    Adapter->>Adapter: invokeCoreNetWithVcard(context, vcardDetails)

    Adapter-->>Client: V卡激活完成
```

### 9.2 UPCC签约模板选择决策流程
```mermaid
flowchart TD
    A[开始UPCC签约] --> B{套餐类型}
    B -->|流量池| C[流量池处理逻辑]
    B -->|普通套餐| D{signSlowSpeed?}

    D -->|false H卡上网| E[upccContext.getUpccSignBizId]
    D -->|true V卡上网| F[V卡专用逻辑]

    F --> G{upccSpeed vs slowUpccSpeed}
    G -->|upccSpeed < slowUpccSpeed| H[保持V卡原模板]
    G -->|upccSpeed >= slowUpccSpeed| I{热点支持?}

    I -->|支持热点| J[slowSpeedUpccId_y]
    I -->|不支持热点| K[slowSpeedUpccId_n]

    C --> L[根据currentRateType选择]
    E --> M[执行UPCC签约]
    H --> M
    J --> M
    K --> M
    L --> M

    M --> N[签约完成]
```

## 10. 测试用例设计

### 10.1 单元测试用例

#### 10.1.1 postProcessBeforeSignatureWithHcard 测试
```java
@Test
public void testPostProcessBeforeSignatureWithHcard_FlowPool() {
    // 测试流量池套餐跳过逻辑
    LocationUpdateHContext context = createContext();
    context.getSurfingContext().getPackageCardRecord()
           .setPackageType(PackageType.FLOW_POOL.getType());

    adapter.postProcessBeforeSignatureWithHcard(context);

    // 验证: 流量池套餐不设置UpccContext
    assertNull(context.getSurfingContext().getUpccContext());
}

@Test
public void testPostProcessBeforeSignatureWithHcard_ReachRelease() {
    // 测试达量释放套餐逻辑
    LocationUpdateHContext context = createContext();
    ChannelPackageCard packageCard = context.getSurfingContext().getPackageCardRecord();
    packageCard.setSurfStatus(ChannelPackageCard.SurfStatusEnum.LIMIT.getValue());
    packageCard.setControlLogic(ChannelPackageCard.ControlLogicEnum.REACH_RELEASE.getValue());

    adapter.postProcessBeforeSignatureWithHcard(context);

    // 验证: 设置空签约ID和特殊速度值
    UpccContext upccContext = context.getSurfingContext().getUpccContext();
    assertEquals(StringPool.EMPTY, upccContext.getUpccSignBizId());
    assertEquals(Long.valueOf(-999999999L), upccContext.getUpccSpeed());
}
```

#### 10.1.2 V卡签约模板选择测试
```java
@Test
public void testVCardSignatureTemplate_LowSpeed() {
    // 测试低速V卡场景
    LocationUpdateHContext context = createVCardContext();
    UpccContext upccContext = new UpccContext();
    upccContext.setUpccSpeed(1000L);  // 低于阈值
    upccContext.setUpccSignBizId("vcard_template_id");
    context.getSurfingContext().setUpccContext(upccContext);

    String result = adapter.selectUpccSignBizId(context, true);

    assertEquals("vcard_template_id", result);
}

@Test
public void testVCardSignatureTemplate_HighSpeedWithHotspot() {
    // 测试高速V卡+支持热点场景
    LocationUpdateHContext context = createVCardContext();
    UpccContext upccContext = new UpccContext();
    upccContext.setUpccSpeed(10000L);  // 高于阈值
    upccContext.setSupportHotspots(CmsPackageCardUpccRelation.SupportHotSpot.YES.getValue());
    context.getSurfingContext().setUpccContext(upccContext);

    String result = adapter.selectUpccSignBizId(context, true);

    assertEquals(slowSpeedUpccId_y, result);
}
```

### 10.2 集成测试场景

#### 10.2.1 端到端V卡激活测试
```java
@Test
public void testVCardSurfingEndToEnd() {
    // 1. 准备测试数据
    LocationUpdateHContext context = createCompleteVCardContext();

    // 2. 执行V卡激活
    adapter.vCardSurfing(context);

    // 3. 验证结果
    verify(coreNetCaller).upccSignature(anyString(), eq(expectedSignBizId), anyString());
    verify(coreNetCaller).otaSending(any(InvokeOtaVO.class));
    assertNotNull(context.getVimsi());
    assertNotNull(context.getMsisdnToV());
}
```

---

**文档版本**: v1.0
**生成时间**: 2025-01-14
**变更范围**: H流程V卡上网激活UPCC签约模板选择逻辑
**核心方法**: postProcessBeforeSignatureWithHcard + invokeCoreNetWithHcard
**技术栈**: Spring Boot + MyBatis-Plus + Feign + 核心网交互
