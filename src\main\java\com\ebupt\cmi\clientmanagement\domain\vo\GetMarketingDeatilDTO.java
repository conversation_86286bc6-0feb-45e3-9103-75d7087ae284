package com.ebupt.cmi.clientmanagement.domain.vo;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
public class GetMarketingDeatilDTO {
    private String name;
    private Long activityId;
    private String beginTime;
    private String endTime;
    private String status;
    private BigDecimal rebateAmount;
    private BigDecimal usedAmount;
    private BigDecimal remainAmount;
    private String createTime;
    private String expireTime;
    private String effectiveTime;
}
