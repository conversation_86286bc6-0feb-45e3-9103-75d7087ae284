package com.ebupt.cmi.clientmanagement.service.lu.h.surfing;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.consumer.uitils.LuWarpper;
import com.ebupt.cmi.clientmanagement.domain.dto.ApnAndCountryDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.lu.PackageSurfingTypeDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelPackageCard;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelSurf;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelSurfDetail;
import com.ebupt.cmi.clientmanagement.domain.enums.LanguageEnum;
import com.ebupt.cmi.clientmanagement.domain.enums.lu.PackageStatusEnum;
import com.ebupt.cmi.clientmanagement.feign.oms.OmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.pms.PmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.CardLuDTO;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.HcardInfo;
import com.ebupt.cmi.clientmanagement.feign.sms.SmsFeignClient;
import com.ebupt.cmi.clientmanagement.mapper.*;
import com.ebupt.cmi.clientmanagement.service.ChannelSurfDetailService;
import com.ebupt.cmi.clientmanagement.service.FlowPoolService;
import com.ebupt.cmi.clientmanagement.service.PackageService;
import com.ebupt.cmi.clientmanagement.service.impl.SmsLuService;
import com.ebupt.cmi.clientmanagement.service.lu.context.h.LocationUpdateHContext;
import com.ebupt.cmi.clientmanagement.service.lu.context.h.SurfingContext;
import com.ebupt.cmi.clientmanagement.service.lu.context.h.surfing.ActivatingPackageSurfingContext;
import com.ebupt.cmi.clientmanagement.service.lu.corenet.CoreNetCaller;
import com.ebupt.cmi.clientmanagement.service.lu.strategy.V2LocationUpdateStrategy;
import com.ebupt.cmi.clientmanagement.service.mvno.context.ConfirmActivationContext;
import com.ebupt.cmi.clientmanagement.service.mvno.factory.ConfirmActivationFactory;
import com.ebupt.cmi.clientmanagement.service.mvno.strategy.ConfirmActivationStrategy;
import com.ebupt.cmi.clientmanagement.utils.BizConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @date 2021/6/3 15:24
 */
@Slf4j
@SuppressWarnings("all")
@Service
public class ActivatingPackageSurfing extends PackageMultiTypeSurfingAdapter {

    @Autowired
    private ChannelPackageCardMapper channelPackageCardMapper;
    @Autowired
    private PackageService packageService;
    @Autowired
    private OmsFeignClient omsFeignClient;

    @Value("#{${sms.placeholder}}")
    private Map<String, String> placeholder;

    public ActivatingPackageSurfing(ChannelCardMapper channelCardMapper, ChannelSurfMapper channelSurfMapper,
                                    ChannelSurfDetailMapper channelSurfDetailMapper, PmsFeignClient pmsFeignClient,
                                    SmsFeignClient smsFeignClient, CoreNetCaller coreNetCaller, SmsLuService smsLuService,
                                    OmsFeignClient omsFeignClient, CmsFlowpoolCardpoolRelationMapper flowpoolCardpoolRelationMapper,
                                    FlowPoolService flowPoolService, ChannelSurfDetailService channelSurfDetailService,
                                    V2LocationUpdateStrategy v2LocationUpdateStrategy, CmsPackageCardUpccRelationMapper cmsPackageCardUpccRelationMapper,
                                    RedisTemplate redisTemplate) {
        super(channelCardMapper, channelSurfMapper, channelSurfDetailMapper, flowpoolCardpoolRelationMapper,
                pmsFeignClient, smsFeignClient, omsFeignClient, coreNetCaller, smsLuService, flowPoolService, channelSurfDetailService,
                v2LocationUpdateStrategy, cmsPackageCardUpccRelationMapper, redisTemplate);
    }

    @Override
    protected PackageSurfingTypeDTO getSurfingTypeDTO(SurfingContext surfingContext) {
        return ((ActivatingPackageSurfingContext) surfingContext).getPackageToSurfing();
    }

    @Override
    public void postProcessBeforeSignatureWithHcard(LocationUpdateHContext context) {
        super.postProcessBeforeSignatureWithHcard(context);
    }

    @Override
    protected String getTagName() {
        return "激活中";
    }

    /**
     * h卡签约后置处理
     *
     * @param context
     */
    @Override
    public void postProcessAfterSignatureWithHcard(LocationUpdateHContext context) {
        log.debug("[H流程] [激活中套餐] H卡后置处理开始");
        // 1. 修改套餐与卡关系表
        log.debug("[H流程] [激活中套餐] [H卡后置处理] 修改套餐与卡关系表");
        ActivatingPackageSurfingContext surfingContext = (ActivatingPackageSurfingContext) context.getSurfingContext();
        ChannelPackageCard presentPackageCard = surfingContext.getPackageCardRecord();
        PackageSurfingTypeDTO packageToSurfing = surfingContext.getPackageToSurfing();
        // 计算过期时间
        Date expireTime;
        if (context.isFlowPool()) {
            expireTime = presentPackageCard.getEffectiveDay();
        } else {
            expireTime = packageService.calcPackageExpireTime(LocalDateTime.now(), presentPackageCard.getKeepPeriod(),
                    presentPackageCard.getPeriodUnit());
        }
        final Date now = new Date();
        final String activationMode = presentPackageCard.getActivationMode();
        final boolean activeExceed = ChannelPackageCard.ActivationModeEnum.AFTER_USING_EXCESS.getValue().equals(activationMode)
                || !StringUtils.isEmpty(presentPackageCard.getBillFlowLimit());
        ChannelPackageCard packageCardToSave = ChannelPackageCard.builder().build();
        if (!activeExceed) {
            log.debug("[H流程] [激活中套餐] [套餐不为达量激活] 更新套餐状态为已激活");
            packageCardToSave.setActiveTime(now);
            packageCardToSave.setExpireTime(expireTime);
            packageCardToSave.setPackageStatus(PackageStatusEnum.ACTIVATED.getStatus());
            channelPackageCardMapper.update(packageCardToSave, Wrappers.lambdaQuery(ChannelPackageCard.class)
                    .eq(ChannelPackageCard::getId, presentPackageCard.getId()));
            packageCardToSave.setPackageUniqueId(presentPackageCard.getPackageUniqueId());
            packageCardToSave.setPackageType(presentPackageCard.getPackageType());
            packageCardToSave.setCorpId(presentPackageCard.getCorpId());

        } else {
            log.debug("[H流程] [激活中套餐] [套餐为达量激活] 不更新套餐状态");
        }

        // 2. 记录上网信息表、上网明细表
        ChannelSurf channelSurf = channelSurfMapper.selectOne(Wrappers.<ChannelSurf>lambdaQuery()
                .eq(ChannelSurf::getPackageUniqueId, presentPackageCard.getPackageUniqueId())
                .eq(ChannelSurf::getImsi, context.getImsi())
                .eq(ChannelSurf::getMcc, context.getMcc()));
        // 2. 记录上网明细表
        if (channelSurf == null) {
            log.debug("[H流程] [激活中套餐] [H卡后置处理] 记录上网信息表");
            channelSurf = ChannelSurf.builder()
                    .startTime(now)
                    .endTime(expireTime)
                    .poolId(packageToSurfing.getCardPoolId())
                    .orderUniqueId(presentPackageCard.getOrderUniqueId())
                    .packageUniqueId(presentPackageCard.getPackageUniqueId())
                    .mcc(context.getMcc())
                    .corpId(context.getChannelAndCard().getCorpId())
                    .internetType(ChannelSurf.InternetTypeEnum.H.getValue())
                    .imsi(context.getImsi())
                    .madeImsi(context.getImsi())
                    .build();
            channelSurfMapper.insert(channelSurf);
        }
        log.debug("[H流程] [激活中套餐] [H卡后置处理] 记录上网明细表");
        channelSurfDetailMapper.insert(ChannelSurfDetail.builder()
                .startTime(now)
                .endTime(expireTime)
                .surfId(channelSurf.getId())
                .corpId(context.getChannelAndCard().getCorpId())
                .build());
        if (activeExceed) {
            log.debug("[H流程] [激活中套餐] [套餐为达量激活] 不下发激活短信");
            return;
        }
        // 3.下发激活前短信
        log.debug("[H流程] [激活中套餐] [H卡后置处理] 下发激活短信");
        CardLuDTO cardLuDTO = context.getCardLuDTO();
        HcardInfo hcardInfo = new HcardInfo();
        BeanUtils.copyProperties(cardLuDTO, hcardInfo);
        ConfirmActivationStrategy commonStrategy = ConfirmActivationFactory.getInvokeStrategy("commonStrategy");
        commonStrategy.confirmActivationProcess(ConfirmActivationContext.builder()
                .hcardInfo(hcardInfo)
                .mcc(context.getMcc())
                //这里如果是达量激活 packageCardToSave为空
                .packageCard(packageCardToSave == null ? presentPackageCard : packageCardToSave)
                .build());
        log.debug("[H流程] [激活中套餐] H卡后置处理结束");
    }

    /**
     * V卡激活、H卡签约后置处理
     *
     * @param context
     */
    @Override
    public void postProcessAfterSignatureWithHcard4V(LocationUpdateHContext context) {
        log.debug("[H流程] [激活中套餐.] [V卡激活、H卡签约后置处理] 开始");
        // 1. 查询上网信息表，没有就记录
        ActivatingPackageSurfingContext surfingContext = (ActivatingPackageSurfingContext) context.getSurfingContext();
        ChannelPackageCard packageCardRecord = surfingContext.getPackageCardRecord();
        PackageSurfingTypeDTO packageToSurfing = surfingContext.getPackageToSurfing();
        log.debug("[激活中套餐.] [V卡激活、H卡签约后置处理] 查询上网信息表数据，没有就插入一条");
        ChannelSurf channelSurf = channelSurfMapper.selectOne(Wrappers.<ChannelSurf>lambdaQuery()
                .eq(ChannelSurf::getPackageUniqueId, packageCardRecord.getPackageUniqueId())
                .eq(ChannelSurf::getImsi, context.getImsi())
                .eq(ChannelSurf::getMcc, context.getMcc()));
        // 2. 记录上网明细表
        if (channelSurf == null) {
            log.debug("[激活中套餐.] [V卡激活、H卡签约后置处理] 不存在上网信息表数据，插入一条");
            ChannelSurf surfToSave = ChannelSurf.builder()
                    .startTime(packageCardRecord.getActiveTime())
                    .endTime(packageCardRecord.getExpireTime())
                    .poolId(packageToSurfing.getCardPoolId())
                    .orderUniqueId(packageCardRecord.getOrderUniqueId())
                    .packageUniqueId(packageCardRecord.getPackageUniqueId())
                    .mcc(context.getMcc())
                    .corpId(context.getChannelAndCard().getCorpId())
                    .internetType(ChannelSurf.InternetTypeEnum.H.getValue())
                    .imsi(context.getImsi())
                    .madeImsi(context.getImsi())
                    .build();
            channelSurfMapper.insert(surfToSave);
            channelSurf = surfToSave;
        } else {
            log.debug("[激活中套餐.] [V卡激活、H卡签约后置处理] 存在上网信息表数据");
        }
        log.debug("[H流程] [激活中套餐.] [V卡激活、H卡签约后置处理] 记录上网明细表");
        channelSurfDetailMapper.insert(ChannelSurfDetail.builder()
                .surfId(channelSurf.getId())
                .startTime(packageCardRecord.getActiveTime())
                .endTime(packageCardRecord.getExpireTime())
                .corpId(context.getChannelAndCard().getCorpId())
                .build());
//        log.debug("[H流程] [激活中套餐.] [V卡激活、H卡签约后置处理] 下发cancel location");
//        coreNetCaller.sendCancelLocation(context.getImsi());
        log.debug("[H流程] [激活中套餐.] [V卡激活、H卡签约后置处理] 结束");
    }
}
